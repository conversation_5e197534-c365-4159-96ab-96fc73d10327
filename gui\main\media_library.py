#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
媒体库组件 - 完全按照原版复制所有功能
"""

import os
import cv2
from pathlib import Path
from typing import List, Optional, Dict, Any

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QScrollArea, QFrame, QFileDialog, QMessageBox, QApplication
)
from PySide6.QtCore import Qt, Signal, QSize, QMimeData, QPoint
from PySide6.QtGui import QPixmap, QFont, QDrag, QPainter, QColor, QImage
from ..styles.dialog_styles import DialogStyleManager, CustomDialog

class ModernButton(QPushButton):
    """现代化按钮"""
    
    def __init__(self, text="", icon_text="", color="#3E8BF0"):
        super().__init__()
        self.setText(f"{icon_text} {text}" if icon_text else text)
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """)
    
    def darken_color(self, color: str, factor: float = 0.9) -> str:
        """使颜色变暗"""
        if color.startswith('#'):
            color = color[1:]
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        r = int(r * factor)
        g = int(g * factor)
        b = int(b * factor)
        return f"#{r:02x}{g:02x}{b:02x}"

class MediaLibrary(QWidget):
    """素材库 - 仿剪映样式"""

    file_selected = Signal(str)
    show_media_info = Signal(str)  # 新信号：显示媒体信息
    template_edit_requested = Signal(dict)  # 🔧 新增：模板编辑请求信号
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.media_files = []
        self.thumbnail_cache = {}  # 缩略图缓存
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 顶部Tab栏容器 - 高度固定48px，宽度自适应
        tab_container = QWidget()
        tab_container.setFixedHeight(48)
        tab_container.setStyleSheet("""
            QWidget {
                background-color: #333333;
            }
        """)

        # Tab栏布局 - 🔧 修改：限制按钮在48px高度内
        tab_layout = QHBoxLayout(tab_container)
        tab_layout.setContentsMargins(24, 6, 24, 6)  # 上下各6px边距，确保按钮在48px内
        tab_layout.setSpacing(24)  # 按钮间距24px

        # 左侧分类标签 - 去掉调节分类
        categories = ["模板", "视频", "音频", "文本", "贴纸", "特效", "转场", "滤镜"]
        self.category_buttons = []  # 保存按钮引用
        self.current_category = "模板"  # 当前选中的分类

        for i, category in enumerate(categories):
            btn = QPushButton(category)
            btn.setCheckable(True)
            if i == 0:  # 默认选中模板
                btn.setChecked(True)

            # 🔧 修改：文本按钮样式，限制在48px高度内
            btn.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    color: #F2ECFF;
                    border: none;
                    padding: 8px 0px;
                    font-size: 14px;
                    font-weight: normal;
                    text-align: center;
                    max-height: 36px;
                }
                QPushButton:checked {
                    color: #00C896;
                    font-weight: bold;
                }
                QPushButton:hover {
                    color: #00C896;
                }
            """)

            # 连接信号处理
            btn.clicked.connect(lambda checked, cat=category: self.on_category_selected(cat))
            self.category_buttons.append(btn)
            tab_layout.addWidget(btn)

        tab_layout.addStretch()

        # 🔧 修改：删除导入素材按钮，功能移到各个tab内容中

        layout.addWidget(tab_container)
        
        # 素材网格区域 - 自适应尺寸
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: #4B4D52;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #3A3C41;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #666666;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #888888;
            }
            QScrollBar:horizontal {
                background-color: #3A3C41;
                height: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:horizontal {
                background-color: #666666;
                border-radius: 6px;
                min-width: 20px;
            }
            QScrollBar::handle:horizontal:hover {
                background-color: #888888;
            }
        """)

        # 网格容器
        self.grid_widget = QWidget()
        self.grid_widget.setStyleSheet("""
            QWidget {
                background-color: #4B4D52;
            }
        """)

        # 🔧 修改：使用5列网格布局，缩小间距适应更多卡片
        self.grid_layout = QGridLayout(self.grid_widget)
        self.grid_layout.setSpacing(10)  # 缩小间距从15到10
        self.grid_layout.setContentsMargins(15, 15, 15, 15)  # 缩小边距
        self.grid_layout.setColumnStretch(0, 1)
        self.grid_layout.setColumnStretch(1, 1)
        self.grid_layout.setColumnStretch(2, 1)
        self.grid_layout.setColumnStretch(3, 1)
        self.grid_layout.setColumnStretch(4, 1)  # 新增第5列

        # 确保内容从上往下排列，而不是居中
        self.grid_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # 设置网格的最小尺寸
        self.grid_widget.setMinimumSize(600, 600)

        scroll_area.setWidget(self.grid_widget)
        layout.addWidget(scroll_area)

        # 设置整个MediaLibrary的样式和最小尺寸
        self.setMinimumSize(400, 500)  # 最小尺寸，但可以缩放
        self.setStyleSheet("""
            MediaLibrary {
                background-color: #4B4D52;
            }
        """)
        
        # 🔧 修改：初始化空的模板数据，删除默认模板
        self.templates = []
        self.template_counter = 5  # 用于生成新模板ID

        # 初始化显示模板内容
        self.update_category_content()

    def create_action_button(self, text: str, color: str, callback):
        """创建操作按钮"""
        btn = QPushButton(text)
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 11px;
                min-width: 80px;
                max-width: 120px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """)
        btn.clicked.connect(callback)
        return btn

    def create_white_button(self, text: str, callback):
        """创建白色背景黑色字的按钮"""
        btn = QPushButton(text)
        btn.setStyleSheet("""
            QPushButton {
                background-color: #FFFFFF;
                color: #000000;
                border: none;
                border-radius: 6px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 11px;
                min-width: 80px;
                max-width: 120px;
            }
            QPushButton:hover {
                background-color: #E0E0E0;
            }
            QPushButton:pressed {
                background-color: #CCCCCC;
            }
        """)
        btn.clicked.connect(callback)
        return btn

    def darken_color(self, color: str, factor: float = 0.9) -> str:
        """使颜色变暗"""
        if color.startswith('#'):
            color = color[1:]
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        r = int(r * factor)
        g = int(g * factor)
        b = int(b * factor)
        return f"#{r:02x}{g:02x}{b:02x}"

    def create_empty_state_widget(self, title: str, description: str):
        """创建空状态显示组件"""
        widget = QWidget()
        widget.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 0.05);
                border: 2px dashed #666666;
                border-radius: 8px;
            }
        """)
        widget.setMinimumHeight(120)

        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: #F2ECFF;
                font-size: 16px;
                font-weight: bold;
                background-color: transparent;
                border: none;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                color: #CCCCCC;
                font-size: 12px;
                background-color: transparent;
                border: none;
            }
        """)
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        layout.addWidget(title_label)
        layout.addWidget(desc_label)

        return widget
    
    def on_category_selected(self, category: str):
        """处理分类选择"""
        print(f"选择分类: {category}")
        
        # 更新当前分类
        self.current_category = category
        
        # 更新按钮状态
        for btn in self.category_buttons:
            btn.setChecked(btn.text() == category)
        
        # 更新内容显示
        self.update_category_content()
    
    def update_category_content(self):
        """更新分类内容显示"""
        # 清空当前网格内容
        self.clear_grid()
        
        if self.current_category == "模板":
            self.show_template_content()
        elif self.current_category == "视频":
            self.show_video_content()
        elif self.current_category == "音频":
            self.show_audio_content()
        elif self.current_category == "文本":
            self.show_text_content()
        elif self.current_category == "贴纸":
            self.show_sticker_content()
        elif self.current_category == "特效":
            self.show_effect_content()
        elif self.current_category == "转场":
            self.show_transition_content()
        elif self.current_category == "滤镜":
            self.show_filter_content()
        elif self.current_category == "调节":
            self.show_adjustment_content()
    
    def clear_grid(self):
        """清空网格内容"""
        while self.grid_layout.count():
            child = self.grid_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
    
    def show_template_content(self):
        """显示模板内容"""
        # 🔧 修改：创建48px高度的按钮容器
        button_container = QWidget()
        button_container.setFixedHeight(48)  # 48px高度
        button_container.setStyleSheet("background-color: transparent;")

        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(12, 6, 12, 6)  # 上下6px边距，确保按钮在48px内
        button_layout.setSpacing(12)

        # 添加弹性空间，让按钮靠右
        button_layout.addStretch()

        # 新增模板按钮
        new_template_btn = self.create_white_button("新增模板", self.create_new_template)
        button_layout.addWidget(new_template_btn)

        # 将按钮容器添加到网格的第一行
        self.grid_layout.addWidget(button_container, 0, 0, 1, 4)  # 跨越4列

        # 🔧 修改：显示动态模板列表，从第二行开始
        for i, template in enumerate(self.templates):
            widget = self.create_template_item(template)
            row = (i // 4) + 1  # 从第二行开始
            col = i % 4
            self.grid_layout.addWidget(widget, row, col)

        # 如果没有模板，显示空状态
        if not self.templates:
            empty_widget = self.create_empty_state_widget("暂无模板", "点击上方新增按钮创建模板")
            self.grid_layout.addWidget(empty_widget, 1, 0, 1, 4)

    def show_video_content(self):
        """显示视频内容"""
        # 🔧 修改：去掉子tab，只保留导入按钮，容器高度48px
        # 创建按钮容器
        button_container = QWidget()
        button_container.setFixedHeight(48)  # 48px高度
        button_container.setStyleSheet("background-color: transparent;")

        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(12, 6, 12, 6)  # 上下6px边距，确保按钮在48px内
        button_layout.setSpacing(12)

        # 添加弹性空间，让按钮靠右
        button_layout.addStretch()

        # 🔧 修改：只保留导入视频按钮，去掉tab
        import_video_btn = self.create_white_button("导入视频", self.import_video)
        button_layout.addWidget(import_video_btn)

        # 将按钮容器添加到网格的第一行
        self.grid_layout.addWidget(button_container, 0, 0, 1, 4)  # 跨越4列

        # 显示已导入的视频文件
        video_files = [f for f in self.media_files if Path(f).suffix.lower() in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']]
        for i, file_path in enumerate(video_files):
            widget = self.create_media_item_widget(file_path)
            row = (i // 4) + 1  # 从第二行开始
            col = i % 4
            self.grid_layout.addWidget(widget, row, col)

        if not video_files:
            # 从第二行开始显示空状态
            empty_widget = self.create_empty_state_widget("暂无视频文件", "点击上方导入按钮添加视频文件")
            self.grid_layout.addWidget(empty_widget, 1, 0, 1, 4)

    def show_audio_content(self):
        """显示音频内容"""
        # 🔧 修改：创建48px高度的按钮容器
        button_container = QWidget()
        button_container.setFixedHeight(48)  # 48px高度
        button_container.setStyleSheet("background-color: transparent;")

        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(12, 6, 12, 6)  # 上下6px边距，确保按钮在48px内
        button_layout.setSpacing(12)

        # 添加弹性空间，让按钮靠右
        button_layout.addStretch()

        # 导入音频按钮
        import_audio_btn = self.create_white_button("导入音频", self.import_audio)
        button_layout.addWidget(import_audio_btn)

        # 将按钮容器添加到网格的第一行
        self.grid_layout.addWidget(button_container, 0, 0, 1, 4)  # 跨越4列

        audio_files = [f for f in self.media_files if Path(f).suffix.lower() in ['.mp3', '.wav', '.aac']]
        for i, file_path in enumerate(audio_files):
            widget = self.create_media_item_widget(file_path)
            row = (i // 4) + 1  # 从第二行开始
            col = i % 4
            self.grid_layout.addWidget(widget, row, col)

        if not audio_files:
            empty_widget = self.create_empty_state_widget("暂无音频文件", "点击上方导入按钮添加音频文件")
            self.grid_layout.addWidget(empty_widget, 1, 0, 1, 4)

    def show_text_content(self):
        """显示文本内容"""
        text_templates = [
            {"name": "标题文字", "icon": "📝", "description": "添加标题文字"},
            {"name": "字幕文字", "icon": "💬", "description": "添加字幕文字"},
            {"name": "片尾署名", "icon": "✍️", "description": "添加片尾署名"},
            {"name": "水印文字", "icon": "🏷️", "description": "添加水印文字"}
        ]

        for i, text in enumerate(text_templates):
            widget = self.create_template_item(text)
            row = i // 4
            col = i % 4
            self.grid_layout.addWidget(widget, row, col)

    def show_sticker_content(self):
        """显示贴纸内容"""
        stickers = [
            {"name": "表情贴纸", "icon": "😊", "description": "各种表情贴纸"},
            {"name": "装饰贴纸", "icon": "🎨", "description": "装饰性贴纸"},
            {"name": "节日贴纸", "icon": "🎉", "description": "节日主题贴纸"},
            {"name": "动态贴纸", "icon": "✨", "description": "动态效果贴纸"}
        ]

        for i, sticker in enumerate(stickers):
            widget = self.create_template_item(sticker)
            row = i // 4
            col = i % 4
            self.grid_layout.addWidget(widget, row, col)

    def show_effect_content(self):
        """显示特效内容"""
        effects = [
            {"name": "淡入淡出", "icon": "🌅", "description": "淡入淡出效果"},
            {"name": "闪光效果", "icon": "⚡", "description": "闪光特效"},
            {"name": "粒子效果", "icon": "✨", "description": "粒子动画效果"},
            {"name": "光晕效果", "icon": "💫", "description": "光晕特效"}
        ]

        for i, effect in enumerate(effects):
            widget = self.create_template_item(effect)
            row = i // 4
            col = i % 4
            self.grid_layout.addWidget(widget, row, col)

    def show_transition_content(self):
        """显示转场内容"""
        transitions = [
            {"name": "溶解转场", "icon": "🌊", "description": "平滑溶解转场"},
            {"name": "滑动转场", "icon": "📱", "description": "滑动切换转场"},
            {"name": "擦除转场", "icon": "🧽", "description": "擦除效果转场"},
            {"name": "翻页转场", "icon": "📖", "description": "翻页效果转场"}
        ]

        for i, transition in enumerate(transitions):
            widget = self.create_template_item(transition)
            row = i // 4
            col = i % 4
            self.grid_layout.addWidget(widget, row, col)

    def show_filter_content(self):
        """显示滤镜内容"""
        filters = [
            {"name": "复古滤镜", "icon": "📷", "description": "复古怀旧滤镜"},
            {"name": "黑白滤镜", "icon": "⚫", "description": "黑白艺术滤镜"},
            {"name": "暖色滤镜", "icon": "🔥", "description": "温暖色调滤镜"},
            {"name": "冷色滤镜", "icon": "❄️", "description": "冷色调滤镜"}
        ]

        for i, filter_item in enumerate(filters):
            widget = self.create_template_item(filter_item)
            row = i // 4
            col = i % 4
            self.grid_layout.addWidget(widget, row, col)

    def show_adjustment_content(self):
        """显示调节内容"""
        adjustments = [
            {"name": "亮度调节", "icon": "☀️", "description": "调节视频亮度"},
            {"name": "对比度调节", "icon": "🌗", "description": "调节视频对比度"},
            {"name": "饱和度调节", "icon": "🎨", "description": "调节色彩饱和度"},
            {"name": "色温调节", "icon": "🌡️", "description": "调节色温平衡"}
        ]

        for i, adjustment in enumerate(adjustments):
            widget = self.create_template_item(adjustment)
            row = i // 4
            col = i % 4
            self.grid_layout.addWidget(widget, row, col)

    def create_template_item(self, item_data: dict) -> QWidget:
        """创建模板项组件"""
        widget = QWidget()
        # 🔧 修改：缩小卡片尺寸，适合5列布局
        widget.setFixedSize(120, 110)  # 从160x140缩小到120x110
        widget.setStyleSheet("""
            QWidget {
                background-color: #2A2A2A;
                border-radius: 6px;
                border: 2px solid transparent;
            }
            QWidget:hover {
                background-color: #3A3A3A;
                border: 2px solid #FFFFFF;
            }
        """)

        layout = QVBoxLayout(widget)
        layout.setContentsMargins(6, 6, 6, 6)  # 缩小边距
        layout.setSpacing(3)  # 🔧 修改：缩小名字和图片的间隙，从5到3

        # 🔧 修改：图片/图标区域
        image_label = QLabel()
        image_label.setFixedSize(108, 65)  # 从140x80缩小到108x65
        image_label.setStyleSheet("""
            QLabel {
                background-color: #1A1A1A;
                border-radius: 4px;
                border: 1px solid #444444;
                font-size: 24px;
            }
        """)
        image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        image_label.setScaledContents(False)  # 不拉伸图片

        # 优先显示图片，否则显示图标
        image_path = item_data.get("image_path", "")
        if image_path and Path(image_path).exists():
            try:
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    # 按比例缩放，不拉伸
                    scaled_pixmap = pixmap.scaled(
                        108, 65,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    image_label.setPixmap(scaled_pixmap)
                else:
                    # 图片加载失败，显示图标
                    image_label.setText(item_data.get("icon", "📝"))
            except Exception as e:
                print(f"加载模板图片失败: {e}")
                image_label.setText(item_data.get("icon", "📝"))
        else:
            # 没有图片，显示图标
            image_label.setText(item_data.get("icon", "📝"))

        layout.addWidget(image_label)

        # 名称标签
        name_label = QLabel(item_data["name"])
        name_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 10px;
                background-color: transparent;
                border: none;
                font-weight: bold;
            }
        """)
        name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        name_label.setWordWrap(True)
        layout.addWidget(name_label)

        # 添加点击事件处理
        def on_template_click():
            self.handle_template_click(item_data)

        # 创建一个可点击的包装器
        class ClickableWidget(QWidget):
            def __init__(self):
                super().__init__()
                self.drag_start_position = None
                self.template_data = item_data  # 保存模板数据
                self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                self.customContextMenuRequested.connect(self.show_context_menu)

            def mousePressEvent(self, event):
                if event.button() == Qt.MouseButton.LeftButton:
                    self.drag_start_position = event.position().toPoint()
                    on_template_click()
                super().mousePressEvent(event)

            def show_context_menu(self, position):
                """显示右键菜单"""
                from PySide6.QtWidgets import QMenu
                menu = QMenu(self)
                menu.setStyleSheet("""
                    QMenu {
                        background-color: #1A1A1A;
                        color: #FFFFFF;
                        border: 1px solid #333333;
                        border-radius: 6px;
                    }
                    QMenu::item {
                        padding: 8px 16px;
                        border-radius: 4px;
                    }
                    QMenu::item:selected {
                        background-color: #00C896;
                        color: #FFFFFF;
                    }
                """)

                # 编辑模板
                edit_action = menu.addAction("编辑模板")
                edit_action.triggered.connect(lambda: self.edit_template())

                # 复制模板
                copy_action = menu.addAction("复制模板")
                copy_action.triggered.connect(lambda: self.copy_template())

                menu.addSeparator()

                # 删除模板
                delete_action = menu.addAction("删除模板")
                delete_action.triggered.connect(lambda: self.delete_template())

                menu.exec(self.mapToGlobal(position))

            def edit_template(self):
                """编辑模板"""
                try:
                    # 🔧 修改：发送模板编辑信号，让主窗口处理
                    media_library = self.parent().parent().parent().parent()
                    media_library.template_edit_requested.emit(self.template_data)
                    print(f"✅ 请求编辑模板: {self.template_data['name']}")
                except Exception as e:
                    print(f"❌ 编辑模板失败: {e}")

            def copy_template(self):
                """复制模板"""
                try:
                    # 创建模板副本
                    new_template = self.template_data.copy()
                    new_template["name"] = f"{self.template_data['name']}_副本"

                    # 生成新的ID
                    import time
                    new_template["id"] = f"{self.template_data.get('id', 'template')}_{int(time.time())}"

                    # 添加到模板列表
                    media_library = self.parent().parent().parent().parent()
                    media_library.templates.append(new_template)

                    # 刷新显示
                    media_library.update_category_content()
                    print(f"✅ 模板已复制: {new_template['name']}")
                except Exception as e:
                    print(f"❌ 复制模板失败: {e}")



            def delete_template(self):
                """删除模板"""
                try:
                    from PySide6.QtWidgets import QMessageBox
                    reply = QMessageBox.question(
                        self, "删除模板",
                        f"确定要删除模板 '{self.template_data['name']}' 吗？",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                        QMessageBox.StandardButton.No
                    )

                    if reply == QMessageBox.StandardButton.Yes:
                        # 从模板列表中删除
                        media_library = self.parent().parent().parent().parent()
                        if self.template_data in media_library.templates:
                            media_library.templates.remove(self.template_data)

                        # 刷新显示
                        media_library.update_category_content()
                        print(f"✅ 模板已删除: {self.template_data['name']}")
                except Exception as e:
                    print(f"❌ 删除模板失败: {e}")

            def mouseMoveEvent(self, event):
                if not (event.buttons() & Qt.MouseButton.LeftButton):
                    return

                if not self.drag_start_position:
                    return

                if ((event.position().toPoint() - self.drag_start_position).manhattanLength()
                    < QApplication.startDragDistance()):
                    return

                # 开始拖拽模板
                self.start_template_drag()

            def start_template_drag(self):
                """开始拖拽模板"""
                drag = QDrag(self)
                mime_data = QMimeData()

                # 设置拖拽数据 - 使用特殊的模板标识
                template_id = self.template_data.get("id", "")
                mime_data.setText(f"template:{template_id}")
                mime_data.setData("application/x-template", template_id.encode())
                drag.setMimeData(mime_data)

                # 创建拖拽图标
                pixmap = QPixmap(self.size())
                pixmap.fill(Qt.GlobalColor.transparent)
                painter = QPainter(pixmap)
                painter.setOpacity(0.7)
                self.render(painter, self.rect().topLeft())
                painter.end()

                drag.setPixmap(pixmap)
                drag.setHotSpot(self.rect().center())

                # 执行拖拽
                drop_action = drag.exec(Qt.DropAction.CopyAction)
                print(f"模板拖拽完成: {self.template_data['name']}, 动作: {drop_action}")

        # 将原widget的内容移到可点击的widget中
        clickable_widget = ClickableWidget()
        clickable_widget.setFixedSize(160, 140)
        clickable_widget.setStyleSheet(widget.styleSheet())
        clickable_widget.setLayout(layout)

        return clickable_widget

    def handle_template_click(self, template_data: dict):
        """处理模板点击事件"""
        template_id = template_data.get("id", "")
        template_name = template_data["name"]
        print(f"点击模板: {template_name} (ID: {template_id})")

        # 🔧 修改：通知主窗口在右侧显示模板编辑界面
        self.show_template_editor_in_right_panel(template_data)

    def show_template_editor_in_right_panel(self, template_data):
        """在右侧面板显示模板编辑器"""
        try:
            # 🔧 修改：发送信号给主窗口，显示模板编辑界面
            self.template_edit_requested.emit(template_data)
            print(f"请求编辑模板: {template_data['name']}")
        except Exception as e:
            print(f"显示模板编辑器失败: {e}")

        # 保持原有的特殊处理逻辑
        if template_data["name"] == "高端理发店标准模板":
            # 获取主窗口实例
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'template_manager'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'template_manager'):
                # 查找默认模板
                try:
                    templates = main_window.template_manager.list_templates()
                    if templates:
                        # 加载第一个模板
                        template_id = templates[0]['id']
                        main_window.load_template(template_id)
                    else:
                        QMessageBox.information(self, "提示", "暂无可用模板，请先创建模板")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"加载模板失败: {str(e)}")
        else:
            # 🔧 修复：使用正确的变量名
            print(f"其他模板点击处理: {template_data['name']}")

    def show_empty_state(self, title: str, message: str):
        """显示空状态"""
        widget = QWidget()
        widget.setFixedSize(800, 300)

        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 空状态图标
        icon_label = QLabel("📁")
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                color: #666666;
            }
        """)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(icon_label)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: #CCCCCC;
                font-size: 18px;
                font-weight: bold;
                margin: 10px 0;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 消息
        message_label = QLabel(message)
        message_label.setStyleSheet("""
            QLabel {
                color: #888888;
                font-size: 14px;
            }
        """)
        message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(message_label)

        self.grid_layout.addWidget(widget, 0, 0, 1, 4)  # 跨4列显示

    def import_media(self):
        """导入媒体文件"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "选择媒体文件",
            self.config.get('paths.last_import_dir', ''),
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv);;音频文件 (*.mp3 *.wav *.aac);;图片文件 (*.jpg *.png *.bmp);;所有文件 (*.*)"
        )

        if file_paths:
            if hasattr(self.config, 'set'):
                self.config.set('paths.last_import_dir', os.path.dirname(file_paths[0]))
                self.config.save_config()

            # 记录导入的文件类型
            imported_video = False
            imported_audio = False

            for file_path in file_paths:
                file_ext = Path(file_path).suffix.lower()
                if file_ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']:
                    imported_video = True
                elif file_ext in ['.mp3', '.wav', '.aac']:
                    imported_audio = True

                self.add_media_item(file_path)

            # 根据导入的文件类型，自动切换到对应的选项卡
            if imported_video and self.current_category != "视频":
                # 如果导入了视频文件且当前不在视频选项卡，切换到视频选项卡
                self.on_category_selected("视频")
            elif imported_audio and not imported_video and self.current_category != "音频":
                # 如果只导入了音频文件且当前不在音频选项卡，切换到音频选项卡
                self.on_category_selected("音频")
            else:
                # 如果当前选项卡正确，刷新显示
                self.update_category_content()

    def add_media_item(self, file_path: str):
        """添加媒体项到网格"""
        if file_path not in self.media_files:
            self.media_files.append(file_path)
            print(f"添加媒体文件到库: {Path(file_path).name}")

            # 注意：这里不直接添加到网格，而是通过 update_category_content 来刷新显示
            # 这样可以确保文件只在正确的选项卡中显示

    def create_media_item_widget(self, file_path: str) -> QWidget:
        """创建媒体项组件"""
        # 创建自定义Widget用于双击事件和拖拽
        class MediaItemWidget(QWidget):
            def __init__(self, parent_library, file_path):
                super().__init__()
                self.parent_library = parent_library
                self.file_path = file_path
                self.setAcceptDrops(False)

                # 启用拖拽
                self.setMouseTracking(True)
                self.drag_start_position = None

                # 🔧 新增：启用右键菜单
                self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                self.customContextMenuRequested.connect(self.show_context_menu)

            def mousePressEvent(self, event):
                if event.button() == Qt.MouseButton.LeftButton:
                    self.drag_start_position = event.position().toPoint()

                    # 🔧 修改：点击时只显示视频信息，不预览到视频窗口
                    self.parent_library.show_media_info.emit(self.file_path)

            def mouseMoveEvent(self, event):
                if not (event.buttons() & Qt.MouseButton.LeftButton):
                    return

                if not self.drag_start_position:
                    return

                if ((event.position().toPoint() - self.drag_start_position).manhattanLength()
                    < QApplication.startDragDistance()):
                    return

                # 开始拖拽
                self.start_drag()

            def start_drag(self):
                drag = QDrag(self)
                mime_data = QMimeData()

                # 设置拖拽数据
                mime_data.setText(self.file_path)
                mime_data.setData("application/x-media-file", self.file_path.encode())
                drag.setMimeData(mime_data)

                # 创建拖拽图标
                pixmap = QPixmap(self.size())
                pixmap.fill(Qt.GlobalColor.transparent)
                painter = QPainter(pixmap)
                painter.setOpacity(0.7)
                self.render(painter, self.rect().topLeft())
                painter.end()

                drag.setPixmap(pixmap)
                drag.setHotSpot(self.rect().center())

                # 执行拖拽
                drop_action = drag.exec(Qt.DropAction.CopyAction)

            def show_context_menu(self, position):
                """显示右键菜单"""
                from PySide6.QtWidgets import QMenu, QMessageBox
                menu = QMenu(self)
                menu.setStyleSheet("""
                    QMenu {
                        background-color: #1A1A1A;
                        color: #FFFFFF;
                        border: 1px solid #333333;
                        border-radius: 6px;
                    }
                    QMenu::item {
                        padding: 8px 16px;
                        border-radius: 4px;
                    }
                    QMenu::item:selected {
                        background-color: #00C896;
                        color: #FFFFFF;
                    }
                """)

                # 添加到轨道
                add_to_track_action = menu.addAction("添加到轨道")
                add_to_track_action.triggered.connect(lambda: self.add_to_track())

                # 在文件管理器中显示
                show_in_explorer_action = menu.addAction("在文件管理器中显示")
                show_in_explorer_action.triggered.connect(lambda: self.show_in_explorer())

                menu.addSeparator()

                # 复制文件
                copy_action = menu.addAction("复制文件")
                copy_action.triggered.connect(lambda: self.copy_file())

                # 重命名
                rename_action = menu.addAction("重命名")
                rename_action.triggered.connect(lambda: self.rename_file())

                menu.addSeparator()

                # 从库中移除
                remove_action = menu.addAction("从库中移除")
                remove_action.triggered.connect(lambda: self.remove_from_library())

                menu.exec(self.mapToGlobal(position))

            def add_to_track(self):
                """添加到轨道"""
                try:
                    # 🔧 修改：使用现有的拖拽逻辑，直接调用handle_track_drop
                    from pathlib import Path
                    import os

                    if not os.path.exists(self.file_path):
                        print(f"❌ 文件不存在: {self.file_path}")
                        return

                    # 获取时间轴组件
                    timeline = None
                    widget = self.parent_library
                    while widget and not hasattr(widget, 'timeline'):
                        widget = widget.parent()

                    if widget and hasattr(widget, 'timeline'):
                        timeline = widget.timeline

                        # 判断文件类型，选择合适的轨道
                        file_ext = Path(self.file_path).suffix.lower()
                        is_video = file_ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.m4v']
                        is_audio = file_ext in ['.mp3', '.wav', '.aac', '.flac', '.ogg', '.m4a']

                        # 找到合适的轨道widget
                        target_track_widget = None
                        if is_video:
                            # 找第一个视频轨道widget
                            for i, track in enumerate(timeline.tracks):
                                if track.get('type') == 'video':
                                    if i < len(timeline.track_widgets):
                                        target_track_widget = timeline.track_widgets[i]
                                        break
                        elif is_audio:
                            # 找第一个音频轨道widget
                            for i, track in enumerate(timeline.tracks):
                                if track.get('type') == 'audio':
                                    if i < len(timeline.track_widgets):
                                        target_track_widget = timeline.track_widgets[i]
                                        break

                        if target_track_widget:
                            # 🔧 关键：使用现有的handle_track_drop方法，这是拖拽的核心逻辑
                            timeline.handle_track_drop(target_track_widget, self.file_path, 0)  # drop_x=0表示添加到轨道开始位置
                            print(f"✅ 已添加到轨道: {Path(self.file_path).name}")
                        else:
                            print(f"❌ 未找到合适的轨道")
                    else:
                        print(f"❌ 未找到时间轴组件")

                except Exception as e:
                    print(f"❌ 添加到轨道失败: {e}")
                    import traceback
                    traceback.print_exc()

            def copy_file(self):
                """复制文件"""
                try:
                    import shutil
                    from pathlib import Path

                    source_path = Path(self.file_path)

                    # 生成副本文件名
                    stem = source_path.stem
                    suffix = source_path.suffix
                    parent = source_path.parent

                    # 找到一个不存在的文件名
                    counter = 1
                    while True:
                        new_name = f"{stem}_副本{counter if counter > 1 else ''}{suffix}"
                        new_path = parent / new_name
                        if not new_path.exists():
                            break
                        counter += 1

                    # 复制文件
                    shutil.copy2(source_path, new_path)

                    # 添加到媒体库
                    self.parent_library.media_files.append(str(new_path))

                    # 刷新显示
                    self.parent_library.update_category_content()

                    print(f"✅ 文件已复制: {new_name}")

                except Exception as e:
                    print(f"❌ 复制文件失败: {e}")
                    from PySide6.QtWidgets import QMessageBox
                    QMessageBox.critical(self, "错误", f"复制文件失败: {e}")

            def rename_file(self):
                """重命名文件"""
                try:
                    from PySide6.QtWidgets import QInputDialog, QMessageBox
                    from pathlib import Path

                    current_path = Path(self.file_path)
                    current_name = current_path.stem

                    # 输入新名称
                    new_name, ok = QInputDialog.getText(
                        self, "重命名文件",
                        "新文件名（不含扩展名）:",
                        text=current_name
                    )

                    if not ok or not new_name.strip():
                        return

                    new_name = new_name.strip()
                    if new_name == current_name:
                        return

                    # 构建新路径
                    new_path = current_path.parent / f"{new_name}{current_path.suffix}"

                    if new_path.exists():
                        QMessageBox.warning(self, "警告", "文件名已存在！")
                        return

                    # 重命名文件
                    current_path.rename(new_path)

                    # 🔧 修复：更新所有引用，包括媒体库和当前widget的file_path
                    old_path_str = str(current_path)
                    new_path_str = str(new_path)

                    # 更新媒体库中的路径
                    for i, media_file in enumerate(self.parent_library.media_files):
                        if isinstance(media_file, str) and media_file == old_path_str:
                            self.parent_library.media_files[i] = new_path_str
                            break
                        elif isinstance(media_file, dict) and media_file.get('file_path') == old_path_str:
                            media_file['file_path'] = new_path_str
                            break

                    # 🔧 关键：更新当前widget的file_path引用
                    self.file_path = new_path_str

                    # 🔧 新增：更新时间轴中所有引用此文件的媒体项
                    timeline = None
                    widget = self.parent_library
                    while widget and not hasattr(widget, 'timeline'):
                        widget = widget.parent()

                    if widget and hasattr(widget, 'timeline'):
                        timeline = widget.timeline
                        # 遍历所有轨道的所有媒体文件，更新路径
                        for track in timeline.tracks:
                            for media_item in track.get('media_files', []):
                                if isinstance(media_item, dict) and media_item.get('file_path') == old_path_str:
                                    media_item['file_path'] = new_path_str
                                    print(f"🔄 更新时间轴中的文件引用: {old_path_str} -> {new_path_str}")

                        # 刷新时间轴显示
                        if hasattr(timeline, 'update_display'):
                            timeline.update_display()

                    # 刷新媒体库显示
                    self.parent_library.update_category_content()

                    print(f"✅ 文件已重命名并更新所有引用: {current_name} -> {new_name}")

                except PermissionError:
                    QMessageBox.critical(self, "错误", "文件被占用，无法重命名！\n请关闭正在使用该文件的程序后重试。")
                    print(f"❌ 文件被占用，重命名失败: {Path(self.file_path).name}")
                except Exception as e:
                    print(f"❌ 重命名文件失败: {e}")
                    QMessageBox.critical(self, "错误", f"重命名失败: {e}")

            def show_in_explorer(self):
                """在文件管理器中显示"""
                try:
                    import subprocess
                    import platform
                    import os

                    file_path = os.path.abspath(self.file_path)

                    if platform.system() == "Windows":
                        # Windows: 使用explorer /select 打开并选中文件
                        subprocess.run(["explorer", "/select,", file_path], check=False)
                    elif platform.system() == "Darwin":  # macOS
                        # macOS: 使用open -R 打开并选中文件
                        subprocess.run(["open", "-R", file_path], check=False)
                    else:  # Linux
                        # Linux: 打开文件所在目录
                        directory = os.path.dirname(file_path)
                        subprocess.run(["xdg-open", directory], check=False)

                    print(f"✅ 已在文件管理器中显示: {Path(self.file_path).name}")
                except Exception as e:
                    print(f"❌ 打开文件管理器失败: {e}")



            def remove_from_library(self):
                """从库中移除"""
                try:
                    from PySide6.QtWidgets import QMessageBox
                    reply = QMessageBox.question(
                        self, "移除文件",
                        f"确定要从媒体库中移除 '{Path(self.file_path).name}' 吗？\n（文件本身不会被删除）",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                        QMessageBox.StandardButton.No
                    )

                    if reply == QMessageBox.StandardButton.Yes:
                        # 🔧 修改：从媒体文件列表中移除，支持多种格式
                        removed = False

                        # 尝试直接移除路径
                        if self.file_path in self.parent_library.media_files:
                            self.parent_library.media_files.remove(self.file_path)
                            removed = True

                        # 如果没找到，尝试移除字典格式的项
                        if not removed:
                            for i, media_item in enumerate(self.parent_library.media_files):
                                if isinstance(media_item, dict) and media_item.get('file_path') == self.file_path:
                                    self.parent_library.media_files.pop(i)
                                    removed = True
                                    break
                                elif isinstance(media_item, str) and media_item == self.file_path:
                                    self.parent_library.media_files.pop(i)
                                    removed = True
                                    break

                        if removed:
                            # 刷新显示
                            self.parent_library.update_category_content()
                            print(f"✅ 已从库中移除: {Path(self.file_path).name}")
                        else:
                            print(f"⚠️ 未找到要移除的文件: {Path(self.file_path).name}")

                except Exception as e:
                    print(f"❌ 移除文件失败: {e}")



        item_widget = MediaItemWidget(self, file_path)
        item_widget.setFixedSize(160, 140)
        item_widget.setStyleSheet("""
            QWidget {
                background-color: #1A1A1A;
                border-radius: 8px;
                border: 2px solid transparent;
            }
            QWidget:hover {
                background-color: #2A2A2A;
                border: 2px solid #FFFFFF;
            }
        """)

        layout = QVBoxLayout(item_widget)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(5)

        # 缩略图区域
        thumbnail_label = QLabel()
        thumbnail_label.setFixedSize(140, 80)
        thumbnail_label.setStyleSheet("""
            QLabel {
                background-color: #0A0A0A;
                border-radius: 4px;
                border: 1px solid #333333;
            }
        """)
        thumbnail_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # 🖼️ 修复：不拉伸图片，保持原始比例
        thumbnail_label.setScaledContents(False)
        thumbnail_label.setScaledContents(False)  # 不拉伸内容

        # 根据文件类型显示不同内容
        file_ext = Path(file_path).suffix.lower()
        if file_ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']:
            # 视频文件 - 获取真实缩略图
            pixmap = self.get_video_thumbnail(file_path)
            thumbnail_label.setPixmap(pixmap)
        elif file_ext in ['.mp3', '.wav', '.aac']:
            # 音频文件
            thumbnail_label.setText("🎵")
            thumbnail_label.setStyleSheet(thumbnail_label.styleSheet() + """
                QLabel {
                    font-size: 32px;
                    color: #FFB74D;
                }
            """)
        else:
            # 图片文件
            try:
                pixmap = QPixmap(file_path)
                if not pixmap.isNull():
                    thumbnail_label.setPixmap(pixmap.scaled(140, 80, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation))
                else:
                    thumbnail_label.setText("🖼")
                    thumbnail_label.setStyleSheet(thumbnail_label.styleSheet() + """
                        QLabel {
                            font-size: 32px;
                            color: #4CAF50;
                        }
                    """)
            except:
                thumbnail_label.setText("🖼")
                thumbnail_label.setStyleSheet(thumbnail_label.styleSheet() + """
                    QLabel {
                        font-size: 32px;
                        color: #4CAF50;
                    }
                """)

        layout.addWidget(thumbnail_label)

        # 文件名标签 - 使用更清晰的颜色
        file_name = Path(file_path).stem
        display_name = file_name[:20] + "..." if len(file_name) > 20 else file_name
        name_label = QLabel(display_name)
        name_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 12px;
                background-color: transparent;
                border: none;
                font-weight: bold;
            }
        """)
        name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        name_label.setWordWrap(True)
        layout.addWidget(name_label)

        return item_widget

    def get_video_thumbnail(self, video_path: str) -> QPixmap:
        """获取视频缩略图"""
        if video_path in self.thumbnail_cache:
            return self.thumbnail_cache[video_path]

        cap = None
        try:
            # 使用OpenCV获取视频第一帧
            cap = cv2.VideoCapture(video_path)
            ret, frame = cap.read()

            if ret:
                # 获取原始尺寸
                original_height, original_width = frame.shape[:2]

                # 计算保持宽高比的缩放
                target_width = 140
                target_height = 80

                # 计算缩放比例
                scale_x = target_width / original_width
                scale_y = target_height / original_height
                scale = min(scale_x, scale_y)

                # 计算新尺寸
                new_width = int(original_width * scale)
                new_height = int(original_height * scale)

                # 调整大小
                frame_resized = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
                frame_rgb = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2RGB)

                # 创建目标尺寸的QPixmap，黑色背景
                final_pixmap = QPixmap(target_width, target_height)
                final_pixmap.fill(QColor(0, 0, 0))

                # 将缩放后的图像居中绘制
                height, width, channel = frame_rgb.shape
                bytes_per_line = 3 * width
                q_image = QImage(frame_rgb.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)

                painter = QPainter(final_pixmap)
                x = (target_width - new_width) // 2
                y = (target_height - new_height) // 2
                painter.drawImage(x, y, q_image)
                painter.end()

                cap.release()

                # 缓存缩略图
                self.thumbnail_cache[video_path] = final_pixmap
                return final_pixmap
        except Exception as e:
            print(f"获取缩略图失败: {e}")
            if cap:
                cap.release()

        # 返回默认图标
        pixmap = QPixmap(140, 80)
        pixmap.fill(QColor(85, 85, 85))
        return pixmap

    def show_custom_message(self, title: str, message: str, icon_type: str = "information"):
        """显示自定义样式的消息框"""
        dialog = CustomDialog(self, title)
        dialog.setFixedSize(400, 200)

        # 创建消息内容
        message_label = QLabel(message)
        message_label.setWordWrap(True)
        message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        message_label.setStyleSheet(f"""
            QLabel {{
                color: {DialogStyleManager.TEXT_COLOR};
                font-size: 14px;
                padding: 20px;
            }}
        """)

        dialog.content_layout.addWidget(message_label)

        # 添加确定按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        ok_button = QPushButton("确定")
        ok_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #00C896;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 24px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #00E8A8;
            }}
        """)
        ok_button.clicked.connect(dialog.accept)
        button_layout.addWidget(ok_button)

        dialog.content_layout.addLayout(button_layout)

        dialog.exec()

    # 🔧 新增：各种导入和创建方法
    def create_new_template(self):
        """创建新模板"""
        try:
            # 🔧 修改：在右侧面板显示新模板编辑界面
            new_template_data = {
                "id": f"template_{self.template_counter}",
                "name": "新模板",
                "icon": "📝",
                "description": "新建的模板",
                "settings": {},
                "is_new": True  # 标记为新模板
            }

            # 通知主窗口在右侧显示新模板编辑界面
            self.show_template_editor_in_right_panel(new_template_data)

        except Exception as e:
            print(f"创建新模板失败: {e}")

    def add_template(self, template_data):
        """添加新模板到列表"""
        try:
            # 生成新的ID
            template_data["id"] = f"template_{self.template_counter}"
            self.template_counter += 1

            # 添加到模板列表
            self.templates.append(template_data)

            # 刷新显示
            if self.current_category == "模板":
                self.update_category_content()

            print(f"成功添加模板: {template_data['name']}")

        except Exception as e:
            print(f"添加模板失败: {e}")

    def update_template(self, template_id, updated_data):
        """更新现有模板"""
        try:
            for i, template in enumerate(self.templates):
                if template["id"] == template_id:
                    # 更新模板数据
                    self.templates[i].update(updated_data)

                    # 刷新显示
                    if self.current_category == "模板":
                        self.update_category_content()

                    print(f"成功更新模板: {updated_data.get('name', template_id)}")
                    return True

            print(f"未找到模板: {template_id}")
            return False

        except Exception as e:
            print(f"更新模板失败: {e}")
            return False

    def delete_template(self, template_id):
        """删除模板"""
        try:
            for i, template in enumerate(self.templates):
                if template["id"] == template_id:
                    deleted_template = self.templates.pop(i)

                    # 刷新显示
                    if self.current_category == "模板":
                        self.update_category_content()

                    print(f"成功删除模板: {deleted_template['name']}")
                    return True

            print(f"未找到要删除的模板: {template_id}")
            return False

        except Exception as e:
            print(f"删除模板失败: {e}")
            return False

    def import_template(self):
        """导入模板"""
        try:
            # 创建文件对话框并应用样式
            dialog = QFileDialog(self, "导入模板", "", "模板文件 (*.json *.xml);;所有文件 (*)")
            dialog.setStyleSheet(DialogStyleManager.get_dialog_style())

            if dialog.exec():
                file_paths = dialog.selectedFiles()
                if file_paths:
                    file_path = file_paths[0]
                    self.show_custom_message("导入模板", f"模板导入功能开发中...\n选择的文件: {file_path}")
        except Exception as e:
            print(f"导入模板失败: {e}")

    def import_video(self):
        """导入视频"""
        try:
            # 创建文件对话框并应用样式
            dialog = QFileDialog(self, "导入视频", "", "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v);;所有文件 (*)")
            dialog.setFileMode(QFileDialog.FileMode.ExistingFiles)
            dialog.setStyleSheet(DialogStyleManager.get_dialog_style())

            if dialog.exec():
                file_paths = dialog.selectedFiles()
                if file_paths:
                    for file_path in file_paths:
                        if file_path not in self.media_files:
                            self.media_files.append(file_path)
                            print(f"添加视频文件到库: {Path(file_path).name}")

                    # 刷新显示
                    if self.current_category == "视频":
                        self.update_category_content()

                    # 🔇 移除导入成功提示弹窗
                    print(f"✅ 成功导入 {len(file_paths)} 个视频文件")
        except Exception as e:
            print(f"导入视频失败: {e}")
            # 显示错误消息
            self.show_custom_message("导入失败", f"导入视频失败: {e}")

    def import_audio(self):
        """导入音频"""
        try:
            # 创建文件对话框并应用样式
            dialog = QFileDialog(self, "导入音频", "", "音频文件 (*.mp3 *.wav *.aac *.flac *.ogg *.m4a);;所有文件 (*)")
            dialog.setFileMode(QFileDialog.FileMode.ExistingFiles)
            dialog.setStyleSheet(DialogStyleManager.get_dialog_style())

            if dialog.exec():
                file_paths = dialog.selectedFiles()
                if file_paths:
                    for file_path in file_paths:
                        if file_path not in self.media_files:
                            self.media_files.append(file_path)
                            print(f"添加音频文件到库: {Path(file_path).name}")

                    # 刷新显示
                    if self.current_category == "音频":
                        self.update_category_content()

                    # 🔇 移除导入成功提示弹窗
                    print(f"✅ 成功导入 {len(file_paths)} 个音频文件")
        except Exception as e:
            print(f"导入音频失败: {e}")
            # 显示错误消息
            self.show_custom_message("导入失败", f"导入音频失败: {e}")
