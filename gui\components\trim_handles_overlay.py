#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PySide6.QtWidgets import QWidget
from PySide6.QtCore import Qt, QPoint, QRect
from PySide6.QtGui import QPainter, QColor, QPen, QBrush, QPolygon

class TrimHandlesOverlay(QWidget):
    """专门的游标绘制层，覆盖整个轨道，支持往外拖扩展"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        print(f"🔧 TrimHandlesOverlay 初始化: parent={parent}")
        
        # 设置透明背景，不阻挡鼠标事件
        self.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, True)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        
        # 确保始终在最顶层
        self.raise_()
        # 设置最高层级显示
        self.setAttribute(Qt.WidgetAttribute.WA_AlwaysStackOnTop, True)
        
        # 防止重复绘制的标志
        self._is_painting = False
        
        print(f"🔧 TrimHandlesOverlay 属性设置完成")
        print(f"   - WA_TransparentForMouseEvents: {self.testAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents)}")
        print(f"   - WA_NoSystemBackground: {self.testAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)}")
        print(f"   - WA_TranslucentBackground: {self.testAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)}")
        print(f"   - WA_AlwaysStackOnTop: {self.testAttribute(Qt.WidgetAttribute.WA_AlwaysStackOnTop)}")
        
    def paintEvent(self, event):
        """绘制所有素材块的游标 - 最后绘制确保层级最高"""
        # 防止重复绘制
        if self._is_painting:
            return
        self._is_painting = True

        painter = None
        try:
            # 🔧 关键修复：先让所有子组件完成绘制
            super().paintEvent(event)

            # 检查父组件是否存在且有媒体块
            parent = self.parent()
            if not parent:
                return

            # 避免循环导入，使用类名字符串查找
            blocks = []
            for child in parent.children():
                if child.__class__.__name__ == 'VideoThumbnailBlock':
                    blocks.append(child)

            # 如果没有媒体块，不需要绘制游标
            if not blocks:
                return

            # 然后绘制游标，确保在最顶层
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # 使用最高级别的合成模式，确保游标不被遮挡
            painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceOver)

            # 绘制所有VideoThumbnailBlock的裁剪游标和扩展区
            for block in blocks:
                self.draw_trim_handles_for_block(painter, block)

        except Exception as e:
            print(f"❌ TrimHandlesOverlay paintEvent 错误: {e}")
        finally:
            # 🔧 修复：确保 QPainter 正确结束
            if painter and painter.isActive():
                painter.end()
            self._is_painting = False
    
    def draw_trim_handles_for_block(self, painter, block):
        """为指定的媒体块绘制裁剪游标"""
        try:
            if not block.isVisible():
                return
            
            # 获取媒体块的几何信息
            block_rect = block.geometry()
            block_x = block_rect.x()
            block_y = block_rect.y()
            block_width = block_rect.width()
            block_height = block_rect.height()
            
            # 获取裁剪状态
            if block.left_trim_dragging or block.right_trim_dragging:
                # 使用预览状态
                left_trim = getattr(block, 'preview_left_trim_pos', 0)
                right_trim = getattr(block, 'preview_right_trim_pos', 0)
            else:
                # 使用实际状态
                left_trim = getattr(block, 'left_trim_pos', 0)
                right_trim = getattr(block, 'right_trim_pos', 0)
            
            # 绘制左游标
            if True:  # 始终显示游标
                left_x = block_x + left_trim
                self.draw_trim_handle(painter, left_x, block_y, block_height, 'left', 
                                    block.left_trim_dragging)
            
            # 绘制右游标
            if True:  # 始终显示游标
                right_x = block_x + block_width - right_trim
                self.draw_trim_handle(painter, right_x, block_y, block_height, 'right', 
                                    block.right_trim_dragging)
                
        except Exception as e:
            print(f"绘制媒体块游标失败: {e}")
    
    def draw_trim_handle(self, painter, x, y, height, side, is_dragging):
        """绘制单个裁剪游标 - 现代化样式"""
        try:
            # 🎨 改变游标样式：使用圆角矩形代替白色箭头
            if is_dragging:
                color = QColor(0, 200, 150, 255)  # 拖拽时的高亮色
                border_color = QColor(255, 255, 255, 255)
            else:
                color = QColor(255, 165, 0, 200)  # 橙色，更现代
                border_color = QColor(255, 255, 255, 150)
            
            # 🎨 现代化游标样式：圆角矩形手柄
            handle_width = 6
            handle_height = 20

            # 绘制游标主体（垂直线）
            painter.setPen(QPen(color, 2))
            painter.drawLine(int(x), y, int(x), y + height)

            # 绘制顶部圆角矩形手柄
            top_handle_rect = QRect(int(x - handle_width//2), y, handle_width, handle_height)
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(border_color, 1))
            painter.drawRoundedRect(top_handle_rect, 3, 3)  # 圆角半径3px

            # 绘制底部圆角矩形手柄
            bottom_handle_rect = QRect(int(x - handle_width//2), y + height - handle_height, handle_width, handle_height)
            painter.drawRoundedRect(bottom_handle_rect, 3, 3)  # 圆角半径3px

            # 在手柄中间绘制小的抓取线条
            painter.setPen(QPen(border_color, 1))
            for i in range(3):  # 绘制3条线
                line_y_top = y + 6 + i * 3
                line_y_bottom = y + height - 18 + i * 3
                painter.drawLine(int(x - 2), line_y_top, int(x + 2), line_y_top)
                painter.drawLine(int(x - 2), line_y_bottom, int(x + 2), line_y_bottom)

            
        except Exception as e:
            print(f"绘制游标失败: {e}")
    
    def update_trim_handles(self):
        """更新游标显示"""
        self.update()
        
    def resizeEvent(self, event):
        """调整大小事件"""
        super().resizeEvent(event)
        # 确保覆盖整个父组件
        if self.parent():
            self.setGeometry(self.parent().rect())
