#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import numpy as np
from pathlib import Path
from PySide6.QtWidgets import QWidget
from PySide6.QtCore import Qt, QPoint, QRect
from PySide6.QtGui import QPainter, QColor, QPen, QBrush, QPixmap, QFont, QLinearGradient

class AudioWaveformBlock(QWidget):
    """显示音频波形的媒体块"""

    def __init__(self, media_item, track_index, media_index, timeline):
        super().__init__()
        self.media_item = media_item
        self.track_index = track_index
        self.media_index = media_index
        self.timeline = timeline
        self.dragging = False
        self.drag_start_pos = None
        self.original_pos = None

        # 设置基本属性
        self.setMinimumHeight(84)

        # 波形数据缓存
        self._waveform_data = None
        self._waveform_pixmap = None

        # 生成波形图
        self._generate_waveform()

        # 裁剪相关（音频也支持裁剪）
        self.trim_handle_width = 8
        self.left_trim_dragging = False
        self.right_trim_dragging = False
        self.trim_drag_start_pos = None

        # 预览状态（拖动时显示，不修改实际数据）
        self.preview_left_trim_pos = 0
        self.preview_right_trim_pos = 0

        # 实际裁剪状态（松开鼠标时应用）
        self.left_trim_pos = 0
        self.right_trim_pos = 0

        # 裁剪历史记录
        self.trim_history = []
        self.current_trim_index = -1

        # 设置鼠标跟踪
        self.setMouseTracking(True)

        # 裁剪历史记录（支持往外拖恢复）
        self.trim_history = []
        self.current_trim_index = -1

        # 启用鼠标追踪，用于更新光标
        self.setMouseTracking(True)

    def get_trim_state(self):
        """暴露裁剪状态给轨道层级使用"""
        return {
            "left_trim": self.preview_left_trim_pos if self.left_trim_dragging else self.left_trim_pos,
            "right_trim": self.preview_right_trim_pos if self.right_trim_dragging else self.right_trim_pos,
            "left_trim_dragging": self.left_trim_dragging,
            "right_trim_dragging": self.right_trim_dragging,
            "geometry": self.geometry(),
            "height": self.height()
        }

    def paintEvent(self, event):
        """绘制音频波形或占位符"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        rect = self.rect()

        # 🎨 改善音频块背景色 - 使用渐变效果
        gradient = QLinearGradient(0, 0, 0, rect.height())
        gradient.setColorAt(0, QColor(65, 105, 225, 180))  # 蓝色渐变顶部
        gradient.setColorAt(1, QColor(25, 25, 112, 180))   # 深蓝色渐变底部
        painter.fillRect(rect, QBrush(gradient))

        # 绘制波形（如果有的话）
        if self._waveform_pixmap and not self._waveform_pixmap.isNull():
            painter.drawPixmap(rect, self._waveform_pixmap)
        else:
            # 🎨 改善音频图标显示
            painter.setPen(QColor(255, 255, 255, 200))
            font = QFont("Arial", 20, QFont.Weight.Bold)
            painter.setFont(font)

            # 绘制多个音符图标，营造音频感
            icon_y = rect.height() // 2
            for i, icon in enumerate(['♪', '♫', '♪']):
                x_pos = rect.width() // 4 * (i + 1) - 10
                painter.drawText(x_pos, icon_y, icon)

        # 绘制踩点标记
        self.draw_beat_markers(painter, rect)

        # 绘制拖拽手柄（在右下角，不遮挡波形）
        self.draw_drag_handle(painter, rect)

        # 绘制边框
        painter.setPen(QPen(QColor(119, 119, 119), 1))
        painter.drawRect(rect.adjusted(0, 0, -1, -1))

        # 绘制文本信息
        if isinstance(self.media_item, dict):
            name = self.media_item.get('name', 'Unknown')
            duration = self.media_item.get('duration', 0)
        else:
            name = 'Audio'
            duration = 0

        # 绘制半透明背景
        overlay_rect = QRect(2, rect.height() - 20, rect.width() - 4, 18)
        painter.fillRect(overlay_rect, QColor(0, 0, 0, 160))

        # 绘制文本
        painter.setPen(QColor(255, 255, 255))
        font = QFont("Arial", 9, QFont.Weight.Bold)
        painter.setFont(font)

        text = f"{name[:10]}... {duration:.1f}s" if len(name) > 10 else f"{name} {duration:.1f}s"
        painter.drawText(overlay_rect, Qt.AlignmentFlag.AlignCenter, text)

        # 绘制裁剪预览遮罩
        self.draw_trim_preview(painter, rect)

        # 绘制裁剪手柄
        self.draw_trim_handles(painter, rect)

        # 绘制裁剪状态指示器
        self.draw_trim_indicator(painter, rect)

        # 结束绘制
        painter.end()

    def _generate_waveform(self):
        """生成音频波形图"""
        if not isinstance(self.media_item, dict):
            return

        try:
            file_path = self.media_item['file_path']

            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"Audio file not found: {file_path}")
                self._generate_placeholder_waveform()
                return

            # 使用专门的音频工具生成波形
            try:
                from core.audio_utils import AudioUtils
                from core.config import Config

                config = Config() if hasattr(self, 'config') else None
                audio_utils = AudioUtils(config)

                # 生成波形数据
                self._waveform_data = audio_utils.generate_waveform_data(file_path, 150)

                if self._waveform_data:
                    print(f"Generated waveform using AudioUtils: {len(self._waveform_data)} points")
                    self._create_waveform_pixmap()
                else:
                    self._generate_placeholder_waveform()

            except Exception as e:
                print(f"AudioUtils failed: {e}, trying fallback methods...")

                # 备选方案1: 使用 librosa
                waveform_generated = False
                try:
                    import librosa
                    import numpy as np

                    # 加载音频文件，降采样以提高性能
                    y, sr = librosa.load(file_path, sr=22050, duration=30)  # 只加载前30秒

                    # 计算每个时间窗口的RMS能量
                    hop_length = 512
                    frame_length = 2048
                    rms = librosa.feature.rms(y=y, frame_length=frame_length, hop_length=hop_length)[0]

                    # 归一化
                    if len(rms) > 0:
                        max_val = np.max(rms)
                        if max_val > 0:
                            rms = rms / max_val

                    self._waveform_data = rms.tolist()
                    waveform_generated = True
                    print(f"Generated waveform using librosa: {len(self._waveform_data)} points")

                except ImportError:
                    print("librosa not available, trying pydub...")
                except Exception as e:
                    print(f"librosa failed: {e}, trying pydub...")

                # 备选方案2: 使用 pydub
                if not waveform_generated:
                    try:
                        from pydub import AudioSegment
                        from pydub.utils import make_chunks
                        import numpy as np

                        # 加载音频文件
                        audio = AudioSegment.from_file(file_path)

                        # 限制长度以提高性能
                        if len(audio) > 30000:  # 30秒
                            audio = audio[:30000]

                        # 将音频分成多个片段
                        chunk_length_ms = 200  # 每200ms一个片段
                        chunks = make_chunks(audio, chunk_length_ms)

                        waveform_data = []
                        for chunk in chunks:
                            # 计算RMS值
                            samples = np.array(chunk.get_array_of_samples())
                            if len(samples) > 0:
                                rms = np.sqrt(np.mean(samples**2))
                                waveform_data.append(rms)

                        # 归一化波形数据
                        if waveform_data:
                            max_val = max(waveform_data)
                            if max_val > 0:
                                waveform_data = [val / max_val for val in waveform_data]

                        self._waveform_data = waveform_data
                        waveform_generated = True
                        print(f"Generated waveform using pydub: {len(self._waveform_data)} points")

                    except ImportError:
                        print("pydub not available, using placeholder waveform")
                    except Exception as e:
                        print(f"pydub failed: {e}, using placeholder waveform")

                # 如果所有方法都失败，使用占位符
                if not waveform_generated:
                    self._generate_placeholder_waveform()
                else:
                    # 生成波形图
                    self._create_waveform_pixmap()

        except Exception as e:
            print(f"Error generating waveform: {e}")
            self._generate_placeholder_waveform()

    def _generate_placeholder_waveform(self):
        """生成占位符波形"""
        import random
        import math

        # 使用文件路径作为种子，确保相同文件的波形一致
        random.seed(hash(self.media_item.get('file_path', '')) % 1000)

        waveform_data = []
        num_points = 150  # 增加点数使波形更平滑

        for i in range(num_points):
            # 创建更真实的音频波形模式
            t = i / num_points

            # 基础正弦波
            base_wave = 0.3 * math.sin(2 * math.pi * t * 3)

            # 添加高频成分
            high_freq = 0.2 * math.sin(2 * math.pi * t * 12)

            # 添加随机噪声
            noise = 0.15 * (random.random() - 0.5)

            # 添加包络（音量变化）
            envelope = 0.5 + 0.3 * math.sin(2 * math.pi * t * 0.5)

            # 组合所有成分
            amplitude = abs((base_wave + high_freq + noise) * envelope)

            # 确保在合理范围内
            amplitude = max(0.05, min(1.0, amplitude))
            waveform_data.append(amplitude)

        self._waveform_data = waveform_data
        self._create_waveform_pixmap()
        print(f"Generated placeholder waveform: {len(waveform_data)} points")

    def _create_waveform_pixmap(self):
        """创建增强的波形图 pixmap"""
        if not self._waveform_data:
            return

        # 创建 pixmap
        width = self.width()
        height = self.height()
        if width <= 0 or height <= 0:
            return

        pixmap = QPixmap(width, height)
        pixmap.fill(QColor(35, 35, 35))  # 更深的背景色

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 绘制增强的波形
        if self._waveform_data:
            data_length = len(self._waveform_data)

            # 音频波形颜色 - 现代化渐变
            center_y = height // 2
            margin = 8  # 减少边距
            max_bar_height = center_y - margin

            # 计算每个样本的宽度，确保不拉伸
            if data_length > 0:
                sample_width = max(1.0, width / data_length)  # 最小宽度1像素
            else:
                sample_width = 1.0

            # 根据音频类型设置不同的颜色
            if self.media_item.get('auto_sync_audio', False):
                # 自动踩点音频 - 紫色到蓝色渐变主题
                primary_color = QColor(120, 80, 255)      # 深紫色
                secondary_color = QColor(180, 120, 255)   # 浅紫色
                accent_color = QColor(80, 150, 255)       # 蓝色强调
            else:
                # 普通音频 - 蓝绿色主题
                primary_color = QColor(80, 200, 180)      # 深蓝绿
                secondary_color = QColor(120, 220, 200)   # 浅蓝绿
                accent_color = QColor(60, 180, 160)       # 强调色

            # 绘制波形条
            for i, amplitude in enumerate(self._waveform_data):
                x = int(i * sample_width)
                bar_height = int(amplitude * max_bar_height)

                if bar_height > 0:
                    bar_width = max(1, int(sample_width))

                    # 根据音量大小选择颜色和效果
                    if amplitude > 0.8:
                        # 极高音量 - 使用强调色
                        gradient = QLinearGradient(x, center_y - bar_height, x, center_y + bar_height)
                        gradient.setColorAt(0, accent_color.lighter(140))
                        gradient.setColorAt(0.3, secondary_color.lighter(120))
                        gradient.setColorAt(0.7, primary_color)
                        gradient.setColorAt(1, accent_color.lighter(140))

                        # 添加外发光效果
                        painter.setBrush(QColor(accent_color.red(), accent_color.green(), accent_color.blue(), 50))
                        painter.setPen(Qt.PenStyle.NoPen)
                        painter.drawRect(x - 1, center_y - bar_height - 1, bar_width + 2, bar_height * 2 + 2)

                    elif amplitude > 0.6:
                        # 高音量 - 亮色
                        gradient = QLinearGradient(x, center_y - bar_height, x, center_y + bar_height)
                        gradient.setColorAt(0, secondary_color.lighter(130))
                        gradient.setColorAt(0.5, primary_color.lighter(110))
                        gradient.setColorAt(1, secondary_color.lighter(130))

                    elif amplitude > 0.3:
                        # 中音量 - 标准颜色
                        gradient = QLinearGradient(x, center_y - bar_height, x, center_y + bar_height)
                        gradient.setColorAt(0, secondary_color)
                        gradient.setColorAt(0.5, primary_color)
                        gradient.setColorAt(1, secondary_color)

                    else:
                        # 低音量 - 暗色
                        gradient = QLinearGradient(x, center_y - bar_height, x, center_y + bar_height)
                        gradient.setColorAt(0, primary_color.darker(140))
                        gradient.setColorAt(0.5, primary_color.darker(120))
                        gradient.setColorAt(1, primary_color.darker(140))

                    # 绘制主波形条
                    painter.setBrush(gradient)
                    painter.setPen(Qt.PenStyle.NoPen)
                    painter.drawRect(x, center_y - bar_height, bar_width, bar_height * 2)

                    # 添加顶部高光
                    if amplitude > 0.5:
                        highlight_height = max(1, int(bar_height * 0.2))
                        painter.setBrush(QColor(255, 255, 255, int(80 * amplitude)))
                        painter.drawRect(x, center_y - bar_height, bar_width, highlight_height)

                    # 添加边缘细线增强立体感
                    if bar_width > 2:
                        painter.setPen(QPen(primary_color.darker(150), 1))
                        painter.drawLine(x, center_y - bar_height, x, center_y + bar_height)
                        painter.drawLine(x + bar_width - 1, center_y - bar_height, x + bar_width - 1, center_y + bar_height)

            # 添加中心线
            painter.setPen(QPen(QColor(255, 255, 255, 80), 1))
            painter.drawLine(0, center_y, width, center_y)

            # 添加边框
            painter.setPen(QPen(QColor(255, 255, 255, 40), 1))
            painter.drawRect(0, margin, width - 1, height - margin * 2)

        painter.end()
        self._waveform_pixmap = pixmap

    def draw_beat_markers(self, painter, rect):
        """绘制踩点标记"""
        if not isinstance(self.media_item, dict):
            return

        # 检查是否是自动踩点音频
        if not self.media_item.get('auto_sync_audio', False):
            return

        # 获取踩点信息
        beat_points = self.media_item.get('beat_points', [])
        if not beat_points:
            # 如果没有踩点数据，生成一些测试数据用于演示
            duration = self.media_item.get('duration', 30.0)
            beat_points = [i * 2.0 for i in range(int(duration // 2))]  # 每2秒一个踩点

        duration = self.media_item.get('duration', 30.0)
        width = rect.width()
        height = rect.height()

        # 绘制踩点标记
        for i, beat_time in enumerate(beat_points):
            if 0 <= beat_time <= duration and width > 0:
                # 计算标记位置
                x = int((beat_time / duration) * width)

                # 确保x在有效范围内
                if 0 <= x < width:
                    # 绘制踩点线
                    painter.setPen(QPen(QColor(255, 215, 0), 2))  # 金色
                    painter.drawLine(x, 0, x, height)

                    # 绘制踩点标记
                    painter.setBrush(QBrush(QColor(255, 215, 0)))
                    painter.setPen(QPen(QColor(255, 215, 0), 1))
                    painter.drawEllipse(x - 3, 5, 6, 6)

    def draw_drag_handle(self, painter, rect):
        """绘制拖拽手柄（在右下角，不遮挡波形）"""
        handle_size = 12
        handle_x = rect.width() - handle_size - 4
        handle_y = rect.height() - handle_size - 4

        # 绘制半透明背景
        painter.setBrush(QBrush(QColor(0, 0, 0, 100)))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRect(handle_x - 2, handle_y - 2, handle_size + 4, handle_size + 4)

        # 绘制拖拽图标
        painter.setPen(QPen(QColor(200, 200, 200), 2))
        for i in range(3):
            for j in range(3):
                if (i + j) % 2 == 0:
                    painter.drawPoint(handle_x + i * 3, handle_y + j * 3)

    def draw_trim_preview(self, painter, rect):
        """绘制裁剪预览遮罩"""
        # 如果正在拖拽裁剪游标，显示预览效果
        if self.left_trim_dragging or self.right_trim_dragging:
            # 使用预览位置
            left_trim = self.preview_left_trim_pos
            right_trim = self.preview_right_trim_pos
        else:
            # 使用实际位置
            left_trim = self.left_trim_pos
            right_trim = self.right_trim_pos

        # 绘制左侧遮罩
        if left_trim > 0:
            mask_rect = QRect(0, 0, left_trim, rect.height())
            painter.fillRect(mask_rect, QColor(0, 0, 0, 120))

        # 绘制右侧遮罩
        if right_trim > 0:
            mask_rect = QRect(rect.width() - right_trim, 0, right_trim, rect.height())
            painter.fillRect(mask_rect, QColor(0, 0, 0, 120))

    def draw_trim_handles(self, painter, rect):
        """绘制裁剪手柄"""
        if not isinstance(self.media_item, dict):
            return

        # 使用预览状态或实际状态
        left_trim = self.preview_left_trim_pos if self.left_trim_dragging else self.left_trim_pos
        right_trim = self.preview_right_trim_pos if self.right_trim_dragging else self.right_trim_pos

        # 绘制左侧裁剪手柄
        if left_trim != 0 or self.left_trim_dragging:
            left_x = max(0, left_trim)
            painter.setPen(QPen(QColor(255, 255, 255), 2))
            painter.setBrush(QBrush(QColor(0, 200, 150)))
            painter.drawRect(left_x, 0, 8, rect.height())

        # 绘制右侧裁剪手柄
        if right_trim != 0 or self.right_trim_dragging:
            right_x = min(rect.width() - 8, rect.width() - right_trim - 8)
            painter.setPen(QPen(QColor(255, 255, 255), 2))
            painter.setBrush(QBrush(QColor(0, 200, 150)))
            painter.drawRect(right_x, 0, 8, rect.height())

    def draw_trim_indicator(self, painter, rect):
        """绘制裁剪状态指示器"""
        if not isinstance(self.media_item, dict):
            return

        # 绘制裁剪遮罩
        left_trim = self.preview_left_trim_pos if self.left_trim_dragging else self.left_trim_pos
        right_trim = self.preview_right_trim_pos if self.right_trim_dragging else self.right_trim_pos

        # 左侧遮罩
        if left_trim > 0:
            painter.fillRect(0, 0, left_trim, rect.height(), QColor(0, 0, 0, 128))

        # 右侧遮罩
        if right_trim > 0:
            painter.fillRect(rect.width() - right_trim, 0, right_trim, rect.height(), QColor(0, 0, 0, 128))

    def mousePressEvent(self, event):
        """鼠标按下事件 - 支持裁剪和拖动"""
        print(f"🎵 音频块鼠标按下: pos={event.pos()}, button={event.button()}")

        if event.button() == Qt.MouseButton.RightButton:
            # 🗑️ 右击显示菜单
            self.show_context_menu(event.globalPos())
            return
        elif event.button() == Qt.MouseButton.LeftButton:
            # 检查是否点击了裁剪游标
            print(f"   - 开始检测音频裁剪游标...")
            left_trim_hit = self.is_on_left_trim_handle(event.pos())
            right_trim_hit = self.is_on_right_trim_handle(event.pos())

            print(f"   - 左游标检测: {left_trim_hit}")
            print(f"   - 右游标检测: {right_trim_hit}")

            if left_trim_hit:
                self.left_trim_dragging = True
                self.trim_drag_start_pos = event.pos()
                # 保存当前裁剪状态到历史记录
                self.save_trim_state()
                print("✅ 开始左侧音频裁剪（预览模式）")
                return
            elif right_trim_hit:
                self.right_trim_dragging = True
                self.trim_drag_start_pos = event.pos()
                # 保存当前裁剪状态到历史记录
                self.save_trim_state()
                print("✅ 开始右侧音频裁剪（预览模式）")
                return

            print("   - 普通拖拽模式")
            # 普通拖拽
            self.drag_start_pos = event.pos()
            self.original_pos = self.pos()

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 支持裁剪和拖动"""
        # 处理裁剪游标拖拽 - 剪映风格预览模式
        if self.left_trim_dragging or self.right_trim_dragging:
            print(f"🔍 音频裁剪拖拽中: left={self.left_trim_dragging}, right={self.right_trim_dragging}")

            if self.trim_drag_start_pos:
                delta_x = event.pos().x() - self.trim_drag_start_pos.x()
                print(f"   - 拖拽偏移: delta_x={delta_x}")

                if self.left_trim_dragging:
                    # 左游标：从左边裁剪，支持负值（扩展）
                    # 🔧 修复：通过global_params访问pixels_per_second
                    global_params = getattr(self.timeline, 'global_params', None)
                    if global_params and hasattr(global_params, 'pixels_per_second') and isinstance(self.media_item, dict):
                        max_extend = self.media_item.get('trim_start', 0) * global_params.pixels_per_second
                    else:
                        max_extend = 0
                    old_preview = self.preview_left_trim_pos
                    self.preview_left_trim_pos = max(-max_extend, min(delta_x, self.width() - self.preview_right_trim_pos - 20))
                    print(f"   - 左游标预览: {old_preview} -> {self.preview_left_trim_pos} (max_extend={max_extend})")

                elif self.right_trim_dragging:
                    # 右游标：从右边裁剪，支持负值（扩展）
                    # 🔧 修复：通过global_params访问pixels_per_second
                    global_params = getattr(self.timeline, 'global_params', None)
                    if global_params and hasattr(global_params, 'pixels_per_second') and isinstance(self.media_item, dict):
                        max_extend = self.media_item.get('trim_end', 0) * global_params.pixels_per_second
                    else:
                        max_extend = 0
                    old_preview = self.preview_right_trim_pos
                    self.preview_right_trim_pos = max(-max_extend, min(-delta_x, self.width() - self.preview_left_trim_pos - 20))
                    print(f"   - 右游标预览: {old_preview} -> {self.preview_right_trim_pos} (max_extend={max_extend})")

                # 🔧 性能优化：减少重绘频率，只在必要时更新
                if not hasattr(self, '_last_update_time'):
                    self._last_update_time = 0

                import time
                current_time = time.time()
                if current_time - self._last_update_time > 0.05:  # 最多20fps更新
                    self.update()
                    self._last_update_time = current_time

                # 🔧 修复：更新专门的游标绘制层
                parent = self.parent()
                if parent and hasattr(parent, 'update_trim_handles'):
                    try:
                        parent.update_trim_handles()
                    except:
                        pass  # 忽略调用错误

                return

        # 更新鼠标样式
        if not (event.buttons() & Qt.MouseButton.LeftButton):
            self.update_cursor(event.pos())
            return

        # 处理普通拖拽
        if (event.buttons() == Qt.MouseButton.LeftButton and
            self.drag_start_pos is not None and
            not self.left_trim_dragging and not self.right_trim_dragging):

            distance = (event.pos() - self.drag_start_pos).manhattanLength()

            if distance >= 5 and not self.dragging:
                self.dragging = True
                self.setStyleSheet("QWidget { border: 2px solid #00C896; }")
                # 显示所有轨道的拖拽指示器
                self.show_all_drag_indicators()

                # 🔧 优化：拖动时禁用绘制，提高性能
                self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, True)
                self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)

            if self.dragging:
                delta = event.pos() - self.drag_start_pos
                new_pos = self.original_pos + delta
                new_x = max(0, new_pos.x())  # 允许移动到x=0，对应时间轴0秒
                new_y = max(-50, new_pos.y())

                # 🔧 新增：在拖动过程中应用防重叠检测
                # 🔧 修复：通过global_params访问pixels_per_second
                global_params = getattr(self.timeline, 'global_params', None)
                if global_params and hasattr(global_params, 'pixels_per_second') and isinstance(self.media_item, dict):
                    # 计算新的时间位置
                    new_time = new_x / global_params.pixels_per_second
                    duration = self.media_item.get('duration', 0)

                    # 应用防重叠检测
                    final_position = self.check_overlap_during_drag(new_time, duration)
                    final_x = int(final_position * global_params.pixels_per_second)

                    # 使用防重叠后的位置
                    new_x = final_x

                # 🔧 优化：减少频繁的move调用，使用更高效的方式
                current_pos = self.pos()
                if abs(current_pos.x() - new_x) > 1 or abs(current_pos.y() - new_y) > 1:
                    # 只在位置变化超过1像素时才移动，减少重影
                    self.move(new_x, new_y)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件 - 支持裁剪和拖动"""
        # 处理裁剪游标释放 - 剪映风格应用模式
        if self.left_trim_dragging or self.right_trim_dragging:
            # 应用预览的裁剪到实际数据
            if isinstance(self.media_item, dict):
                self.apply_preview_trim()

            self.left_trim_dragging = False
            self.right_trim_dragging = False
            self.trim_drag_start_pos = None
            # 清除预览状态
            self.preview_left_trim_pos = 0
            self.preview_right_trim_pos = 0

            # 🔧 修复：更新专门的游标绘制层
            parent = self.parent()
            if parent and hasattr(parent, 'update_trim_handles'):
                try:
                    parent.update_trim_handles()
                except:
                    pass  # 忽略调用错误

            print("✅ 音频裁剪完成，已应用到实际数据")
            return

        # 处理普通拖拽释放
        if event.button() == Qt.MouseButton.LeftButton:
            if self.dragging:
                # 应用拖拽结果
                self.apply_drag_result()

                # 清除拖拽状态
                self.dragging = False
                self.setStyleSheet("")  # 清除拖拽样式

                # 恢复正常绘制
                self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, False)
                self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, False)

                # 清除所有拖拽指示器
                self.clear_all_drag_indicators()

                print("✅ 音频拖拽完成")
            elif isinstance(self.media_item, dict):
                # 🎯 修复：音频块被点击 - 直接跳转到对应帧
                start_time = self.media_item.get('start_time', 0.0)
                print(f"🎵 音频素材块被点击，跳转到: {start_time:.2f}s")

                # 找到主窗口并跳转到对应时间
                main_window = self.parent()
                while main_window and not hasattr(main_window, 'global_params'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'global_params'):
                    main_window.global_params.set_current_position(start_time)
                    if hasattr(main_window, 'update_preview_at_position'):
                        main_window.update_preview_at_position(start_time)
                    print(f"🎯 点击音频跳转到: {start_time:.2f}s")

        self.drag_start_pos = None
        self.original_pos = None

    def is_on_left_trim_handle(self, pos):
        """检查鼠标是否在左游标上 - 剪映风格，支持预览"""
        # 安全检查：确保预览属性存在
        if not hasattr(self, 'preview_left_trim_pos'):
            self.preview_left_trim_pos = 0

        # 使用预览状态或实际状态
        left_trim = self.preview_left_trim_pos if self.left_trim_dragging else self.left_trim_pos

        # 获取素材块在轨道中的位置
        block_rect = self.geometry()
        block_x = block_rect.x()

        # 计算左游标在轨道坐标系中的位置
        if left_trim < 0:
            # 扩展状态：游标在素材块左边
            left_x = block_x + left_trim
        else:
            # 普通裁剪状态
            left_x = block_x + left_trim

        # 将鼠标位置转换为轨道坐标系
        track_pos = self.mapToParent(pos)

        # 增加检测区域，使游标更容易点击
        # 检测区域：游标位置前后各12像素，总共24像素宽度
        hit = (left_x - 12) <= track_pos.x() <= (left_x + 12)

        return hit

    def is_on_right_trim_handle(self, pos):
        """检查鼠标是否在右游标上 - 剪映风格，支持预览"""
        # 安全检查：确保预览属性存在
        if not hasattr(self, 'preview_right_trim_pos'):
            self.preview_right_trim_pos = 0

        # 使用预览状态或实际状态
        right_trim = self.preview_right_trim_pos if self.right_trim_dragging else self.right_trim_pos

        # 获取素材块在轨道中的位置
        block_rect = self.geometry()
        block_x = block_rect.x()
        block_width = block_rect.width()

        # 计算右游标在轨道坐标系中的位置
        if right_trim < 0:
            # 扩展状态：游标在素材块右边
            right_x = block_x + block_width - right_trim
        else:
            # 普通裁剪状态
            right_x = block_x + block_width - right_trim

        # 将鼠标位置转换为轨道坐标系
        track_pos = self.mapToParent(pos)

        # 增加检测区域，使游标更容易点击
        # 检测区域：游标位置前后各12像素，总共24像素宽度
        hit = (right_x - 12) <= track_pos.x() <= (right_x + 12)

        return hit

    def update_cursor(self, pos):
        """更新鼠标光标"""
        if self.is_on_left_trim_handle(pos) or self.is_on_right_trim_handle(pos):
            self.setCursor(Qt.CursorShape.SizeHorCursor)
        else:
            self.setCursor(Qt.CursorShape.ArrowCursor)

    def save_trim_state(self):
        """保存当前裁剪状态到历史记录"""
        state = {
            'left_trim_pos': self.left_trim_pos,
            'right_trim_pos': self.right_trim_pos
        }
        self.trim_history.append(state)
        self.current_trim_index = len(self.trim_history) - 1

    def apply_preview_trim(self):
        """应用预览的裁剪到实际数据"""
        if not isinstance(self.media_item, dict):
            return

        # 检查是否是恢复操作（往外拖）
        is_restore_left = self.preview_left_trim_pos < 0
        is_restore_right = self.preview_right_trim_pos < 0

        if is_restore_left or is_restore_right:
            # 恢复裁剪的视觉提示
            self.show_restore_hint(is_restore_left, is_restore_right)

        # 将预览状态应用到实际状态
        self.left_trim_pos = self.preview_left_trim_pos
        self.right_trim_pos = self.preview_right_trim_pos

        # 应用裁剪
        self.apply_trim_to_media()

        print(f"应用音频预览裁剪: 左={self.left_trim_pos}px, 右={self.right_trim_pos}px")

    def apply_trim_to_media(self):
        """应用裁剪到媒体项数据"""
        if not isinstance(self.media_item, dict):
            return

        # 将像素位置转换为时间偏移
        # 🔧 修复：通过global_params访问pixels_per_second
        global_params = getattr(self.timeline, 'global_params', None)
        if not global_params or not hasattr(global_params, 'pixels_per_second'):
            print("❌ 无法获取pixels_per_second")
            return

        left_time_offset = self.left_trim_pos / global_params.pixels_per_second
        right_time_offset = self.right_trim_pos / global_params.pixels_per_second

        # 更新媒体项的裁剪信息
        current_trim_start = self.media_item.get('trim_start', 0)
        current_trim_end = self.media_item.get('trim_end', 0)

        # 应用新的裁剪
        self.media_item['trim_start'] = current_trim_start + left_time_offset
        self.media_item['trim_end'] = current_trim_end + right_time_offset

        # 计算新时长
        original_duration = self.media_item.get('original_duration', self.media_item['duration'])
        new_duration = original_duration - self.media_item['trim_start'] - self.media_item['trim_end']
        self.media_item['duration'] = max(0.1, new_duration)  # 最小0.1秒

        # 🔧 关键修复：更新媒体块的物理宽度，让它变短
        new_width = max(80, int(self.media_item['duration'] * global_params.pixels_per_second))
        current_pos = self.pos()

        print(f"🔧 音频裁剪前宽度: {self.width()}px, 新时长: {self.media_item['duration']:.2f}s")
        print(f"🔧 计算新宽度: {new_width}px (像素比={global_params.pixels_per_second})")

        # 🔧 修复：不要移动位置，只改变宽度
        # 保持左边缘位置不变，只缩短右边缘
        self.setGeometry(current_pos.x(), current_pos.y(), new_width, self.height())

        # 重置裁剪状态
        self.left_trim_pos = 0
        self.right_trim_pos = 0

        print(f"应用音频裁剪到媒体: trim_start={self.media_item['trim_start']:.2f}s, trim_end={self.media_item['trim_end']:.2f}s, new_duration={self.media_item['duration']:.2f}s")
        print(f"🔧 音频媒体块宽度更新: {self.width()} -> {new_width}px")

        # 强制重新绘制
        self.update()

    def show_all_drag_indicators(self):
        """显示所有轨道的拖拽指示器"""
        # 向上查找MultiTrackTimeline实例
        timeline = self.parent()
        while timeline and not hasattr(timeline, 'show_all_drag_indicators'):
            timeline = timeline.parent()

        if timeline and hasattr(timeline, 'show_all_drag_indicators'):
            timeline.show_all_drag_indicators()

    def clear_all_drag_indicators(self):
        """清除所有轨道的拖拽指示器"""
        # 向上查找MultiTrackTimeline实例
        timeline = self.parent()
        while timeline and not hasattr(timeline, 'clear_all_drag_indicators'):
            timeline = timeline.parent()

        if timeline and hasattr(timeline, 'clear_all_drag_indicators'):
            timeline.clear_all_drag_indicators()

    def apply_drag_result(self):
        """应用拖拽结果"""
        if not isinstance(self.media_item, dict):
            return

        # 计算新的时间位置
        # 🔧 修复：通过global_params访问pixels_per_second
        global_params = getattr(self.timeline, 'global_params', None)
        if not global_params or not hasattr(global_params, 'pixels_per_second'):
            print("❌ 无法获取pixels_per_second")
            return

        new_time = max(0, self.x() / global_params.pixels_per_second)

        # 应用磁性吸附
        if hasattr(self.timeline, 'apply_snap'):
            new_time = self.timeline.apply_snap(new_time)

        # 🔧 新功能：轨道内拖动时也要进行防重叠检测
        timeline = self.timeline
        while timeline and not hasattr(timeline, 'find_non_overlapping_position'):
            timeline = timeline.parent()

        if timeline and hasattr(timeline, 'find_non_overlapping_position'):
            # 找到当前素材所在的轨道
            current_track = None
            for track in timeline.tracks:
                if self.media_item in track['media_files']:
                    current_track = track
                    break

            if current_track:
                # 临时移除当前素材，避免与自己重叠检测
                current_track['media_files'].remove(self.media_item)

                # 检查新位置是否重叠
                duration = self.media_item.get('duration', 0)
                final_position = timeline.find_non_overlapping_position(current_track, new_time, duration)

                # 重新添加素材到轨道
                current_track['media_files'].append(self.media_item)

                # 更新媒体项的时间位置
                old_time = self.media_item.get('start_time', 0)
                self.media_item['start_time'] = final_position

                # 更新素材块的显示位置
                final_x = int(final_position * global_params.pixels_per_second)
                self.move(final_x, self.y())

                print(f"🎵 音频块移动: {old_time:.2f}s -> {final_position:.2f}s (防重叠)")
            else:
                # 如果找不到轨道，使用原来的逻辑
                old_time = self.media_item.get('start_time', 0)
                self.media_item['start_time'] = new_time
                print(f"🎵 音频块移动: {old_time:.2f}s -> {new_time:.2f}s")
        else:
            # 如果没有防重叠功能，使用原来的逻辑
            old_time = self.media_item.get('start_time', 0)
            self.media_item['start_time'] = new_time
            print(f"🎵 音频块移动: {old_time:.2f}s -> {new_time:.2f}s")

        # 更新轨道显示
        if hasattr(self.timeline, 'update_track_display'):
            self.timeline.update_track_display(self.track_index)

        # 更新总时长
        if hasattr(self.timeline, 'update_total_duration'):
            self.timeline.update_total_duration()

    def check_overlap_during_drag(self, desired_time: float, duration: float) -> float:
        """在拖动过程中检查重叠并返回合适的位置"""
        try:
            # 找到时间轴对象
            timeline = self.timeline
            while timeline and not hasattr(timeline, 'find_non_overlapping_position'):
                timeline = timeline.parent()

            if not timeline or not hasattr(timeline, 'tracks'):
                return desired_time

            # 找到当前素材所在的轨道
            current_track = None
            for track in timeline.tracks:
                if self.media_item in track['media_files']:
                    current_track = track
                    break

            if not current_track:
                return desired_time

            # 检查是否与其他素材重叠
            desired_end = desired_time + duration

            for media in current_track['media_files']:
                if media == self.media_item:
                    continue  # 跳过自己

                media_start = media.get('start_time', 0)
                media_end = media_start + media.get('duration', 0)

                # 检查重叠
                if not (desired_end <= media_start or desired_time >= media_end):
                    # 有重叠，找到最近的非重叠位置
                    distance_to_start = abs(desired_time - media_start)
                    distance_to_end = abs(desired_time - media_end)

                    if distance_to_start < distance_to_end:
                        # 移动到该素材之前
                        return max(0, media_start - duration)
                    else:
                        # 移动到该素材之后
                        return media_end

            # 没有重叠，返回原位置
            return max(0, desired_time)

        except Exception as e:
            print(f"❌ 音频拖动防重叠检测失败: {e}")
            return max(0, desired_time)

    def show_context_menu(self, global_pos):
        """显示右击菜单"""
        try:
            from PySide6.QtWidgets import QMenu

            menu = QMenu(self)
            menu.setStyleSheet("""
                QMenu {
                    background-color: #2B2B2B;
                    color: #F2ECFF;
                    border: 1px solid #555555;
                    border-radius: 4px;
                    padding: 4px;
                }
                QMenu::item {
                    padding: 8px 16px;
                    border-radius: 3px;
                }
                QMenu::item:selected {
                    background-color: #00C896;
                    color: white;
                }
            """)

            # 添加移除选项
            remove_action = menu.addAction("🗑️ 移除音频")
            remove_action.triggered.connect(self.remove_from_track)

            # 添加复制选项
            copy_action = menu.addAction("📋 复制音频")
            copy_action.triggered.connect(self.copy_media_item)

            # 显示菜单
            menu.exec(global_pos)

        except Exception as e:
            print(f"❌ 显示右击菜单失败: {e}")

    def remove_from_track(self):
        """从轨道中移除音频"""
        try:
            # 找到父级轨道
            track_widget = self.parent()
            if not track_widget or not hasattr(track_widget, 'track_data'):
                print("❌ 无法找到轨道数据")
                return

            track_data = track_widget.track_data
            media_files = track_data.get('media_files', [])

            # 从媒体文件列表中移除
            if self.media_item in media_files:
                media_files.remove(self.media_item)
                print(f"🗑️ 已从轨道移除音频: {self.media_item.get('name', 'Unknown')}")

                # 隐藏并删除自己
                self.hide()
                self.deleteLater()

                # 刷新轨道显示
                if hasattr(track_widget, 'update'):
                    track_widget.update()

            else:
                print("⚠️ 音频不在轨道中")

        except Exception as e:
            print(f"❌ 移除音频失败: {e}")

    def copy_media_item(self):
        """复制音频项"""
        try:
            print(f"📋 复制音频: {self.media_item.get('name', 'Unknown')}")
            # TODO: 实现复制到剪贴板的功能
        except Exception as e:
            print(f"❌ 复制音频失败: {e}")

    def show_restore_hint(self, is_restore_left, is_restore_right):
        """显示恢复裁剪的视觉提示"""
        # 这里可以添加视觉提示，比如改变颜色、显示提示文本等
        if is_restore_left:
            print("💡 提示：正在恢复左侧裁剪")
        if is_restore_right:
            print("💡 提示：正在恢复右侧裁剪")

    def resizeEvent(self, event):
        """调整大小事件"""
        super().resizeEvent(event)
        # 重新生成波形图
        if self._waveform_data:
            self._create_waveform_pixmap()
        self.update()
