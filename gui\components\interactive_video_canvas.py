"""
可拖动的视频播放器窗体 - 每个视频都是独立的可拖动窗体
"""

import cv2
import numpy as np
from PySide6.QtWidgets import QWidget, QLabel, QVBoxLayout, QSizeGrip
from PySide6.QtCore import Qt, QPoint, QRect, QSize, Signal, QTimer
from PySide6.QtGui import QPixmap, QImage, QPainter, QPen, QColor, QMouseEvent
from typing import Dict, List

class DraggableVideoPlayer(QWidget):
    """可拖动的视频播放器窗体"""

    # 信号
    position_changed = Signal(str, QPoint)  # video_path, position
    size_changed = Signal(str, int, int)    # video_path, width, height

    def __init__(self, video_path: str, parent=None):
        super().__init__(parent)

        # 视频信息
        self.video_path = video_path
        self.current_frame = None
        self.video_width = 0
        self.video_height = 0

        # 交互状态
        self.is_dragging = False
        self.last_mouse_pos = QPoint()

        # 🔧 新增：视频播放线程
        self.video_player_thread = None
        self.is_playing = False

        # 🔧 修复：窗体设置 - 作为父容器的子窗体，移出可视窗口就隐藏
        self.setMinimumSize(100, 75)  # 最小尺寸
        self.setMaximumSize(800, 600)  # 最大尺寸，防止过度缩放
        self.resize(100, 75)  # 🔧 修复：使用最小尺寸作为初始尺寸，等视频加载后再调整
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)

        # 🔧 新增：标记是否已经调整过初始尺寸
        self.initial_size_set = False

        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(0)

        # 创建视频显示标签
        self.video_label = QLabel()
        self.video_label.setStyleSheet("""
            QLabel {
                background-color: #000000;
                border: 2px solid #00C896;
                border-radius: 5px;
            }
        """)
        self.video_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.video_label.setScaledContents(True)  # 启用自动缩放
        self.video_label.setText("视频加载中...")  # 默认文本
        layout.addWidget(self.video_label)

        # 🔧 修复：使用自定义缩放手柄，不使用QSizeGrip（QSizeGrip会影响顶级窗体）
        self.resize_handle = QLabel(self)
        self.resize_handle.setFixedSize(16, 16)
        self.resize_handle.setStyleSheet("""
            QLabel {
                background-color: #00C896;
                border: 1px solid #00A876;
                border-radius: 3px;
            }
        """)
        self.resize_handle.setCursor(Qt.CursorShape.SizeFDiagCursor)

        # 缩放相关状态
        self.is_resizing = False
        self.resize_start_pos = QPoint()
        self.resize_start_size = QSize()

        # 启用鼠标跟踪
        self.setMouseTracking(True)

        # 🔧 新增：鼠标悬停时显示缩放手柄
        self.resize_handle.hide()  # 默认隐藏

        print(f"🎬 创建可拖动视频播放器窗体: {video_path}")

        # 🔧 新增：初始化视频播放线程
        self.init_video_player_thread()

    def update_frame(self, frame: np.ndarray):
        """更新显示帧"""
        try:
            if frame is None:
                return

            # 存储帧信息
            self.current_frame = frame.copy()
            self.video_height, self.video_width = frame.shape[:2]

            # 🔧 修复：如果是第一次加载，调整窗体大小到视频尺寸
            if self.video_width > 0 and self.video_height > 0 and not self.initial_size_set:
                # 计算合适的初始大小，保持视频比例
                max_width = 400
                max_height = 300  # 🔧 修复：使用更大的默认高度以适应不同比例

                video_aspect = self.video_width / self.video_height
                print(f"🔧 原始视频尺寸: {self.video_width}x{self.video_height}, 比例: {video_aspect:.3f}")

                if max_width / max_height > video_aspect:
                    # 以高度为准
                    new_height = max_height
                    new_width = int(new_height * video_aspect)
                else:
                    # 以宽度为准
                    new_width = max_width
                    new_height = int(new_width / video_aspect)

                # 确保尺寸在合理范围内
                new_width = max(100, min(new_width, 800))
                new_height = max(75, min(new_height, 600))

                self.resize(new_width, new_height)
                print(f"🔧 调整播放器大小: {new_width}x{new_height} (比例: {video_aspect:.3f})")

                # 🔧 新增：记录初始播放器尺寸作为缩放基准
                self.initial_player_width = new_width
                self.initial_player_height = new_height
                self.initial_size_set = True  # 标记已设置初始尺寸

                # 🔧 新增：发出尺寸改变信号以记录变换
                self.size_changed.emit(self.video_path, QSize(new_width, new_height))

            # 显示帧
            self.display_frame()

        except Exception as e:
            print(f"❌ 更新帧失败: {e}")

    def display_frame(self):
        """显示帧到视频标签，等比缩放填充"""
        try:
            if self.current_frame is None:
                print(f"⚠️ 当前帧为空: {self.video_path}")
                return

            # 转换为RGB
            rgb_frame = cv2.cvtColor(self.current_frame, cv2.COLOR_BGR2RGB)

            # 转换为QImage
            height, width, channel = rgb_frame.shape
            bytes_per_line = 3 * width
            q_image = QImage(rgb_frame.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)

            # 转换为QPixmap
            pixmap = QPixmap.fromImage(q_image)

            # 直接设置pixmap，让QLabel自动缩放
            self.video_label.setPixmap(pixmap)

            print(f"✅ 视频帧已显示: {self.video_path} ({width}x{height})")

        except Exception as e:
            print(f"❌ 显示帧失败: {e}")
            # 显示错误信息
            self.video_label.setText(f"显示错误: {str(e)}")

    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件 - 拖动或缩放播放器"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 🔧 修复：检查是否点击在缩放手柄上
            handle_rect = QRect(self.width() - 16, self.height() - 16, 16, 16)
            if handle_rect.contains(event.position().toPoint()):
                # 开始缩放
                self.is_resizing = True
                self.resize_start_pos = event.globalPosition().toPoint()
                self.resize_start_size = self.size()
                print(f"🔧 开始缩放播放器: {self.video_path}")
            else:
                # 开始拖动
                self.is_dragging = True
                self.last_mouse_pos = event.position().toPoint()  # 使用相对坐标
                self.setCursor(Qt.CursorShape.ClosedHandCursor)
                print(f"🖱️ 开始拖动播放器: {self.video_path}")
            event.accept()  # 阻止事件传播

    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件 - 拖动或等比缩放播放器"""
        if self.is_resizing:
            # 🔧 修复：处理等比缩放
            current_pos = event.globalPosition().toPoint()
            delta = current_pos - self.resize_start_pos

            # 🔧 等比缩放：使用对角线距离来计算缩放比例
            diagonal_delta = (delta.x() + delta.y()) / 2  # 取平均值作为缩放因子

            # 🔧 修复：计算新尺寸（保持原视频比例）
            base_width = self.resize_start_size.width()
            base_height = self.resize_start_size.height()

            new_width = max(100, base_width + diagonal_delta)

            # 🔧 修复：使用原视频比例而不是固定16:9
            if self.video_width > 0 and self.video_height > 0:
                video_aspect = self.video_width / self.video_height
                new_height = max(75, int(new_width / video_aspect))  # 保持原视频比例
            else:
                new_height = max(75, int(new_width * 9 / 16))  # 默认16:9比例

            # 限制最大尺寸（保持比例）
            if new_width > 800:
                new_width = 800
                if self.video_width > 0 and self.video_height > 0:
                    video_aspect = self.video_width / self.video_height
                    new_height = int(new_width / video_aspect)
                else:
                    new_height = int(new_width * 9 / 16)
            if new_height > 600:
                new_height = 600
                if self.video_width > 0 and self.video_height > 0:
                    video_aspect = self.video_width / self.video_height
                    new_width = int(new_height * video_aspect)
                else:
                    new_width = int(new_height * 16 / 9)

            # 🔧 关键：直接调用resize，只影响当前窗体
            self.resize(new_width, new_height)

            print(f"🔧 等比缩放播放器: {self.video_path} -> {new_width}x{new_height}")
            event.accept()

        elif self.is_dragging:
            # 处理拖动
            current_pos = event.position().toPoint()
            delta = current_pos - self.last_mouse_pos

            # 🔧 修复：允许视频移出可视区域（用于裁剪效果）
            new_pos = self.pos() + delta

            # 🔧 修复：不限制边界，允许视频完全移出可视区域
            # 这样用户可以通过移动来实现裁剪效果
            # 移除边界限制，让用户可以自由移动视频

            self.move(new_pos)

            # 发送位置变化信号
            self.position_changed.emit(self.video_path, new_pos)

            print(f"📍 拖动播放器: {self.video_path} -> ({new_pos.x()}, {new_pos.y()})")
            event.accept()  # 阻止事件传播

    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件 - 停止拖动或缩放"""
        if event.button() == Qt.MouseButton.LeftButton:
            if self.is_resizing:
                self.is_resizing = False
                print(f"🔧 停止缩放播放器: {self.video_path}")
            elif self.is_dragging:
                self.is_dragging = False
                self.setCursor(Qt.CursorShape.ArrowCursor)
                print(f"🖱️ 停止拖动播放器: {self.video_path}")
            event.accept()  # 阻止事件传播

    def enterEvent(self, event):
        """鼠标进入事件 - 显示缩放手柄"""
        self.resize_handle.show()
        super().enterEvent(event)

    def leaveEvent(self, event):
        """鼠标离开事件 - 隐藏缩放手柄"""
        if not self.is_resizing:  # 如果正在缩放，不隐藏
            self.resize_handle.hide()
        super().leaveEvent(event)

    def resizeEvent(self, event):
        """窗体大小改变事件 - 🔧 修复：确保只影响当前播放器窗体"""
        super().resizeEvent(event)

        # 🔧 修复：阻止事件传播到父窗体，防止影响整个项目窗体
        event.accept()

        # 🔧 修复：调整自定义缩放手柄位置到右下角
        self.resize_handle.move(
            self.width() - 16,  # 固定16px宽度
            self.height() - 16  # 固定16px高度
        )

        # 发送大小变化信号
        self.size_changed.emit(self.video_path, self.width(), self.height())

        # 重新显示帧以适应新尺寸
        if self.current_frame is not None:
            self.display_frame()

        print(f"📏 播放器大小改变: {self.video_path} -> {self.width()}x{self.height()}")

        # 🔧 修复：确保缩放操作不会影响父窗体
        # 注意：不在resizeEvent中调用resize，避免递归调用
        # 尺寸限制已经在setMinimumSize和setMaximumSize中设置

    def mouseDoubleClickEvent(self, event: QMouseEvent):
        """鼠标双击事件 - 重置到视频原始尺寸"""
        if event.button() == Qt.MouseButton.LeftButton and self.video_width > 0 and self.video_height > 0:
            self.resize(self.video_width + 4, self.video_height + 4)
            print(f"🔄 重置到原始尺寸: {self.video_width}x{self.video_height}")

    def closeEvent(self, event):
        """关闭事件"""
        print(f"🗑️ 关闭视频播放器: {self.video_path}")
        # 🔧 新增：关闭时停止播放线程
        if self.video_player_thread:
            self.video_player_thread.stop()
            self.video_player_thread.wait()
        event.accept()

    def init_video_player_thread(self):
        """初始化视频播放线程"""
        try:
            from gui.components.video_player import VideoPlayerThread
            self.video_player_thread = VideoPlayerThread()
            self.video_player_thread.frame_ready.connect(self.on_video_frame_ready)
            self.video_player_thread.position_changed.connect(self.on_video_position_changed)
            self.video_player_thread.load_video(self.video_path)
            self.video_player_thread.start()
            print(f"✅ 视频播放线程初始化成功: {self.video_path}")
        except Exception as e:
            print(f"❌ 视频播放线程初始化失败: {e}")

    def on_video_frame_ready(self, frame):
        """处理视频帧就绪信号"""
        if self.is_playing:
            self.update_frame(frame)

    def on_video_position_changed(self, position):
        """处理视频位置变化信号"""
        pass  # 位置变化由主窗口管理

    def play(self):
        """开始播放"""
        self.is_playing = True
        if self.video_player_thread:
            self.video_player_thread.play()
            print(f"▶️ 开始播放视频: {self.video_path}")

    def pause(self):
        """暂停播放"""
        self.is_playing = False
        if self.video_player_thread:
            self.video_player_thread.pause()
            print(f"⏸️ 暂停播放视频: {self.video_path}")

    def seek_to_position(self, position: float):
        """跳转到指定位置"""
        if self.video_player_thread:
            self.video_player_thread.seek_to_position(position)
            print(f"⏭️ 跳转到位置: {self.video_path} -> {position:.2f}s")


class VideoCanvasContainer(QWidget):
    """视频播放器容器 - 管理多个可拖动的视频播放器窗体"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # 容器设置
        self.setMinimumSize(800, 600)
        self.setStyleSheet("""
            QWidget {
                background-color: #161616;
                border: 1px solid #333333;
            }
        """)

        # 🔧 新增：设置剪裁属性，隐藏可视窗口外的内容
        self.setAttribute(Qt.WidgetAttribute.WA_OpaquePaintEvent, True)  # 优化绘制性能
        self.setAutoFillBackground(True)  # 自动填充背景

        # 🖼️ 关键：设置剪裁区域，实现拖出去的部分不可见的效果
        # 在Qt中，子窗体默认会被父窗体剪裁，但我们需要确保这个行为
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, False)
        self.setUpdatesEnabled(True)

        # 视频播放器管理
        self.video_players = {}  # {video_path: DraggableVideoPlayer}
        self.current_frames = {}  # {video_path: frame}

        # 预览设置
        self.preview_quality = 'original'
        self.preview_aspect_ratio = '9:16'
        self.aspect_ratios = {
            '16:9': (16, 9),
            '4:3': (4, 3),
            '9:16': (9, 16),
            '1:1': (1, 1),
            '21:9': (21, 9)
        }

        print("🎬 视频画布容器初始化完成")

    def remove_video_player(self, video_path: str):
        """移除指定的视频播放器"""
        try:
            if video_path in self.video_players:
                player = self.video_players[video_path]

                # 停止播放
                if hasattr(player, 'stop_video'):
                    player.stop_video()

                # 从容器中移除
                player.setParent(None)
                player.deleteLater()

                # 从字典中删除
                del self.video_players[video_path]

                # 清除帧缓存
                if video_path in self.current_frames:
                    del self.current_frames[video_path]

                print(f"🗑️ 已移除视频播放器: {video_path}")
            else:
                print(f"⚠️ 视频播放器不存在: {video_path}")
        except Exception as e:
            print(f"❌ 移除视频播放器失败: {e}")

    def add_video_player(self, video_path: str, start_time: float = 0.0):
        """添加视频播放器窗体"""
        try:
            if video_path in self.video_players:
                print(f"⚠️ 视频播放器已存在: {video_path}")
                return self.video_players[video_path]

            # 创建可拖动的视频播放器窗体（作为容器的子窗体）
            player = DraggableVideoPlayer(video_path, self)  # 设置父容器
            player.position_changed.connect(self.on_video_position_changed)
            player.size_changed.connect(self.on_video_size_changed)

            # 🔧 优化：计算容器中心位置，确保视频默认显示在可视窗口中间
            container_center_x = self.width() // 2
            container_center_y = self.height() // 2

            # 🔧 优化：错开显示多个播放器，但保持在中心区域
            offset = len(self.video_players) * 20  # 减小偏移量
            initial_x = container_center_x - 200 + offset  # 200是播放器宽度的一半
            initial_y = container_center_y - 150 + offset  # 150是播放器高度的一半

            # 🔧 新增：确保播放器不会超出容器边界
            max_x = self.width() - 400  # 播放器最大宽度
            max_y = self.height() - 300  # 播放器最大高度
            initial_x = max(50, min(initial_x, max_x))  # 限制在容器内
            initial_y = max(50, min(initial_y, max_y))  # 限制在容器内

            player.move(initial_x, initial_y)
            player.show()

            # 存储播放器
            self.video_players[video_path] = player

            print(f"✅ 添加容器内视频播放器: {video_path} at ({initial_x}, {initial_y}) (容器: {self.width()}x{self.height()})")
            return player

        except Exception as e:
            print(f"❌ 添加视频播放器失败: {e}")
            return None



    def update_video_frame(self, video_path: str, frame: np.ndarray):
        """更新指定视频的帧"""
        try:
            # 存储帧
            self.current_frames[video_path] = frame

            # 更新对应的播放器
            if video_path in self.video_players:
                player = self.video_players[video_path]
                player.update_frame(frame)

        except Exception as e:
            print(f"❌ 更新视频帧失败: {e}")

    def show_video_player(self, video_path: str):
        """显示指定的视频播放器"""
        try:
            if video_path in self.video_players:
                player = self.video_players[video_path]
                if not player.isVisible():
                    player.show()
                    # 🔧 优化：确保播放器在可视区域内
                    self.ensure_player_in_bounds(player)
                    print(f"👁️ 显示视频播放器: {video_path}")
        except Exception as e:
            print(f"❌ 显示视频播放器失败: {e}")

    def hide_video_player(self, video_path: str):
        """隐藏指定的视频播放器"""
        try:
            if video_path in self.video_players:
                player = self.video_players[video_path]
                if player.isVisible():
                    player.hide()
                    # 🔧 新增：隐藏时暂停对应的播放线程
                    if hasattr(player, 'video_player_thread'):
                        player.video_player_thread.pause()
                    print(f"🙈 隐藏视频播放器: {video_path}")
        except Exception as e:
            print(f"❌ 隐藏视频播放器失败: {e}")

    def ensure_player_in_bounds(self, player):
        """确保播放器在容器边界内"""
        try:
            # 获取播放器当前位置和大小
            player_x = player.x()
            player_y = player.y()
            player_width = player.width()
            player_height = player.height()

            # 计算容器边界
            container_width = self.width()
            container_height = self.height()

            # 调整位置确保在边界内
            new_x = max(0, min(player_x, container_width - player_width))
            new_y = max(0, min(player_y, container_height - player_height))

            # 如果位置需要调整，移动播放器
            if new_x != player_x or new_y != player_y:
                player.move(new_x, new_y)
                print(f"📍 调整播放器位置到边界内: ({new_x}, {new_y})")

        except Exception as e:
            print(f"❌ 调整播放器位置失败: {e}")

    def play_all_videos(self):
        """播放所有可见的视频"""
        try:
            for video_path, player in self.video_players.items():
                if player.isVisible():
                    player.play()
            print(f"▶️ 开始播放所有可见视频，共 {len([p for p in self.video_players.values() if p.isVisible()])} 个")
        except Exception as e:
            print(f"❌ 播放所有视频失败: {e}")

    def pause_all_videos(self):
        """暂停所有视频"""
        try:
            for video_path, player in self.video_players.items():
                player.pause()
            print(f"⏸️ 暂停所有视频，共 {len(self.video_players)} 个")
        except Exception as e:
            print(f"❌ 暂停所有视频失败: {e}")

    def seek_all_videos_to_position(self, position: float):
        """让所有视频跳转到指定位置"""
        try:
            for video_path, player in self.video_players.items():
                if player.isVisible():
                    # 计算每个视频的相对位置
                    # 这里需要从主窗口获取视频的开始时间
                    player.seek_to_position(position)
            print(f"⏭️ 所有可见视频跳转到位置: {position:.2f}s")
        except Exception as e:
            print(f"❌ 视频跳转失败: {e}")

    def on_video_position_changed(self, video_path: str, position: QPoint):
        """视频播放器位置改变"""
        print(f"📍 视频位置改变: {video_path} -> ({position.x()}, {position.y()})")

        # 🔧 新增：记录视频变换信息用于导出
        self.record_video_transform(video_path, position=position)

    def on_video_size_changed(self, video_path: str, width: int, height: int):
        """视频播放器大小改变"""
        print(f"📏 视频大小改变: {video_path} -> {width}x{height}")

        # 🔧 新增：记录视频变换信息用于导出
        self.record_video_transform(video_path, size=(width, height))

    def record_video_transform(self, video_path: str, position: QPoint = None, size: tuple = None):
        """记录视频变换信息用于导出"""
        try:
            # 初始化变换记录
            if not hasattr(self, 'video_transforms'):
                self.video_transforms = {}

            if video_path not in self.video_transforms:
                self.video_transforms[video_path] = {
                    'position': {'x': 0, 'y': 0},
                    'size': {'width': 400, 'height': 225},
                    'scale': 1.0,
                    'container_size': {'width': self.width(), 'height': self.height()}
                }

            # 更新位置信息
            if position is not None:
                self.video_transforms[video_path]['position'] = {
                    'x': position.x(),
                    'y': position.y()
                }

            # 更新尺寸信息
            if size is not None:
                width, height = size
                self.video_transforms[video_path]['size'] = {
                    'width': width,
                    'height': height
                }
                # 🔧 修复：计算缩放比例（相对于初始播放器尺寸）
                # 获取初始播放器尺寸作为基准
                if hasattr(self, 'initial_player_width') and hasattr(self, 'initial_player_height'):
                    default_width = self.initial_player_width
                    default_height = self.initial_player_height
                else:
                    # 如果没有初始尺寸，使用当前视频比例计算默认尺寸
                    if self.video_width > 0 and self.video_height > 0:
                        video_aspect = self.video_width / self.video_height
                        default_width = 400
                        default_height = int(400 / video_aspect)
                    else:
                        default_width, default_height = 400, 225  # 默认16:9

                scale_x = width / default_width
                scale_y = height / default_height
                self.video_transforms[video_path]['scale'] = min(scale_x, scale_y)  # 使用较小的缩放比例保持比例

            # 更新容器尺寸
            self.video_transforms[video_path]['container_size'] = {
                'width': self.width(),
                'height': self.height()
            }

            print(f"🔧 记录视频变换: {video_path}")
            print(f"   - 位置: ({self.video_transforms[video_path]['position']['x']}, {self.video_transforms[video_path]['position']['y']})")
            print(f"   - 尺寸: {self.video_transforms[video_path]['size']['width']}x{self.video_transforms[video_path]['size']['height']}")
            print(f"   - 缩放: {self.video_transforms[video_path]['scale']:.2f}")
            print(f"   - 容器: {self.video_transforms[video_path]['container_size']['width']}x{self.video_transforms[video_path]['container_size']['height']}")

            # 通知主窗口变换信息已更新
            parent = self.parent()
            while parent is not None:
                if hasattr(parent, 'on_video_transform_updated'):
                    parent.on_video_transform_updated(video_path, self.video_transforms[video_path])
                    break
                parent = parent.parent()

        except Exception as e:
            print(f"❌ 记录视频变换失败: {e}")

    def get_video_transforms(self) -> dict:
        """获取所有视频的变换信息"""
        return getattr(self, 'video_transforms', {})

    def set_preview_quality(self, quality: str):
        """设置预览质量"""
        if quality in ['original', 'high', 'smooth']:
            self.preview_quality = quality
            print(f"🎬 设置预览质量: {quality}")

            # 🎥 修复：将质量设置传递给所有视频播放器
            for video_path, player in self.video_players.items():
                if hasattr(player, 'set_preview_quality'):
                    player.set_preview_quality(quality)
                    print(f"🎥 已更新播放器质量: {video_path} -> {quality}")

    def set_preview_aspect_ratio(self, aspect_ratio: str):
        """设置预览比例"""
        if aspect_ratio in self.aspect_ratios:
            self.preview_aspect_ratio = aspect_ratio
            self.update()  # 重绘比例框
            print(f"📐 设置预览比例: {aspect_ratio}")

    def paintEvent(self, event):
        """绘制事件 - 绘制比例框"""
        super().paintEvent(event)

        try:
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # 绘制比例框
            if self.preview_aspect_ratio in self.aspect_ratios:
                aspect_w, aspect_h = self.aspect_ratios[self.preview_aspect_ratio]
                aspect_ratio = aspect_w / aspect_h

                container_width = self.width()
                container_height = self.height()

                # 计算比例框大小
                if container_width / container_height > aspect_ratio:
                    frame_height = container_height - 40  # 留边距
                    frame_width = int(frame_height * aspect_ratio)
                else:
                    frame_width = container_width - 40  # 留边距
                    frame_height = int(frame_width / aspect_ratio)

                # 计算比例框位置（居中）
                frame_x = (container_width - frame_width) // 2
                frame_y = (container_height - frame_height) // 2

                # 绘制比例框边界
                pen = QPen(QColor(0, 200, 150), 2)  # 绿色边框
                painter.setPen(pen)
                painter.drawRect(frame_x, frame_y, frame_width, frame_height)

                # 绘制比例标签
                painter.setPen(QPen(QColor(255, 255, 255), 1))
                painter.drawText(frame_x + 10, frame_y + 20, f"导出比例: {self.preview_aspect_ratio}")
                painter.drawText(frame_x + 10, frame_y + frame_height - 10, f"尺寸: {frame_width}x{frame_height}")

        except Exception as e:
            print(f"❌ 绘制比例框失败: {e}")

    def get_visible_content_for_export(self):
        """获取可视窗口内的内容用于导出"""
        # TODO: 实现导出逻辑 - 只导出比例框内可见的视频内容
        pass
