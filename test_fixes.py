#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复功能的脚本
"""

def test_overlap_prevention():
    """测试防重叠功能"""
    print("🔧 测试防重叠功能...")
    
    # 模拟轨道数据
    track_data = {
        'media_files': [
            {'start_time': 0.0, 'duration': 5.0, 'name': 'video1.mp4'},
            {'start_time': 10.0, 'duration': 3.0, 'name': 'video2.mp4'},
            {'start_time': 20.0, 'duration': 4.0, 'name': 'video3.mp4'},
        ]
    }
    
    def check_overlap(desired_time, duration, current_media=None):
        """检查重叠逻辑"""
        desired_end = desired_time + duration
        
        for media in track_data['media_files']:
            if media == current_media:
                continue
            
            media_start = media['start_time']
            media_end = media_start + media['duration']
            
            # 检查重叠
            if not (desired_end <= media_start or desired_time >= media_end):
                print(f"❌ 检测到重叠: {desired_time:.1f}-{desired_end:.1f}s 与 {media['name']} {media_start:.1f}-{media_end:.1f}s")
                
                distance_to_start = abs(desired_time - media_start)
                distance_to_end = abs(desired_time - media_end)
                
                if distance_to_start < distance_to_end:
                    return max(0, media_start - duration)
                else:
                    return media_end
        
        return desired_time
    
    # 测试用例
    test_cases = [
        (2.0, 2.0, "应该移动到0-2s或5-7s"),
        (8.0, 3.0, "应该移动到7-10s或13-16s"),
        (6.0, 2.0, "不重叠，保持6-8s"),
        (22.0, 2.0, "应该移动到18-20s或24-26s"),
    ]
    
    for desired_time, duration, expected in test_cases:
        result = check_overlap(desired_time, duration)
        result_end = result + duration
        print(f"期望: {desired_time:.1f}-{desired_time + duration:.1f}s -> 实际: {result:.1f}-{result_end:.1f}s ({expected})")
    
    print("✅ 防重叠测试完成\n")

def test_thumbnail_aspect_ratio():
    """测试缩略图比例"""
    print("🖼️ 测试缩略图比例...")
    
    # 模拟缩略图尺寸计算
    def calculate_thumbnail_size(original_width, original_height, target_width, target_height):
        """计算保持比例的缩略图尺寸"""
        scale_x = target_width / original_width
        scale_y = target_height / original_height
        scale = min(scale_x, scale_y)
        
        new_width = int(original_width * scale)
        new_height = int(original_height * scale)
        
        return new_width, new_height
    
    test_videos = [
        (1920, 1080, "16:9视频"),
        (1080, 1920, "9:16竖屏视频"),
        (1280, 720, "16:9 HD视频"),
        (640, 480, "4:3视频"),
    ]
    
    target_size = (140, 80)
    
    for width, height, desc in test_videos:
        new_w, new_h = calculate_thumbnail_size(width, height, target_size[0], target_size[1])
        original_ratio = width / height
        new_ratio = new_w / new_h
        print(f"{desc}: {width}x{height} -> {new_w}x{new_h} (比例保持: {abs(original_ratio - new_ratio) < 0.01})")
    
    print("✅ 缩略图比例测试完成\n")

def test_track_height():
    """测试轨道高度"""
    print("📏 测试轨道高度...")
    
    track_height = 64
    media_height = track_height - 8  # 减去8px边距
    media_y = 4  # 上下各4px边距
    
    print(f"轨道高度: {track_height}px")
    print(f"素材高度: {media_height}px")
    print(f"素材Y位置: {media_y}px")
    print(f"上边距: {media_y}px")
    print(f"下边距: {track_height - media_y - media_height}px")
    
    assert media_y == 4, "上边距应该是4px"
    assert media_height == 56, "素材高度应该是56px"
    assert track_height - media_y - media_height == 4, "下边距应该是4px"
    
    print("✅ 轨道高度测试通过\n")

def test_trim_handle_detection():
    """测试裁剪游标检测"""
    print("✂️ 测试裁剪游标检测...")
    
    trim_handle_width = 24
    media_width = 200
    
    def is_on_left_trim_handle(pos_x):
        return pos_x <= trim_handle_width
    
    def is_on_right_trim_handle(pos_x):
        return pos_x >= media_width - trim_handle_width
    
    test_positions = [
        (10, "左游标区域"),
        (30, "中间区域"),
        (180, "右游标区域"),
        (100, "中间区域"),
    ]
    
    for pos_x, expected in test_positions:
        left_hit = is_on_left_trim_handle(pos_x)
        right_hit = is_on_right_trim_handle(pos_x)
        
        if left_hit:
            result = "左游标"
        elif right_hit:
            result = "右游标"
        else:
            result = "中间区域"
        
        print(f"位置 {pos_x}px: {result} (期望: {expected})")
    
    print("✅ 裁剪游标检测测试完成\n")

if __name__ == "__main__":
    print("🧪 开始测试修复功能...\n")
    
    test_overlap_prevention()
    test_thumbnail_aspect_ratio()
    test_track_height()
    test_trim_handle_detection()
    
    print("🎉 所有测试完成！")
