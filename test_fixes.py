#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复的功能
"""

def test_video_transform_fix():
    """测试视频变换记录修复"""
    print("🔧 测试视频变换记录修复...")
    
    # 模拟VideoCanvasContainer
    class MockVideoCanvasContainer:
        def __init__(self):
            self.video_players = {
                'test_video.mp4': MockPlayer()
            }
        
        def width(self):
            return 800
        
        def height(self):
            return 600
    
    class MockPlayer:
        def __init__(self):
            self.video_width = 1920
            self.video_height = 1080
    
    container = MockVideoCanvasContainer()
    
    # 测试记录变换
    try:
        # 模拟record_video_transform逻辑
        video_path = 'test_video.mp4'
        size = (400, 225)
        
        # 初始化变换记录
        video_transforms = {}
        
        if video_path not in video_transforms:
            video_transforms[video_path] = {
                'position': {'x': 0, 'y': 0},
                'size': {'width': 400, 'height': 225},
                'scale': 1.0,
                'container_size': {'width': container.width(), 'height': container.height()}
            }
        
        # 更新尺寸信息
        if size is not None:
            width, height = size
            video_transforms[video_path]['size'] = {
                'width': width,
                'height': height
            }
            
            # 🐛 修复：从播放器获取视频尺寸
            player = container.video_players.get(video_path)
            if player and hasattr(player, 'video_width') and hasattr(player, 'video_height'):
                if player.video_width > 0 and player.video_height > 0:
                    video_aspect = player.video_width / player.video_height
                    default_width = 400
                    default_height = int(400 / video_aspect)
                    print(f"✅ 计算默认尺寸: {default_width}x{default_height}")
                else:
                    default_width = 400
                    default_height = 225
            else:
                default_width = 400
                default_height = 225
        
        print("✅ 视频变换记录修复测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 视频变换记录修复测试失败: {e}")
        return False

def test_signal_fix():
    """测试信号修复"""
    print("🔧 测试信号修复...")
    
    try:
        # 模拟正确的信号发射
        def emit_size_changed(video_path, width, height):
            print(f"📡 发射信号: {video_path}, {width}x{height}")
            return True
        
        # 测试正确的参数
        result = emit_size_changed("test_video.mp4", 400, 225)
        
        if result:
            print("✅ 信号修复测试通过")
            return True
        else:
            print("❌ 信号修复测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 信号修复测试失败: {e}")
        return False

def test_trim_handle_fix():
    """测试裁剪游标修复"""
    print("🔧 测试裁剪游标修复...")
    
    try:
        # 模拟游标检测
        class MockVideoBlock:
            def __init__(self):
                self.trim_handle_width = 24
                self._width = 80  # 模拟素材块宽度
            
            def width(self):
                return self._width
            
            def is_on_left_trim_handle(self, pos_x):
                return pos_x <= self.trim_handle_width
            
            def is_on_right_trim_handle(self, pos_x):
                return pos_x >= self.width() - self.trim_handle_width
            
            def should_draw_handles(self):
                return self.width() >= 50  # 修复后的条件
        
        block = MockVideoBlock()
        
        # 测试游标检测
        left_hit = block.is_on_left_trim_handle(10)  # 在左游标区域
        right_hit = block.is_on_right_trim_handle(70)  # 在右游标区域
        middle_hit = block.is_on_left_trim_handle(40) or block.is_on_right_trim_handle(40)  # 在中间
        
        should_draw = block.should_draw_handles()
        
        if left_hit and right_hit and not middle_hit and should_draw:
            print("✅ 裁剪游标修复测试通过")
            return True
        else:
            print(f"❌ 裁剪游标修复测试失败: left={left_hit}, right={right_hit}, middle={middle_hit}, draw={should_draw}")
            return False
            
    except Exception as e:
        print(f"❌ 裁剪游标修复测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 开始测试修复...")
    
    tests = [
        test_video_transform_fix,
        test_signal_fix,
        test_trim_handle_fix
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
