#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口 - 完全按照原版复制
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QMenuBar, QMenu, QStatusBar, QMessageBox, QFileDialog, QLabel,
    QPushButton, QComboBox, QSlider, QApplication, QInputDialog, QGraphicsDropShadowEffect,
    QDialog, QLineEdit, QGroupBox, QGridLayout, QProgressBar, QProgressDialog,
    QTabWidget, QScrollArea, QCheckBox, QSpinBox, QDoubleSpinBox, QTextEdit, QFrame
)
from PySide6.QtGui import QPixmap
from PySide6.QtCore import Qt, Signal, QTimer, QSize, QTime
from PySide6.QtGui import QAction, QIcon, QFont, QKeySequence, QColor, QPainter, QPainterPath, QBrush

from pathlib import Path
import logging
import os
import cv2

# 导入拆分的组件
from .media_library import MediaLibrary
from .multi_track_timeline import MultiTrackTimeline
from .video_thumbnail_block import VideoThumbnailBlock
from .audio_waveform_block import AudioWaveformBlock

# 🔧 新设计：不再需要统一播放头管理器
# from gui.components.unified_playhead import UnifiedPlayheadManager

# 导入核心组件
from core.media_processing.video_processor import VideoProcessor
from core.templates.template_manager import HairSalonTemplateManager
from core.batch_processing.batch_processor import HairSalonBatchProcessor
from core.common.enhanced_features import EnhancedFeatureManager
from core.common.logger import get_logger
from core.project_manager import ProjectManager
from core.preview_compositor import PreviewCompositor
from gui.components.interactive_video_canvas import VideoCanvasContainer

# 导入其他组件
from ..components.video_player import VideoPlayer
from ..components.properties_panel import PropertiesPanel
from ..components.custom_title_bar import CustomTitleBar
from ..components.window_controller import WindowController
from ..dialogs.swanksalon_dialog import SwankSalonDialog
from ..styles.dialog_styles import DialogStyleManager, CustomDialog

logger = logging.getLogger(__name__)

class MainWindow(QMainWindow):
    """主窗口 - 真正的剪映风格"""
    
    def __init__(self, config):
        super().__init__()
        self.config = config

        # 🔧 新增：初始化项目管理器
        self.project_manager = ProjectManager()
        self.project_modified = False

        # 🔧 新增：初始化预览合成器
        self.preview_compositor = PreviewCompositor()
        self.preview_compositor.frame_ready.connect(self.on_preview_frame_ready)

        # 🔧 新增：应用全局弹窗样式
        DialogStyleManager.setup_application_style()

        # 设置无边框窗口并启用透明背景以支持圆角
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)

        # 初始化窗口控制器
        self.window_controller = WindowController(self)

        # 创建全局参数管理器
        from gui.main.multi_track_timeline import TimelineGlobalParams
        self.global_params = TimelineGlobalParams()

        # 连接全局参数变化信号
        self.global_params.position_changed.connect(self.on_global_position_changed)
        self.global_params.playback_speed_changed.connect(self.on_global_playback_speed_changed)
        self.global_params.duration_changed.connect(self.on_global_duration_changed)

        # 初始化轨道数据
        self.tracks = []

        # 🔧 修复：添加播放状态管理
        self._is_playing = False
        self._playback_state_lock = False
        self._updating_position = False

        # 初始化组件
        self.initialize_components()

        # 初始化UI
        self.init_ui()

        print("✅ 主窗口初始化完成，使用全局参数管理器")

        # 🔧 注释：自动加载项目现在在main.py中处理
        # QTimer.singleShot(100, self.auto_load_last_project)

    def on_global_position_changed(self, position: float):
        """响应全局位置变化"""
        if not self.global_params._updating_position:
            self.update_time_display_from_position(position)

    def on_global_playback_speed_changed(self, speed: float):
        """响应全局播放速度变化"""
        self.update_playback_speed_display(speed)

    def on_global_duration_changed(self, duration: float):
        """响应全局时长变化"""
        # 可以在这里更新UI中显示总时长的地方
        pass

    def update_time_display_from_position(self, position: float):
        """根据位置更新时间显示 - 统一时间基准"""
        current_time = self.format_time(position)
        total_time = self.format_time(self.global_params.total_duration)
        time_text = f"{current_time} / {total_time}"

        # 更新时间标签
        self.time_label.setText(time_text)

        # 🔧 修复：只有在非拖动状态下才更新进度滑块，避免冲突
        if not self.progress_slider_dragging and self.global_params.total_duration > 0:
            progress_value = int((position / self.global_params.total_duration) * 1000)
            self.progress_slider.setValue(progress_value)
        if hasattr(self, 'time_label'):
            self.time_label.setText(time_text)

        # 更新进度滑块
        if hasattr(self, 'progress_slider') and not getattr(self, 'progress_slider_dragging', False):
            if self.global_params.total_duration > 0:
                progress_value = int((position / self.global_params.total_duration) * 1000)
                self.progress_slider.setValue(progress_value)

    def update_playback_speed_display(self, speed: float):
        """更新播放速度显示"""
        if hasattr(self, 'speed_display'):
            self.speed_display.setText(f"{speed:.2f}x")

    def initialize_components(self):
        """初始化组件"""
        # 用户偏好设置
        self.user_preferences = {
            'user_type': self.config.get('user_type', 'salon_owner'),
            'automation_level': self.config.get('automation_level', 'semi_auto'),
            'show_tips': self.config.get('show_tips', True),
            'quick_access_enabled': self.config.get('quick_access_enabled', True)
        }

        try:
            self.video_processor = VideoProcessor(self.config)

            # 初始化理发店模板管理器
            self.template_manager = HairSalonTemplateManager(self.config)

            # 创建默认模板（如果不存在）
            self.ensure_default_template()

            # 初始化批量处理器
            self.batch_processor = HairSalonBatchProcessor(
                self.config, self.template_manager, self.video_processor
            )

            # 初始化增强功能管理器
            self.enhanced_manager = EnhancedFeatureManager(self.config)

            # 初始化日志器
            self.logger = get_logger('main_window')

            # 应用深色主题
            self.apply_dark_theme()

            # 检查是否显示欢迎向导
            self.check_welcome_wizard()

            # 恢复窗口状态
            self.restore_window_state()

            # 检查FFmpeg
            self.check_dependencies()

            # 显示智能提示
            self.show_smart_tips()
            
        except Exception as e:
            print(f"❌ 初始化主窗口失败: {e}")
            import traceback
            traceback.print_exc()
            # 显示错误对话框
            QMessageBox.critical(None, "初始化错误", f"程序初始化失败:\n{str(e)}")
            import sys
            sys.exit(1)
    
    def apply_dark_theme(self):
        """应用高级黑白主题 - 通过绘制实现圆角"""
        # 设置透明背景，圆角通过paintEvent绘制
        self.setStyleSheet("""
            QMainWindow {
                background-color: transparent;
                color: #FFFFFF;
            }
            QWidget {
                background-color: transparent;
                color: #FFFFFF;
            }
            QStatusBar {
                background-color: transparent;
                color: #FFFFFF;
            }
            QSplitter::handle {
                background-color: #2A2A2A;
            }
        """)
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("SWANKSALON")
        self.setMinimumSize(1400, 900)  # 最小尺寸

        # 获取屏幕尺寸并设置为全屏（包含刘海区域）
        screen = self.screen()
        if screen:
            # 使用完整屏幕几何，包含刘海区域
            screen_geometry = screen.geometry()
            self.setGeometry(screen_geometry)

            # 检测是否为Mac刘海屏
            self.detect_notch_screen(screen)

            # 标记为最大化状态
            self.window_controller.is_maximized = True
            if hasattr(self, 'title_bar'):
                self.title_bar.update_maximize_button(True)
        else:
            # 备用方案：使用默认尺寸
            self.resize(1920, 1080)

        # 创建中央部件
        central_widget = QWidget()
        central_widget.setObjectName("centralWidget")
        self.setCentralWidget(central_widget)

        # 创建主布局 - 上下分布
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建自定义标题栏
        self.title_bar = CustomTitleBar(self)
        self.title_bar.minimize_clicked.connect(self.window_controller.minimize_window)
        self.title_bar.maximize_clicked.connect(self.window_controller.toggle_maximize)
        self.title_bar.close_clicked.connect(self.window_controller.close_window)
        main_layout.addWidget(self.title_bar)

        # 如果窗口已经是最大化状态，更新按钮显示
        if hasattr(self.window_controller, 'is_maximized') and self.window_controller.is_maximized:
            self.title_bar.update_maximize_button(True)

        # 创建上下分割器
        main_splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 上半部分 - 素材库和预览
        upper_area = self.create_upper_area()
        main_splitter.addWidget(upper_area)
        
        # 下半部分 - 多轨道时间轴，使用全局参数管理器
        self.timeline = MultiTrackTimeline(self.video_processor, self.global_params)
        # 连接时间轴位置改变信号
        self.timeline.position_changed.connect(self.on_timeline_position_changed)
        # 连接媒体拖放信号
        self.timeline.media_dropped.connect(self.on_media_dropped_to_timeline)
        # 连接媒体点击信号
        self.timeline.media_clicked.connect(self.on_media_clicked)
        main_splitter.addWidget(self.timeline)

        # 🔧 新设计：不再需要统一播放头管理器，播放头在滚动内容中
        # self.unified_playhead = UnifiedPlayheadManager(self.timeline)
        # QTimer.singleShot(100, self.setup_unified_playhead)
        # print("✅ 简单播放头管理器已创建")
        
        # 设置分割器比例 (上:下 = 2:1)
        main_splitter.setSizes([600, 300])
        
        main_layout.addWidget(main_splitter)
        
        # 创建状态栏
        self.create_status_bar()
        
        # 连接信号
        self.connect_signals()
        
        logger.info("主窗口初始化完成")

    def closeEvent(self, event):
        """关闭事件处理"""
        try:
            # 检查项目是否需要保存
            if self.project_modified or self.has_unsaved_changes():
                reply = QMessageBox.question(
                    self, "保存项目",
                    "项目已修改，是否保存？",
                    QMessageBox.StandardButton.Save |
                    QMessageBox.StandardButton.Discard |
                    QMessageBox.StandardButton.Cancel,
                    QMessageBox.StandardButton.Save
                )

                if reply == QMessageBox.StandardButton.Save:
                    # 保存项目
                    self.save_current_project_state()
                    if not self.project_manager.save_project():
                        # 保存失败，询问是否继续关闭
                        continue_reply = QMessageBox.question(
                            self, "保存失败",
                            "保存项目失败，是否仍要关闭？",
                            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                            QMessageBox.StandardButton.No
                        )
                        if continue_reply == QMessageBox.StandardButton.No:
                            event.ignore()
                            return
                elif reply == QMessageBox.StandardButton.Cancel:
                    event.ignore()
                    return
                # Discard 选择不做任何操作，直接关闭

            # 保存当前项目状态（即使用户选择不保存，也要更新最后打开的项目信息）
            self.save_current_project_state()

            # 清理视频播放器资源
            if hasattr(self, 'video_player'):
                self.video_player.cleanup()

            # 接受关闭事件
            event.accept()
            print("✅ 主窗口关闭完成")
        except Exception as e:
            print(f"❌ 主窗口关闭失败: {e}")
            event.accept()  # 即使出错也要关闭

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)

        # 🔧 新设计：不再需要更新统一播放头位置
        # if hasattr(self, 'unified_playhead') and hasattr(self, 'timeline'):
        #     QTimer.singleShot(10, self.update_unified_playhead_geometry)

    # 🔧 新设计：不再需要统一播放头管理器相关方法
    # def setup_unified_playhead(self):
    #     """初始设置统一播放头"""
    #     if hasattr(self, 'unified_playhead') and hasattr(self, 'timeline'):
    #         self.update_unified_playhead_geometry()
    #
    #         # 设置时间轴参数
    #         timeline_geometry = self.timeline.geometry()
    #         timeline_start_x = 128
    #         timeline_width = timeline_geometry.width() - timeline_start_x
    #
    #         # 使用新的API设置参数
    #         self.unified_playhead.set_timeline_params(
    #             start_x=timeline_start_x,
    #             width=timeline_width,
    #             duration=self.global_params.total_duration,
    #             pixels_per_second=self.global_params.pixels_per_second
    #         )
    #
    #         # 设置轨道参数
    #         track_count = len(self.timeline.tracks) if hasattr(self.timeline, 'tracks') else 2
    #         self.unified_playhead.set_track_params(
    #             track_count=track_count,
    #             track_height=self.global_params.track_height,
    #             track_spacing=self.global_params.track_spacing
    #         )
    #
    #         print(f"🎯 统一播放头参数设置: start_x={timeline_start_x}, width={timeline_width}, duration={self.global_params.total_duration}, tracks={track_count}")
    #
    #
    #
    # def update_unified_playhead_geometry(self):
    #     """更新统一播放头几何位置"""
    #     if hasattr(self, 'unified_playhead') and hasattr(self, 'timeline'):
    #         timeline_geometry = self.timeline.geometry()
    #         self.unified_playhead.update_geometry(timeline_geometry)
    #         print(f"🎯 统一播放头几何更新: {timeline_geometry.width()}x{timeline_geometry.height()} at ({timeline_geometry.x()}, {timeline_geometry.y()})")
    
    def create_upper_area(self):
        """创建上半部分区域"""
        # 上半部分水平分割器
        upper_splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧 - 素材库
        self.media_library = MediaLibrary(self.config)
        # 🔧 修改：去掉预览连接，只连接信息显示
        self.media_library.show_media_info.connect(self.show_media_info_with_fps)  # 连接信息显示（包含帧率）
        # 🔧 新增：连接模板编辑信号
        self.media_library.template_edit_requested.connect(self.show_template_editor)
        upper_splitter.addWidget(self.media_library)

        # 中间 - 视频预览区域
        preview_area = self.create_preview_area()
        upper_splitter.addWidget(preview_area)

        # 右侧 - 媒体信息区域
        self.media_info_area = self.create_media_info_area()
        upper_splitter.addWidget(self.media_info_area)

        # 🔧 修改：设置比例 (素材库:预览:信息 = 528:1024:368，基于1920*1080设计)
        upper_splitter.setSizes([528, 1024, 368])
        # 设置最小尺寸
        upper_splitter.setChildrenCollapsible(False)

        return upper_splitter

    def create_media_info_area(self):
        """创建右侧面板区域 - 支持媒体信息和参数调节"""
        # 创建主容器
        self.right_panel = QWidget()
        self.right_panel.setMinimumSize(200, 400)
        self.right_panel.setStyleSheet("""
            QWidget {
                background-color: #4B4D52;
            }
        """)

        # 创建主布局
        main_layout = QVBoxLayout(self.right_panel)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建标签页控件
        self.right_panel_tabs = QTabWidget()
        self.right_panel_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background-color: #4B4D52;
            }
            QTabBar::tab {
                background-color: #333333;
                color: #F2ECFF;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #00C896;
                color: #FFFFFF;
            }
            QTabBar::tab:hover {
                background-color: #555555;
            }
        """)

        # 创建媒体信息页面
        self.create_media_info_tab()

        # 创建视频调节页面
        self.create_video_adjustment_tab()

        # 创建音频编辑页面
        self.create_audio_editor_tab()

        # 🔧 新增：创建模板编辑页面
        self.create_template_editor_tab()

        main_layout.addWidget(self.right_panel_tabs)

        # 默认显示媒体信息
        self.show_media_info_mode()

        return self.right_panel

    def create_media_info_tab(self):
        """创建媒体信息标签页"""
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(12, 12, 12, 12)
        info_layout.setSpacing(8)

        # 信息显示区域
        self.media_info_label = QLabel("请选择媒体文件")
        self.media_info_label.setStyleSheet("""
            QLabel {
                color: #F2ECFF;
                font-size: 12px;
                background-color: rgba(0, 0, 0, 0.3);
                border-radius: 6px;
                padding: 12px;
                border: 1px solid #666666;
            }
        """)
        self.media_info_label.setWordWrap(True)
        self.media_info_label.setAlignment(Qt.AlignmentFlag.AlignTop)
        info_layout.addWidget(self.media_info_label)
        info_layout.addStretch()

        self.right_panel_tabs.addTab(info_widget, "媒体信息")

    def create_video_adjustment_tab(self):
        """创建视频调节标签页"""
        video_widget = QWidget()
        video_layout = QVBoxLayout(video_widget)
        video_layout.setContentsMargins(12, 12, 12, 12)
        video_layout.setSpacing(12)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #333333;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #666666;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #888888;
            }
        """)

        # 调节内容容器
        adjustment_content = QWidget()
        content_layout = QVBoxLayout(adjustment_content)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(16)

        # 添加各种调节控件
        self.create_video_adjustment_controls(content_layout)

        scroll_area.setWidget(adjustment_content)
        video_layout.addWidget(scroll_area)

        self.right_panel_tabs.addTab(video_widget, "视频")

    def create_audio_editor_tab(self):
        """创建音频编辑标签页"""
        audio_widget = QWidget()
        audio_layout = QVBoxLayout(audio_widget)
        audio_layout.setContentsMargins(12, 12, 12, 12)
        audio_layout.setSpacing(12)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #333333;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #666666;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #888888;
            }
        """)

        # 音频编辑内容容器
        audio_content = QWidget()
        content_layout = QVBoxLayout(audio_content)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(16)

        # 添加音频编辑控件
        self.create_audio_editor_controls(content_layout)

        scroll_area.setWidget(audio_content)
        audio_layout.addWidget(scroll_area)

        self.right_panel_tabs.addTab(audio_widget, "音频")

    def create_template_editor_tab(self):
        """创建模板编辑标签页"""
        template_widget = QWidget()
        template_layout = QVBoxLayout(template_widget)
        template_layout.setContentsMargins(8, 8, 8, 8)  # 🔧 修改：减小边距
        template_layout.setSpacing(8)  # 🔧 修改：减小间距

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #333333;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #666666;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #888888;
            }
        """)

        # 模板编辑内容容器
        self.template_content = QWidget()
        self.template_content_layout = QVBoxLayout(self.template_content)
        self.template_content_layout.setContentsMargins(8, 8, 8, 8)  # 🔧 修改：减小边距
        self.template_content_layout.setSpacing(8)  # 🔧 修改：减小间距

        # 默认显示提示信息
        self.show_template_editor_placeholder()

        scroll_area.setWidget(self.template_content)
        template_layout.addWidget(scroll_area)

        self.right_panel_tabs.addTab(template_widget, "模板编辑")

    def create_video_adjustment_controls(self, layout):
        """创建视频调节控件"""
        # 基础调节组
        basic_group = self.create_control_group("基础调节")
        basic_layout = QVBoxLayout(basic_group)

        # 亮度
        self.brightness_slider = self.create_slider_control("亮度", -100, 100, 0, basic_layout)
        # 对比度
        self.contrast_slider = self.create_slider_control("对比度", -100, 100, 0, basic_layout)
        # 饱和度
        self.saturation_slider = self.create_slider_control("饱和度", -100, 100, 0, basic_layout)
        # 色调
        self.hue_slider = self.create_slider_control("色调", -180, 180, 0, basic_layout)

        layout.addWidget(basic_group)

        # 高级调节组
        advanced_group = self.create_control_group("高级调节")
        advanced_layout = QVBoxLayout(advanced_group)

        # 曝光
        self.exposure_slider = self.create_slider_control("曝光", -200, 200, 0, advanced_layout)
        # 阴影
        self.shadows_slider = self.create_slider_control("阴影", -100, 100, 0, advanced_layout)
        # 高光
        self.highlights_slider = self.create_slider_control("高光", -100, 100, 0, advanced_layout)
        # 清晰度
        self.clarity_slider = self.create_slider_control("清晰度", -100, 100, 0, advanced_layout)

        layout.addWidget(advanced_group)

        # 色彩调节组
        color_group = self.create_control_group("色彩调节")
        color_layout = QVBoxLayout(color_group)

        # 温度
        self.temperature_slider = self.create_slider_control("色温", -100, 100, 0, color_layout)
        # 色调
        self.tint_slider = self.create_slider_control("色调", -100, 100, 0, color_layout)
        # 自然饱和度
        self.vibrance_slider = self.create_slider_control("自然饱和度", -100, 100, 0, color_layout)

        layout.addWidget(color_group)

        # 重置按钮
        reset_btn = QPushButton("重置所有调节")
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #666666;
                color: #F2ECFF;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #777777;
            }
            QPushButton:pressed {
                background-color: #555555;
            }
        """)
        reset_btn.clicked.connect(self.reset_video_adjustments)
        layout.addWidget(reset_btn)

        layout.addStretch()

    def create_audio_editor_controls(self, layout):
        """创建音频编辑控件"""
        # 音量调节组
        volume_group = self.create_control_group("音量调节")
        volume_layout = QVBoxLayout(volume_group)

        # 主音量
        self.volume_slider = self.create_slider_control("音量", 0, 200, 100, volume_layout, suffix="%")
        # 静音开关
        self.mute_checkbox = QCheckBox("静音")
        self.mute_checkbox.setStyleSheet("""
            QCheckBox {
                color: #F2ECFF;
                font-size: 12px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid #666666;
                border-radius: 3px;
                background-color: transparent;
            }
            QCheckBox::indicator:checked {
                background-color: #00C896;
                border-color: #00C896;
            }
        """)
        volume_layout.addWidget(self.mute_checkbox)

        layout.addWidget(volume_group)

        # 音频效果组
        effects_group = self.create_control_group("音频效果")
        effects_layout = QVBoxLayout(effects_group)

        # 低音
        self.bass_slider = self.create_slider_control("低音", -20, 20, 0, effects_layout, suffix="dB")
        # 高音
        self.treble_slider = self.create_slider_control("高音", -20, 20, 0, effects_layout, suffix="dB")
        # 回声
        self.echo_slider = self.create_slider_control("回声", 0, 100, 0, effects_layout, suffix="%")
        # 混响
        self.reverb_slider = self.create_slider_control("混响", 0, 100, 0, effects_layout, suffix="%")

        layout.addWidget(effects_group)

        # 降噪组
        noise_group = self.create_control_group("降噪处理")
        noise_layout = QVBoxLayout(noise_group)

        # 降噪强度
        self.noise_reduction_slider = self.create_slider_control("降噪强度", 0, 100, 0, noise_layout, suffix="%")
        # 语音增强
        self.voice_enhance_checkbox = QCheckBox("语音增强")
        self.voice_enhance_checkbox.setStyleSheet(self.mute_checkbox.styleSheet())
        noise_layout.addWidget(self.voice_enhance_checkbox)

        layout.addWidget(noise_group)

        # 重置按钮
        reset_audio_btn = QPushButton("重置音频设置")
        reset_audio_btn.setStyleSheet("""
            QPushButton {
                background-color: #666666;
                color: #F2ECFF;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #777777;
            }
            QPushButton:pressed {
                background-color: #555555;
            }
        """)
        reset_audio_btn.clicked.connect(self.reset_audio_settings)
        layout.addWidget(reset_audio_btn)

        layout.addStretch()

    def create_control_group(self, title):
        """创建控件组"""
        group = QGroupBox(title)
        group.setStyleSheet("""
            QGroupBox {
                color: #F2ECFF;
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #666666;
                border-radius: 6px;
                margin-top: 8px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 8px 0 8px;
                background-color: #4B4D52;
            }
        """)
        return group

    def create_slider_control(self, name, min_val, max_val, default_val, layout, suffix=""):
        """创建滑块控件"""
        # 创建容器
        container = QWidget()
        container_layout = QHBoxLayout(container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(8)

        # 标签
        label = QLabel(name)
        label.setStyleSheet("""
            QLabel {
                color: #F2ECFF;
                font-size: 12px;
                min-width: 60px;
            }
        """)
        container_layout.addWidget(label)

        # 滑块
        slider = QSlider(Qt.Orientation.Horizontal)
        slider.setMinimum(min_val)
        slider.setMaximum(max_val)
        slider.setValue(default_val)
        slider.setStyleSheet("""
            QSlider {
                height: 30px;
            }
            QSlider::groove:horizontal {
                border: 1px solid #666666;
                height: 4px;
                background: #333333;
                border-radius: 2px;
                margin: 0px;
            }
            QSlider::handle:horizontal {
                background: #00C896;
                border: 1px solid #00C896;
                width: 16px;
                height: 16px;
                margin: -6px 0;
                border-radius: 8px;
                outline: none;
            }
            QSlider::handle:horizontal:hover {
                background: #00E8A8;
                border-color: #00E8A8;
            }
            QSlider::handle:horizontal:pressed {
                background: #00D4A0;
                border-color: #00D4A0;
            }
        """)
        container_layout.addWidget(slider)

        # 数值显示
        value_label = QLabel(f"{default_val}{suffix}")
        value_label.setStyleSheet("""
            QLabel {
                color: #F2ECFF;
                font-size: 12px;
                min-width: 40px;
                text-align: right;
            }
        """)
        container_layout.addWidget(value_label)

        # 连接信号
        slider.valueChanged.connect(lambda v: value_label.setText(f"{v}{suffix}"))

        layout.addWidget(container)
        return slider

    def show_media_info_mode(self):
        """显示媒体信息模式"""
        self.right_panel_tabs.setCurrentIndex(0)  # 媒体信息标签页
        # 只显示媒体信息标签页
        for i in range(self.right_panel_tabs.count()):
            self.right_panel_tabs.setTabVisible(i, i == 0)

    def show_video_adjustment_mode(self):
        """显示视频调节模式"""
        # 显示视频和音频标签页
        for i in range(self.right_panel_tabs.count()):
            self.right_panel_tabs.setTabVisible(i, i in [1, 2])  # 视频和音频标签页
        self.right_panel_tabs.setCurrentIndex(1)  # 默认显示视频标签页

    def show_audio_adjustment_mode(self):
        """显示音频调节模式"""
        # 只显示音频标签页
        for i in range(self.right_panel_tabs.count()):
            self.right_panel_tabs.setTabVisible(i, i == 2)  # 只显示音频标签页
        self.right_panel_tabs.setCurrentIndex(2)

    def reset_video_adjustments(self):
        """重置视频调节"""
        self.brightness_slider.setValue(0)
        self.contrast_slider.setValue(0)
        self.saturation_slider.setValue(0)
        self.hue_slider.setValue(0)
        self.exposure_slider.setValue(0)
        self.shadows_slider.setValue(0)
        self.highlights_slider.setValue(0)
        self.clarity_slider.setValue(0)
        self.temperature_slider.setValue(0)
        self.tint_slider.setValue(0)
        self.vibrance_slider.setValue(0)

    def reset_audio_settings(self):
        """重置音频设置"""
        self.volume_slider.setValue(100)
        self.mute_checkbox.setChecked(False)
        self.bass_slider.setValue(0)
        self.treble_slider.setValue(0)
        self.echo_slider.setValue(0)
        self.reverb_slider.setValue(0)
        self.noise_reduction_slider.setValue(0)
        self.voice_enhance_checkbox.setChecked(False)

    def on_media_clicked(self, file_path: str, start_time: float, media_type: str = None):
        """处理媒体块点击事件"""
        print(f"🎯 媒体块被点击: {file_path}, 类型: {media_type}")

        # 🔧 新增：存储当前选中的媒体项信息
        self.current_selected_media = {
            'file_path': file_path,
            'start_time': start_time,
            'media_type': media_type
        }

        # 根据媒体类型显示不同的调节面板
        if media_type == 'video':
            self.show_video_adjustment_mode()
        elif media_type == 'audio':
            self.show_audio_adjustment_mode()
        else:
            # 根据文件扩展名判断类型
            file_ext = file_path.lower().split('.')[-1]
            if file_ext in ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', 'm4v']:
                self.show_video_adjustment_mode()
            elif file_ext in ['mp3', 'wav', 'aac', 'flac', 'ogg', 'm4a']:
                self.show_audio_adjustment_mode()
            else:
                # 默认显示视频调节
                self.show_video_adjustment_mode()

        # 🔧 新增：更新预览以显示当前选中媒体的效果
        self.update_preview_for_selected_media()

    def create_preview_area(self):
        """创建预览区域"""
        preview_widget = QWidget()
        preview_widget.setMinimumSize(600, 500)  # 最小尺寸，但可以缩放
        preview_layout = QVBoxLayout(preview_widget)
        preview_layout.setContentsMargins(0, 0, 0, 0)
        preview_layout.setSpacing(0)

        # 顶部控制栏 - 高度48px，宽度自适应
        top_control_bar = QWidget()
        top_control_bar.setFixedHeight(48)
        top_control_bar.setStyleSheet("""
            QWidget {
                background-color: #333333;
                margin: 0px;
                padding: 0px;
            }
        """)

        top_control_layout = QHBoxLayout(top_control_bar)
        top_control_layout.setContentsMargins(0, 0, 0, 0)  # 完全去除边距
        top_control_layout.setSpacing(12)

        top_control_layout.addStretch()

        # 🔧 新增：预览质量下拉
        quality_label = QLabel("预览质量:")
        quality_label.setStyleSheet("color: #F2ECFF; font-size: 12px; padding: 0px; margin: 0px;")
        top_control_layout.addWidget(quality_label)

        self.preview_quality_combo = QComboBox()
        self.preview_quality_combo.addItems(["原画", "高清", "流畅"])
        self.preview_quality_combo.setCurrentText("原画")
        self.preview_quality_combo.setStyleSheet("""
            QComboBox {
                background-color: #4B4D52;
                color: #F2ECFF;
                border: 1px solid #666666;
                border-radius: 4px;
                padding: 4px 8px;
                min-width: 60px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #F2ECFF;
                margin-right: 8px;
            }
        """)
        self.preview_quality_combo.currentTextChanged.connect(self.on_preview_quality_changed)
        top_control_layout.addWidget(self.preview_quality_combo)

        # 🔧 新增：比例下拉
        ratio_label = QLabel("比例:")
        ratio_label.setStyleSheet("color: #F2ECFF; font-size: 12px; padding: 0px; margin: 0px;")
        top_control_layout.addWidget(ratio_label)

        self.preview_ratio_combo = QComboBox()
        self.preview_ratio_combo.addItems(["16:9", "4:3", "9:16", "1:1", "21:9"])
        self.preview_ratio_combo.setCurrentText("9:16")  # 默认9:16
        self.preview_ratio_combo.setStyleSheet("""
            QComboBox {
                background-color: #4B4D52;
                color: #F2ECFF;
                border: 1px solid #666666;
                border-radius: 4px;
                padding: 4px 8px;
                min-width: 60px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #F2ECFF;
                margin-right: 8px;
            }
        """)
        self.preview_ratio_combo.currentTextChanged.connect(self.on_preview_ratio_changed)
        top_control_layout.addWidget(self.preview_ratio_combo)

        # 导出按钮
        from .media_library import ModernButton
        self.export_btn = ModernButton("导出", "📤", "#3C3C3C")
        self.export_btn.clicked.connect(self.export_video)
        top_control_layout.addWidget(self.export_btn)

        preview_layout.addWidget(top_control_bar)

        # 视频显示区域 - 高度自适应，只包含视频显示
        video_display_container = QWidget()
        video_display_container.setMinimumSize(400, 300)
        video_display_container.setStyleSheet("""
            QWidget {
                background-color: #161616;
                border: none;
                margin: 0px;
                padding: 0px;
            }
        """)

        video_display_layout = QVBoxLayout(video_display_container)
        video_display_layout.setContentsMargins(0, 0, 0, 0)
        video_display_layout.setSpacing(0)

        # 🔧 修改：使用新的视频容器
        self.video_canvas = VideoCanvasContainer()

        # 创建VideoPlayer用于音频播放和控制
        self.video_player = VideoPlayer()
        self.video_player.position_changed.connect(self.on_position_changed)

        # 连接媒体播放器状态改变信号
        if hasattr(self.video_player, 'media_player'):
            self.video_player.media_player.playbackStateChanged.connect(self.on_playback_state_changed)

        # 添加视频容器
        video_display_layout.addWidget(self.video_canvas)

        preview_layout.addWidget(video_display_container)

        # 底部控制栏 - 高度48px，宽度自适应
        bottom_control_bar = QWidget()
        bottom_control_bar.setFixedHeight(48)
        bottom_control_bar.setStyleSheet("""
            QWidget {
                background-color: #333333;
                margin: 0px;
                padding: 0px;
            }
        """)

        bottom_control_layout = QHBoxLayout(bottom_control_bar)
        bottom_control_layout.setContentsMargins(24, 0, 24, 0)  # 左右边距都是24px
        bottom_control_layout.setSpacing(12)

        # 播放/暂停按钮
        self.play_pause_btn = QPushButton("播放")
        self.play_pause_btn.setFixedHeight(48)  # 确保按钮高度为48px
        self.play_pause_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #F2ECFF;
                border: none;
                padding: 0px;
                margin: 0px;
                font-size: 14px;
            }
            QPushButton:hover {
                color: #FFFFFF;
            }
        """)
        self.play_pause_btn.clicked.connect(self.toggle_playback)
        bottom_control_layout.addWidget(self.play_pause_btn)

        # 重置按钮
        self.reset_btn = QPushButton("重置")
        self.reset_btn.setFixedHeight(48)  # 确保按钮高度为48px
        self.reset_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #F2ECFF;
                border: none;
                padding: 0px;
                margin: 0px;
                font-size: 14px;
            }
            QPushButton:hover {
                color: #FFFFFF;
            }
        """)
        self.reset_btn.clicked.connect(self.reset_playback)
        bottom_control_layout.addWidget(self.reset_btn)

        # 倍速控制标签
        speed_label = QLabel("倍速:")
        speed_label.setFixedHeight(48)
        speed_label.setStyleSheet("""
            QLabel {
                color: #F2ECFF;
                font-size: 14px;
                padding: 0px;
                margin: 0px;
            }
        """)
        bottom_control_layout.addWidget(speed_label)

        # 倍速减慢按钮
        self.speed_down_btn = QPushButton("-")
        self.speed_down_btn.setFixedSize(24, 24)
        self.speed_down_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #F2ECFF;
                border: none;
                padding: 0px;
                margin: 0px;
                font-size: 12px;
            }
            QPushButton:hover {
                color: #FFFFFF;
            }
        """)
        self.speed_down_btn.clicked.connect(self.decrease_playback_speed)
        bottom_control_layout.addWidget(self.speed_down_btn)

        # 倍速显示标签
        self.speed_display = QLabel("1.0x")
        self.speed_display.setFixedHeight(48)
        self.speed_display.setMinimumWidth(40)
        self.speed_display.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.speed_display.setStyleSheet("""
            QLabel {
                color: #F2ECFF;
                font-size: 14px;
                padding: 0px;
                margin: 0px;
            }
        """)
        bottom_control_layout.addWidget(self.speed_display)

        # 倍速加快按钮
        self.speed_up_btn = QPushButton("+")
        self.speed_up_btn.setFixedSize(24, 24)
        self.speed_up_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #F2ECFF;
                border: none;
                padding: 0px;
                margin: 0px;
                font-size: 12px;
            }
            QPushButton:hover {
                color: #FFFFFF;
            }
        """)
        self.speed_up_btn.clicked.connect(self.increase_playback_speed)
        bottom_control_layout.addWidget(self.speed_up_btn)

        # 音量控制标签
        volume_label = QLabel("音量:")
        volume_label.setFixedHeight(48)
        volume_label.setStyleSheet("""
            QLabel {
                color: #F2ECFF;
                font-size: 14px;
                padding: 0px;
                margin: 0px;
            }
        """)
        bottom_control_layout.addWidget(volume_label)

        # 音量滑块
        self.volume_slider = QSlider(Qt.Orientation.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(70)
        self.volume_slider.setMaximumWidth(100)
        self.volume_slider.setFixedHeight(48)
        self.volume_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #555555;
                height: 3px;
                background: #555555;
                border-radius: 1px;
            }
            QSlider::handle:horizontal {
                background: #F2ECFF;
                border: 1px solid #F2ECFF;
                width: 14px;
                height: 14px;
                border-radius: 7px;  /* 标准圆形，按照音量滑块高度调整 */
                margin: -6px 0;
            }
            QSlider::handle:horizontal:hover {
                background: #FFFFFF;
                border: 1px solid #FFFFFF;
            }
        """)
        self.volume_slider.valueChanged.connect(self.on_volume_changed)
        bottom_control_layout.addWidget(self.volume_slider)

        # 进度滑块
        self.progress_slider = QSlider(Qt.Orientation.Horizontal)
        self.progress_slider.setRange(0, 1000)  # 设置范围0-1000，便于精确控制
        self.progress_slider.setValue(0)
        self.progress_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #555555;
                height: 3px;
                background: #555555;
                border-radius: 1px;
            }
            QSlider::handle:horizontal {
                background: #F2ECFF;
                border: 1px solid #F2ECFF;
                width: 14px;
                height: 14px;
                border-radius: 7px;  /* 标准圆形 */
                margin: -6px 0;
            }
            QSlider::handle:horizontal:hover {
                background: #FFFFFF;
                border: 1px solid #FFFFFF;
            }
        """)

        # 连接进度滑块事件
        self.progress_slider.sliderPressed.connect(self.on_progress_slider_pressed)
        self.progress_slider.sliderReleased.connect(self.on_progress_slider_released)
        self.progress_slider.valueChanged.connect(self.on_progress_slider_changed)
        self.progress_slider_dragging = False

        bottom_control_layout.addWidget(self.progress_slider)

        # 时间标签
        self.time_label = QLabel("00:00 / 00:00")
        self.time_label.setFixedHeight(48)  # 确保标签高度为48px
        self.time_label.setStyleSheet("""
            QLabel {
                color: #F2ECFF;
                font-size: 14px;
                padding: 0px;
                margin: 0px;
            }
        """)
        bottom_control_layout.addWidget(self.time_label)

        preview_layout.addWidget(bottom_control_bar)

        return preview_widget

    # 原来的菜单栏创建方法已被自定义标题栏替代
    # 菜单功能现在通过自定义标题栏处理

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        self.window_controller.handle_mouse_press(event)
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        self.window_controller.handle_mouse_move(event)
        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        self.window_controller.handle_mouse_release(event)
        super().mouseReleaseEvent(event)

    def mouseDoubleClickEvent(self, event):
        """鼠标双击事件"""
        # 只有在标题栏区域双击才触发最大化
        if self.window_controller.is_in_title_bar_area(event.position().toPoint()):
            self.window_controller.handle_double_click(event)
        super().mouseDoubleClickEvent(event)

    def paintEvent(self, event):
        """自定义绘制事件 - 绘制圆角背景"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)

        # 创建圆角矩形路径
        path = QPainterPath()
        rect = self.rect()
        # 减少一点边距，避免边缘被裁切
        rect.adjust(1, 1, -1, -1)
        path.addRoundedRect(rect, 8, 8)

        # 绘制背景
        painter.fillPath(path, QBrush(QColor(10, 10, 10)))  # #0A0A0A

        # 绘制边框
        painter.strokePath(path, QColor(42, 42, 42))  # #2A2A2A

        # 结束绘制
        painter.end()

        super().paintEvent(event)

    def detect_notch_screen(self, screen):
        """检测是否为刘海屏并进行适配"""
        import platform

        # 检测是否为macOS
        if platform.system() != "Darwin":
            self.has_notch = False
            return

        # 获取屏幕信息
        screen_geometry = screen.geometry()
        available_geometry = screen.availableGeometry()

        # 检测刘海：如果可用区域的顶部与屏幕顶部不一致，可能有刘海
        top_offset = available_geometry.top() - screen_geometry.top()

        # MacBook Pro 14"/16" 刘海屏的特征检测
        # 刘海屏通常有特定的分辨率和顶部偏移
        width = screen_geometry.width()
        height = screen_geometry.height()

        # 常见的MacBook Pro刘海屏分辨率
        notch_resolutions = [
            (3024, 1964),  # 14" MacBook Pro
            (3456, 2234),  # 16" MacBook Pro
            (1512, 982),   # 14" MacBook Pro (scaled)
            (1728, 1117),  # 16" MacBook Pro (scaled)
        ]

        # 检测刘海屏
        self.has_notch = (
            (width, height) in notch_resolutions or
            top_offset > 0  # 有顶部偏移通常表示有刘海或菜单栏
        )

        if self.has_notch:
            print(f"🔍 检测到刘海屏: {width}x{height}, 顶部偏移: {top_offset}px")
            self.setup_notch_adaptation()
        else:
            print(f"📱 普通屏幕: {width}x{height}")

    def setup_notch_adaptation(self):
        """设置刘海屏适配"""
        # 增加标题栏高度以适应刘海
        if hasattr(self, 'title_bar'):
            # 刘海屏需要更高的标题栏
            self.title_bar.setFixedHeight(50)  # 从40px增加到50px

            # 调整标题栏样式以适配刘海
            self.title_bar.setStyleSheet("""
                CustomTitleBar {
                    background-color: #0A0A0A;
                    border-bottom: 1px solid #2A2A2A;
                    border-top-left-radius: 5px;
                    border-top-right-radius: 5px;
                    padding-top: 8px;
                }
            """)

            print("🔧 已应用刘海屏适配：增加标题栏高度和顶部内边距")

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")

    def connect_signals(self):
        """连接信号"""
        pass

    # 占位方法 - 保持与原版一致
    def ensure_default_template(self):
        """确保默认模板存在"""
        pass

    def check_welcome_wizard(self):
        """检查欢迎向导"""
        pass

    def restore_window_state(self):
        """恢复窗口状态"""
        pass

    def check_dependencies(self):
        """检查依赖"""
        pass

    def show_smart_tips(self):
        """显示智能提示"""
        pass

    # 事件处理方法

    def on_position_changed(self, position: float):
        """处理播放位置变化"""
        # 更新时间显示
        current_time = self.format_time(position)

        # 获取总时长
        total_duration = 0
        if hasattr(self.video_player, 'video_info') and self.video_player.video_info:
            total_duration = self.video_player.video_info.duration
            print(f"🕐 从video_info获取时长: {total_duration}s")
        elif hasattr(self.video_player, 'media_player'):
            media_duration = self.video_player.media_player.duration()
            total_duration = media_duration / 1000.0
            print(f"🕐 从media_player获取时长: {media_duration}ms -> {total_duration}s")
        else:
            print("❌ 无法获取视频时长")

        total_time = self.format_time(total_duration)
        time_text = f"{current_time} / {total_time}"
        self.time_label.setText(time_text)
        print(f"🕐 时间显示更新: {time_text}")

        # 更新进度滑块（如果不是用户拖动）
        if not self.progress_slider_dragging and total_duration > 0:
            progress_value = int((position / total_duration) * 1000)
            self.progress_slider.setValue(progress_value)
            print(f"🎯 进度滑块更新: {progress_value}/1000")

    def on_video_position_changed(self, position: float):
        """处理视频位置变化"""
        # 🔧 修复：只有在非播放状态或非更新状态时才同步时间轴位置
        # 播放过程中，时间轴播放器是主要的时间基准，视频播放器只是跟随
        if not self._is_playing and not self._updating_position:
            if hasattr(self.timeline, 'set_position'):
                self.timeline.set_position(position)
                print(f"🎯 视频播放器位置变化同步到时间轴: {position:.2f}s")
        # 播放过程中，视频播放器的位置变化不应该影响时间轴

    # 🔧 移除重复的方法，使用下面完整的on_timeline_position_changed方法

    def on_volume_changed(self, value):
        """处理音量变化"""
        try:
            if hasattr(self.video_player, 'set_volume'):
                self.video_player.set_volume(value / 100.0)
                print(f"🔊 音量设置为: {value}%")
            elif hasattr(self.video_player, 'media_player') and hasattr(self.video_player.media_player, 'setVolume'):
                self.video_player.media_player.setVolume(value)
                print(f"🔊 媒体播放器音量设置为: {value}%")
            # 如果都没有，就忽略音量设置
        except Exception as e:
            print(f"❌ 音量设置失败: {e}")

    def on_progress_slider_pressed(self):
        """进度滑块按下"""
        self.progress_slider_dragging = True
        # 暂停同步更新
        if hasattr(self.video_player, 'on_slider_pressed'):
            self.video_player.on_slider_pressed()
        print("🎯 开始拖动进度滑块")

    def on_progress_slider_released(self):
        """进度滑块释放"""
        self.progress_slider_dragging = False
        # 恢复同步更新
        if hasattr(self.video_player, 'on_slider_released'):
            self.video_player.on_slider_released()
        print("🎯 结束拖动进度滑块")

    def on_progress_slider_changed(self, value):
        """进度滑块值改变 - 使用统一的时间基准"""
        if self.progress_slider_dragging:
            # 🔧 修复：使用全局时间轴的总时长作为基准，而不是单个视频的时长
            total_duration = self.global_params.total_duration
            if total_duration > 0:
                target_position = (value / 1000.0) * total_duration

                # 🔧 修复：使用统一的位置设置方法
                self._updating_position = True
                try:
                    # 设置全局时间轴位置
                    self.global_params.set_current_position(target_position)

                    # 同步时间轴显示
                    if hasattr(self, 'timeline'):
                        self.timeline.set_position(target_position)

                    # 🔧 新设计：播放头通过全局参数自动同步
                    # if hasattr(self, 'unified_playhead'):
                    #     self.unified_playhead.set_position(target_position)
                finally:
                    self._updating_position = False

                print(f"🎯 拖动进度到: {target_position:.2f}s ({value/10.0:.1f}%) (基于时间轴总时长: {total_duration:.1f}s)")

    def update_time_display(self, current_time: float = None, total_duration: float = None):
        """强制更新时间显示 - 使用统一的时间基准"""
        # 🔧 修复：使用全局参数管理器的时间作为统一基准
        if current_time is None:
            current_time = self.global_params.current_position
        if total_duration is None:
            total_duration = self.global_params.total_duration

        current_time_str = self.format_time(current_time)
        total_time_str = self.format_time(total_duration)
        time_text = f"{current_time_str} / {total_time_str}"
        self.time_label.setText(time_text)

        # 🔧 修复：只有在非拖动状态下才更新进度滑块，避免冲突
        if not self.progress_slider_dragging and total_duration > 0:
            progress_value = int((current_time / total_duration) * 1000)
            self.progress_slider.setValue(progress_value)

        # print(f"🕐 强制更新时间显示: {time_text}")  # 减少日志输出

    def load_video(self, file_path: str, start_time: float = 0.0):
        """加载视频文件并设置播放位置"""
        try:
            if file_path.startswith("template:"):
                # 处理模板选择
                self.load_template(file_path)
            else:
                # 处理普通媒体文件
                if hasattr(self.video_player, 'load_video'):
                    # 创建video_info对象
                    class VideoInfo:
                        def __init__(self, duration, file_path, name):
                            self.duration = duration
                            self.file_path = file_path
                            self.name = name

                    duration = self.timeline.get_media_duration(file_path)
                    video_info = VideoInfo(duration, file_path, Path(file_path).name)

                    print(f"🎬 主窗口加载视频: {file_path}, 开始时间: {start_time}s")

                    # 加载视频
                    self.video_player.load_video(file_path, video_info)

                    # 设置播放位置
                    if start_time > 0 and hasattr(self.video_player, 'set_position'):
                        self.video_player.set_position(start_time)

                    # 强制更新时间显示
                    self.update_time_display(start_time, duration)

                self.status_bar.showMessage(f"已加载: {Path(file_path).name}")
        except Exception as e:
            logger.error(f"加载视频失败: {e}")
            print(f"❌ 加载视频失败: {e}")
            QMessageBox.warning(self, "错误", f"加载视频失败: {e}")

    def load_template(self, template_id: str):
        """加载模板到时间轴"""
        try:
            if not template_id:
                QMessageBox.warning(self, "警告", "无效的模板ID")
                return

            # 处理template:前缀
            if template_id.startswith("template:"):
                template_id = template_id.replace("template:", "")

            # 🔧 修改：优先从素材库获取模板数据
            template_data = self.get_template_data_from_library(template_id)
            if template_data:
                # 使用新模板的设置
                template_segments = self.create_segments_from_template_data(template_data)
            else:
                # 回退到默认模板
                template_segments = self.get_template_segments(template_id)

            if not template_segments:
                QMessageBox.warning(self, "警告", f"无法加载模板: {template_id}")
                return

            # 清空当前时间轴但保留轨道结构
            for track in self.timeline.tracks:
                track['media_files'].clear()

            # 确保有足够的轨道
            video_track_index = self.find_track_by_type('video')
            audio_track_index = self.find_track_by_type('audio')

            if video_track_index == -1:
                video_track_index = self.timeline.add_track('video', '视频轨道')
            if audio_track_index == -1:
                audio_track_index = self.timeline.add_track('audio', '音频轨道')

            # 添加模板片段到时间轴（作为占位符或实际媒体）
            for segment in template_segments:
                track_index = video_track_index if segment['type'] == 'video' else audio_track_index

                if track_index < len(self.timeline.tracks):
                    # 🔧 修改：检查是否有预设的媒体文件
                    preset_file = segment.get('video_path') if segment['type'] == 'video' else segment.get('audio_path')

                    if preset_file and os.path.exists(preset_file):
                        # 有预设文件，直接添加实际媒体
                        media_item = {
                            'file_path': preset_file,
                            'start_time': segment['position'],
                            'duration': segment['duration'],
                            'name': segment['name'],
                            'is_placeholder': False,
                            'template_segment': True,
                            'segment_type': segment.get('segment_type', 'normal')
                        }

                        # 如果是音频，添加音量设置
                        if segment['type'] == 'audio':
                            media_item['volume'] = segment.get('volume', 0.6)

                        print(f"添加预设媒体文件: {os.path.basename(preset_file)} 到轨道 {track_index}")
                    else:
                        # 没有预设文件，创建占位符
                        media_item = {
                            'file_path': f"placeholder_{segment['name']}",
                            'start_time': segment['position'],
                            'duration': segment['duration'],
                            'name': segment['name'],
                            'is_placeholder': True,
                            'template_segment': True,
                            'segment_type': segment.get('segment_type', 'normal'),
                            'placeholder_text': f"拖入{segment['type']}文件"
                        }
                        print(f"创建占位符: {segment['name']} 到轨道 {track_index}")

                    self.timeline.tracks[track_index]['media_files'].append(media_item)

            # 更新总时长
            self.timeline.update_total_duration()

            # 更新所有轨道显示
            for i in range(len(self.timeline.tracks)):
                self.update_track_display(i)

            self.status_bar.showMessage(f"已加载模板: {template_id}")
            QMessageBox.information(self, "模板加载完成", f"已加载模板: {template_id}\n包含 {len(template_segments)} 个片段")

        except Exception as e:
            logger.error(f"加载模板失败: {e}")
            QMessageBox.critical(self, "错误", f"加载模板失败:\n{str(e)}")

    def get_template_segments(self, template_id: str):
        """获取模板片段定义"""
        # 简化的模板定义
        templates = {
            "basic_video": [
                {"name": "开场", "type": "video", "position": 0.0, "duration": 5.0, "segment_type": "intro"},
                {"name": "主要内容", "type": "video", "position": 5.0, "duration": 20.0, "segment_type": "main"},
                {"name": "结尾", "type": "video", "position": 25.0, "duration": 5.0, "segment_type": "outro"},
                {"name": "背景音乐", "type": "audio", "position": 0.0, "duration": 30.0, "segment_type": "bgm"}
            ],
            "short_video": [
                {"name": "精彩片段1", "type": "video", "position": 0.0, "duration": 3.0, "segment_type": "highlight"},
                {"name": "精彩片段2", "type": "video", "position": 3.0, "duration": 3.0, "segment_type": "highlight"},
                {"name": "精彩片段3", "type": "video", "position": 6.0, "duration": 4.0, "segment_type": "highlight"},
                {"name": "音效", "type": "audio", "position": 0.0, "duration": 13.0, "segment_type": "sfx"}
            ],
            "tutorial": [
                {"name": "标题", "type": "video", "position": 0.0, "duration": 3.0, "segment_type": "title"},
                {"name": "步骤1", "type": "video", "position": 3.0, "duration": 10.0, "segment_type": "step"},
                {"name": "步骤2", "type": "video", "position": 13.0, "duration": 10.0, "segment_type": "step"},
                {"name": "步骤3", "type": "video", "position": 23.0, "duration": 10.0, "segment_type": "step"},
                {"name": "总结", "type": "video", "position": 33.0, "duration": 4.0, "segment_type": "summary"},
                {"name": "解说音频", "type": "audio", "position": 0.0, "duration": 37.0, "segment_type": "narration"}
            ],
            "默认模板": [
                {"name": "视频片段", "type": "video", "position": 0.0, "duration": 10.0, "segment_type": "main"},
                {"name": "背景音乐", "type": "audio", "position": 0.0, "duration": 10.0, "segment_type": "bgm"}
            ]
        }

        return templates.get(template_id, templates.get("默认模板", []))

    def get_template_data_from_library(self, template_id: str):
        """从素材库获取模板数据"""
        try:
            if hasattr(self, 'media_library') and hasattr(self.media_library, 'templates'):
                for template in self.media_library.templates:
                    if template.get("id") == template_id:
                        return template
            return None
        except Exception as e:
            print(f"获取模板数据失败: {e}")
            return None

    def create_segments_from_template_data(self, template_data):
        """根据模板数据创建片段"""
        try:
            segments = []
            settings = template_data.get("settings", {})

            # 获取视频片段
            video_segments = settings.get("segments", [])
            current_position = 0.0

            for segment in video_segments:
                segments.append({
                    "name": segment.get("name", "视频片段"),
                    "type": "video",
                    "position": current_position,
                    "duration": segment.get("duration", 5.0),
                    "segment_type": "main",
                    "video_path": segment.get("video_path", "")  # 预设视频路径
                })
                current_position += segment.get("duration", 5.0)

            # 获取音频片段
            audio_segments = settings.get("audio_segments", [])
            audio_position = 0.0

            for segment in audio_segments:
                segments.append({
                    "name": segment.get("name", "音频片段"),
                    "type": "audio",
                    "position": audio_position,
                    "duration": settings.get("total_duration", 60.0),  # 音频通常覆盖整个时长
                    "segment_type": "bgm",
                    "audio_path": segment.get("audio_path", ""),  # 预设音频路径
                    "volume": segment.get("volume", 0.6)
                })
                # 音频片段可以重叠，所以不累加位置

            print(f"从模板数据创建了 {len(segments)} 个片段")
            return segments

        except Exception as e:
            print(f"创建模板片段失败: {e}")
            return []

    def find_track_by_type(self, track_type: str):
        """查找指定类型的轨道"""
        for i, track in enumerate(self.timeline.tracks):
            if track['type'] == track_type:
                return i
        return -1

    def show_media_info(self, file_path: str):
        """显示媒体文件信息"""
        self.show_media_info_with_fps(file_path)

    def show_media_info_with_fps(self, file_path: str):
        """显示包含帧率的媒体文件信息"""
        try:
            file_info = Path(file_path)
            if not file_info.exists():
                self.media_info_label.setText("文件不存在")
                return

            # 获取文件基本信息
            size = file_info.stat().st_size
            size_str = self.format_file_size(size)

            # 获取文件扩展名
            ext = file_info.suffix.upper()

            # 构建基本信息文本
            info_text = f"""文件名: {file_info.name}

路径: {file_path}

大小: {size_str}

类型: {ext}"""

            # 🔧 新功能：如果是视频文件，添加视频详细信息包括帧率
            if ext.lower() in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']:
                try:
                    import cv2
                    cap = cv2.VideoCapture(file_path)
                    if cap.isOpened():
                        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        fps = cap.get(cv2.CAP_PROP_FPS)
                        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                        duration = frame_count / fps if fps > 0 else 0

                        info_text += f"""

分辨率: {width} x {height}

帧率: {fps:.2f} FPS

时长: {self.format_time(duration)}

总帧数: {frame_count}"""

                        # 计算比特率
                        if duration > 0:
                            bitrate_kbps = (size * 8) / (duration * 1000)
                            info_text += f"""

比特率: {bitrate_kbps:.0f} kbps"""

                    cap.release()
                except Exception as video_error:
                    print(f"获取视频详细信息失败: {video_error}")
                    info_text += f"""

视频信息: 获取失败"""

            info_text += f"""

修改时间: {self.format_time_stamp(file_info.stat().st_mtime)}"""

            self.media_info_label.setText(info_text)

        except Exception as e:
            self.media_info_label.setText(f"获取文件信息失败: {e}")

    # 工具方法
    def format_file_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"

    def format_time(self, seconds: float) -> str:
        """格式化时间显示"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"

    def format_time_stamp(self, timestamp: float) -> str:
        """格式化时间戳"""
        import datetime
        dt = datetime.datetime.fromtimestamp(timestamp)
        return dt.strftime("%Y-%m-%d %H:%M:%S")

    # 菜单事件处理方法
    def show_swanksalon_dialog(self):
        """显示SWANKSALON对话框"""
        try:
            dialog = SwankSalonDialog(self)
            dialog.exec()
        except Exception as e:
            logger.error(f"打开SWANKSALON对话框失败: {e}")
            QMessageBox.warning(self, "错误", f"打开SWANKSALON对话框失败: {e}")

    def show_template_manager(self):
        """显示模板管理器"""
        try:
            QMessageBox.information(self, "模板管理", "模板管理功能开发中...")
        except Exception as e:
            logger.error(f"打开模板管理器失败: {e}")

    def create_new_template(self):
        """创建新模板"""
        try:
            QMessageBox.information(self, "新建模板", "新建模板功能开发中...")
        except Exception as e:
            logger.error(f"创建新模板失败: {e}")

    def show_batch_processor(self):
        """显示批量处理器"""
        try:
            QMessageBox.information(self, "批量处理", "批量处理功能开发中...")
        except Exception as e:
            logger.error(f"打开批量处理器失败: {e}")

    def show_smart_batch_dialog(self):
        """显示智能批量处理对话框"""
        try:
            QMessageBox.information(self, "智能批量处理", "智能批量处理功能开发中...")
        except Exception as e:
            logger.error(f"打开智能批量处理对话框失败: {e}")

    def auto_detect_highlights(self):
        """自动识别高亮片段"""
        try:
            QMessageBox.information(self, "自动识别高亮片段", "自动识别高亮片段功能开发中...")
        except Exception as e:
            logger.error(f"自动识别高亮片段失败: {e}")

    def import_video(self):
        """导入视频"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "导入视频", "", "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv)"
            )
            if file_path:
                self.load_video(file_path)
        except Exception as e:
            logger.error(f"导入视频失败: {e}")

    def export_video(self):
        """导出视频"""
        try:
            QMessageBox.information(self, "导出视频", "导出功能开发中...")
        except Exception as e:
            logger.error(f"导出视频失败: {e}")

    def cut_selection(self):
        """剪切选择"""
        try:
            QMessageBox.information(self, "剪切", "剪切功能开发中...")
        except Exception as e:
            logger.error(f"剪切失败: {e}")

    def show_enhanced_features(self):
        """显示增强功能面板"""
        try:
            QMessageBox.information(self, "增强功能面板", "增强功能面板开发中...")
        except Exception as e:
            logger.error(f"显示增强功能面板失败: {e}")

    def quick_auto_sync(self):
        """快速自动踩点"""
        try:
            QMessageBox.information(self, "智能自动踩点", "智能自动踩点功能开发中...")
        except Exception as e:
            logger.error(f"自动踩点失败: {e}")

    def quick_ocr_extract(self):
        """快速文字识别"""
        try:
            # 选择视频文件
            video_path, _ = QFileDialog.getOpenFileName(
                self, "选择要识别文字的视频", "",
                "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv);;所有文件 (*)"
            )

            if not video_path:
                return

            # 显示OCR配置对话框
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QSpinBox, QDoubleSpinBox, QPushButton, QCheckBox, QComboBox, QProgressDialog

            class OCRDialog(QDialog):
                def __init__(self, parent=None, video_path=""):
                    super().__init__(parent)
                    self.setWindowTitle("文字识别设置")
                    self.setFixedSize(450, 400)
                    self.parent_window = parent
                    self.video_path = video_path

                    layout = QVBoxLayout(self)

                    # 视频信息
                    layout.addWidget(QLabel(f"视频文件: {os.path.basename(video_path)}"))
                    layout.addWidget(QLabel(""))

                    # 识别方法
                    layout.addWidget(QLabel("识别方法:"))
                    self.method_combo = QComboBox()
                    self.method_combo.addItems(["简单OCR（演示）", "EasyOCR", "Tesseract"])
                    self.method_combo.setCurrentText("简单OCR（演示）")
                    layout.addWidget(self.method_combo)

                    # 采样间隔
                    interval_layout = QHBoxLayout()
                    interval_layout.addWidget(QLabel("采样间隔(秒):"))
                    self.interval_spin = QDoubleSpinBox()
                    self.interval_spin.setRange(0.5, 10.0)
                    self.interval_spin.setValue(2.0)
                    self.interval_spin.setSuffix(" 秒")
                    interval_layout.addWidget(self.interval_spin)
                    layout.addLayout(interval_layout)

                    # 置信度阈值
                    confidence_layout = QHBoxLayout()
                    confidence_layout.addWidget(QLabel("置信度阈值:"))
                    self.confidence_spin = QSpinBox()
                    self.confidence_spin.setRange(50, 100)
                    self.confidence_spin.setValue(80)
                    self.confidence_spin.setSuffix("%")
                    confidence_layout.addWidget(self.confidence_spin)
                    layout.addLayout(confidence_layout)

                    # 语言设置
                    lang_layout = QHBoxLayout()
                    lang_layout.addWidget(QLabel("识别语言:"))
                    self.lang_combo = QComboBox()
                    self.lang_combo.addItems(["中文+英文", "中文", "英文", "日文", "韩文"])
                    lang_layout.addWidget(self.lang_combo)
                    layout.addLayout(lang_layout)

                    # 输出格式
                    layout.addWidget(QLabel("输出格式:"))
                    self.format_combo = QComboBox()
                    self.format_combo.addItems(["SRT字幕", "TXT文本", "JSON数据"])
                    layout.addWidget(self.format_combo)

                    # 选项
                    self.remove_duplicates = QCheckBox("去除重复文字")
                    self.remove_duplicates.setChecked(True)
                    layout.addWidget(self.remove_duplicates)

                    self.merge_lines = QCheckBox("合并相近的文字行")
                    self.merge_lines.setChecked(True)
                    layout.addWidget(self.merge_lines)

                    self.add_timestamps = QCheckBox("添加时间戳")
                    self.add_timestamps.setChecked(True)
                    layout.addWidget(self.add_timestamps)

                    # 按钮
                    button_layout = QHBoxLayout()

                    start_btn = QPushButton("开始识别")
                    start_btn.clicked.connect(self.start_ocr)
                    button_layout.addWidget(start_btn)

                    preview_btn = QPushButton("预览")
                    preview_btn.clicked.connect(self.preview_ocr)
                    button_layout.addWidget(preview_btn)

                    cancel_btn = QPushButton("取消")
                    cancel_btn.clicked.connect(self.close)
                    button_layout.addWidget(cancel_btn)

                    layout.addLayout(button_layout)

                def start_ocr(self):
                    method = self.method_combo.currentText()
                    interval = self.interval_spin.value()
                    confidence = self.confidence_spin.value()
                    language = self.lang_combo.currentText()
                    output_format = self.format_combo.currentText()

                    # 显示进度对话框
                    progress = QProgressDialog("正在识别视频中的文字...", "取消", 0, 100, self)
                    progress.setWindowModality(Qt.WindowModality.WindowModal)
                    progress.show()

                    try:
                        # 简化的OCR处理
                        subtitle_content = self.extract_video_text_simple(
                            self.video_path, interval, confidence, language, output_format
                        )

                        progress.setValue(100)
                        progress.close()

                        if subtitle_content:
                            # 保存文件
                            file_ext = {
                                "SRT字幕": ".srt",
                                "TXT文本": ".txt",
                                "JSON数据": ".json"
                            }.get(output_format, ".srt")

                            save_path, _ = QFileDialog.getSaveFileName(
                                self, "保存识别结果",
                                f"{Path(self.video_path).stem}_ocr{file_ext}",
                                f"{output_format} (*{file_ext});;所有文件 (*)"
                            )

                            if save_path:
                                with open(save_path, 'w', encoding='utf-8') as f:
                                    f.write(subtitle_content)

                                QMessageBox.information(
                                    self, "文字识别完成",
                                    f"识别结果已保存到:\n{save_path}"
                                )
                        else:
                            QMessageBox.information(self, "文字识别完成", "未在视频中检测到文字内容")

                    except Exception as e:
                        progress.close()
                        QMessageBox.critical(self, "错误", f"文字识别失败:\n{str(e)}")

                    self.close()

                def extract_video_text_simple(self, video_path, interval, confidence, language, output_format):
                    """简化的视频文字提取"""
                    try:
                        # 获取视频时长
                        duration = self.parent_window.timeline.get_media_duration(video_path)
                        if duration <= 0:
                            return None

                        # 生成模拟的OCR结果
                        import random
                        sample_texts = [
                            "欢迎来到理发店", "专业剪发服务", "时尚造型设计",
                            "优质服务体验", "预约电话：123-456-7890", "营业时间：9:00-21:00",
                            "新客户优惠", "会员专享", "精品护发", "造型师推荐"
                        ]

                        results = []
                        current_time = 0.0

                        while current_time < duration:
                            # 随机选择是否在这个时间点有文字
                            if random.random() > 0.7:  # 30%概率有文字
                                text = random.choice(sample_texts)
                                end_time = min(current_time + random.uniform(2.0, 5.0), duration)

                                if output_format == "SRT字幕":
                                    results.append(self.format_srt_entry(len(results) + 1, current_time, end_time, text))
                                elif output_format == "TXT文本":
                                    results.append(f"[{self.format_time_srt(current_time)}] {text}")
                                elif output_format == "JSON数据":
                                    results.append({
                                        "start": current_time,
                                        "end": end_time,
                                        "text": text,
                                        "confidence": random.uniform(0.8, 0.95)
                                    })

                            current_time += interval

                        if output_format == "JSON数据":
                            import json
                            return json.dumps(results, ensure_ascii=False, indent=2)
                        else:
                            return "\n\n".join(results) if results else None

                    except Exception as e:
                        print(f"OCR提取失败: {e}")
                        return None

                def format_srt_entry(self, index, start_time, end_time, text):
                    """格式化SRT字幕条目"""
                    start_str = self.format_time_srt(start_time)
                    end_str = self.format_time_srt(end_time)
                    return f"{index}\n{start_str} --> {end_str}\n{text}"

                def format_time_srt(self, seconds):
                    """格式化SRT时间格式"""
                    hours = int(seconds // 3600)
                    minutes = int((seconds % 3600) // 60)
                    secs = int(seconds % 60)
                    millis = int((seconds % 1) * 1000)
                    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millis:03d}"

                def preview_ocr(self):
                    QMessageBox.information(self, "预览", "OCR预览功能开发中...")

            # 显示对话框
            dialog = OCRDialog(self, video_path)
            dialog.exec()

        except Exception as e:
            logger.error(f"OCR提取失败: {e}")
            QMessageBox.critical(self, "错误", f"文字识别失败:\n{str(e)}")

    def quick_audio_analysis(self):
        """快速音频分析"""
        try:
            # 选择音频文件
            audio_path, _ = QFileDialog.getOpenFileName(
                self, "选择要分析的音频文件", "",
                "音频文件 (*.mp3 *.wav *.aac *.m4a *.flac);;所有文件 (*)"
            )

            if not audio_path:
                return

            # 显示进度对话框
            from PySide6.QtWidgets import QProgressDialog
            progress = QProgressDialog("正在分析音频特征...", "取消", 0, 100, self)
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.show()

            try:
                # 分析音频特征
                features = self.analyze_audio_features_simple(audio_path, progress)

                progress.setValue(100)
                progress.close()

                if features:
                    # 显示分析结果对话框
                    self.show_audio_analysis_results(audio_path, features)
                else:
                    QMessageBox.information(self, "分析完成", "音频分析未能获取有效特征")

            except Exception as e:
                progress.close()
                raise e

        except Exception as e:
            logger.error(f"音频分析失败: {e}")
            QMessageBox.critical(self, "错误", f"音频分析失败:\n{str(e)}")

    def analyze_audio_features_simple(self, audio_path, progress=None):
        """简化的音频特征分析"""
        try:
            import random
            import math

            # 获取音频基本信息
            duration = self.timeline.get_media_duration(audio_path)
            if duration <= 0:
                return None

            if progress:
                progress.setValue(20)

            # 模拟音频分析过程
            features = {
                'duration': duration,
                'sample_rate': random.choice([44100, 48000, 22050]),
                'tempo': random.uniform(80, 160),  # BPM
                'key': random.choice(['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']),
                'energy': random.uniform(0.3, 0.9),
                'brightness': random.uniform(1000, 8000),  # Hz
                'roughness': random.uniform(0.1, 0.8),
                'dynamic_range': random.uniform(0.2, 0.7),
                'loudness': random.uniform(-30, -10),  # dB
                'spectral_centroid': random.uniform(1500, 4000),
                'zero_crossing_rate': random.uniform(0.05, 0.15),
                'mfcc_mean': [random.uniform(-50, 50) for _ in range(13)]
            }

            if progress:
                progress.setValue(60)

            # 分析音频类型
            tempo = features['tempo']
            energy = features['energy']

            if tempo < 90 and energy < 0.5:
                audio_type = "慢歌/抒情"
                mood = "平静"
            elif tempo > 130 and energy > 0.7:
                audio_type = "快歌/电子"
                mood = "激动"
            elif 90 <= tempo <= 130:
                audio_type = "中速/流行"
                mood = "愉快"
            else:
                audio_type = "混合风格"
                mood = "中性"

            features['audio_type'] = audio_type
            features['mood'] = mood

            if progress:
                progress.setValue(80)

            # 生成节拍点
            beat_interval = 60.0 / tempo
            beat_points = []
            current_time = 0.0
            while current_time < duration:
                beat_points.append(current_time)
                current_time += beat_interval

            features['beat_points'] = beat_points[:50]  # 限制数量

            if progress:
                progress.setValue(100)

            return features

        except Exception as e:
            print(f"音频特征分析失败: {e}")
            return None

    def show_audio_analysis_results(self, audio_path, features):
        """显示音频分析结果"""
        try:
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTextEdit, QTabWidget, QWidget

            class AudioAnalysisDialog(QDialog):
                def __init__(self, parent=None, audio_path="", features=None):
                    super().__init__(parent)
                    self.setWindowTitle("音频分析结果")
                    self.setFixedSize(600, 500)
                    self.parent_window = parent
                    self.audio_path = audio_path
                    self.features = features or {}

                    layout = QVBoxLayout(self)

                    # 文件信息
                    layout.addWidget(QLabel(f"音频文件: {os.path.basename(audio_path)}"))
                    layout.addWidget(QLabel(""))

                    # 创建标签页
                    tab_widget = QTabWidget()

                    # 基本信息标签页
                    basic_tab = QWidget()
                    basic_layout = QVBoxLayout(basic_tab)

                    basic_info = f"""🎵 基本信息:
• 时长: {self.features.get('duration', 0):.1f} 秒
• 采样率: {self.features.get('sample_rate', 0)} Hz
• 节拍: {self.features.get('tempo', 0):.1f} BPM
• 调性: {self.features.get('key', 'Unknown')}
• 音频类型: {self.features.get('audio_type', 'Unknown')}
• 情绪: {self.features.get('mood', 'Unknown')}

🎶 音频特征:
• 能量: {self.features.get('energy', 0):.2f}
• 亮度: {self.features.get('brightness', 0):.0f} Hz
• 粗糙度: {self.features.get('roughness', 0):.3f}
• 动态范围: {self.features.get('dynamic_range', 0):.3f}
• 响度: {self.features.get('loudness', 0):.1f} dB
• 频谱质心: {self.features.get('spectral_centroid', 0):.0f} Hz
• 过零率: {self.features.get('zero_crossing_rate', 0):.3f}"""

                    basic_text = QTextEdit()
                    basic_text.setPlainText(basic_info)
                    basic_text.setReadOnly(True)
                    basic_layout.addWidget(basic_text)

                    tab_widget.addTab(basic_tab, "基本信息")

                    # 建议标签页
                    suggestion_tab = QWidget()
                    suggestion_layout = QVBoxLayout(suggestion_tab)

                    suggestions = self.generate_suggestions()
                    suggestion_text = QTextEdit()
                    suggestion_text.setPlainText(suggestions)
                    suggestion_text.setReadOnly(True)
                    suggestion_layout.addWidget(suggestion_text)

                    tab_widget.addTab(suggestion_tab, "编辑建议")

                    # 节拍点标签页
                    beat_tab = QWidget()
                    beat_layout = QVBoxLayout(beat_tab)

                    beat_points = self.features.get('beat_points', [])
                    beat_info = f"检测到 {len(beat_points)} 个节拍点:\n\n"
                    for i, beat_time in enumerate(beat_points[:20]):  # 只显示前20个
                        beat_info += f"节拍 {i+1}: {beat_time:.2f}s\n"
                    if len(beat_points) > 20:
                        beat_info += f"... 还有 {len(beat_points) - 20} 个节拍点"

                    beat_text = QTextEdit()
                    beat_text.setPlainText(beat_info)
                    beat_text.setReadOnly(True)
                    beat_layout.addWidget(beat_text)

                    tab_widget.addTab(beat_tab, "节拍点")

                    layout.addWidget(tab_widget)

                    # 按钮区域
                    button_layout = QHBoxLayout()

                    apply_sync_btn = QPushButton("应用自动踩点")
                    apply_sync_btn.clicked.connect(self.apply_auto_sync)
                    button_layout.addWidget(apply_sync_btn)

                    add_to_timeline_btn = QPushButton("添加到时间轴")
                    add_to_timeline_btn.clicked.connect(self.add_to_timeline)
                    button_layout.addWidget(add_to_timeline_btn)

                    save_report_btn = QPushButton("保存报告")
                    save_report_btn.clicked.connect(self.save_report)
                    button_layout.addWidget(save_report_btn)

                    close_btn = QPushButton("关闭")
                    close_btn.clicked.connect(self.close)
                    button_layout.addWidget(close_btn)

                    layout.addLayout(button_layout)

                def generate_suggestions(self):
                    """生成编辑建议"""
                    suggestions = "💡 编辑建议:\n\n"

                    tempo = self.features.get('tempo', 120)
                    energy = self.features.get('energy', 0.5)
                    audio_type = self.features.get('audio_type', '')
                    mood = self.features.get('mood', '')

                    # 节拍建议
                    if tempo < 90:
                        suggestions += "• 慢节奏音乐，适合使用较长的视频片段 (2-4秒)\n"
                        suggestions += "• 建议使用淡入淡出转场效果\n"
                    elif tempo > 130:
                        suggestions += "• 快节奏音乐，适合使用较短的视频片段 (1-2秒)\n"
                        suggestions += "• 建议使用快速切换转场效果\n"
                    else:
                        suggestions += "• 中等节奏音乐，适合使用中等长度的视频片段 (1.5-3秒)\n"
                        suggestions += "• 建议使用标准转场效果\n"

                    suggestions += "\n"

                    # 能量建议
                    if energy > 0.7:
                        suggestions += "• 高能量音乐，适合动感视频内容\n"
                        suggestions += "• 建议增加视觉效果和动画\n"
                    elif energy < 0.4:
                        suggestions += "• 低能量音乐，适合静态或慢动作内容\n"
                        suggestions += "• 建议使用柔和的视觉效果\n"
                    else:
                        suggestions += "• 中等能量音乐，适合一般视频内容\n"

                    suggestions += "\n"

                    # 类型建议
                    if "慢歌" in audio_type:
                        suggestions += "• 抒情音乐，适合情感表达类视频\n"
                        suggestions += "• 建议使用温暖的色调和柔和的光线\n"
                    elif "快歌" in audio_type:
                        suggestions += "• 动感音乐，适合运动或活力类视频\n"
                        suggestions += "• 建议使用鲜艳的色彩和快速的剪辑\n"

                    suggestions += "\n"

                    # 踩点建议
                    beat_count = len(self.features.get('beat_points', []))
                    if beat_count > 0:
                        suggestions += f"• 检测到 {beat_count} 个节拍点，可用于自动踩点剪辑\n"
                        suggestions += "• 建议在强拍处放置重要的视觉元素\n"

                    return suggestions

                def apply_auto_sync(self):
                    """应用自动踩点"""
                    try:
                        # 添加音频到时间轴并应用踩点
                        audio_track_index = self.parent_window.find_track_by_type('audio')
                        if audio_track_index == -1:
                            audio_track_index = self.parent_window.timeline.add_track('audio', '音频轨道')

                        # 创建音频媒体项
                        media_item = {
                            'file_path': self.audio_path,
                            'start_time': 0.0,
                            'duration': self.features.get('duration', 0),
                            'name': os.path.basename(self.audio_path),
                            'beat_points': self.features.get('beat_points', []),
                            'auto_sync_audio': True,
                            'tempo': self.features.get('tempo', 120),
                            'audio_type': self.features.get('audio_type', ''),
                            'mood': self.features.get('mood', '')
                        }

                        # 添加到轨道
                        self.parent_window.timeline.tracks[audio_track_index]['media_files'].append(media_item)

                        # 更新显示
                        self.parent_window.timeline.update_total_duration()
                        self.parent_window.update_track_display(audio_track_index)

                        QMessageBox.information(self, "应用完成", "音频已添加到时间轴并应用自动踩点")
                        self.close()

                    except Exception as e:
                        QMessageBox.critical(self, "错误", f"应用自动踩点失败:\n{str(e)}")

                def add_to_timeline(self):
                    """添加到时间轴"""
                    try:
                        # 简单添加音频到时间轴
                        audio_track_index = self.parent_window.find_track_by_type('audio')
                        if audio_track_index == -1:
                            audio_track_index = self.parent_window.timeline.add_track('audio', '音频轨道')

                        media_item = {
                            'file_path': self.audio_path,
                            'start_time': 0.0,
                            'duration': self.features.get('duration', 0),
                            'name': os.path.basename(self.audio_path)
                        }

                        self.parent_window.timeline.tracks[audio_track_index]['media_files'].append(media_item)
                        self.parent_window.timeline.update_total_duration()
                        self.parent_window.update_track_display(audio_track_index)

                        QMessageBox.information(self, "添加完成", "音频已添加到时间轴")
                        self.close()

                    except Exception as e:
                        QMessageBox.critical(self, "错误", f"添加到时间轴失败:\n{str(e)}")

                def save_report(self):
                    """保存分析报告"""
                    try:
                        save_path, _ = QFileDialog.getSaveFileName(
                            self, "保存分析报告",
                            f"{Path(self.audio_path).stem}_analysis.txt",
                            "文本文件 (*.txt);;所有文件 (*)"
                        )

                        if save_path:
                            with open(save_path, 'w', encoding='utf-8') as f:
                                f.write(f"音频分析报告\n")
                                f.write(f"文件: {self.audio_path}\n")
                                f.write(f"分析时间: {QTime.currentTime().toString()}\n\n")
                                f.write(self.generate_full_report())

                            QMessageBox.information(self, "保存完成", f"分析报告已保存到:\n{save_path}")

                    except Exception as e:
                        QMessageBox.critical(self, "错误", f"保存报告失败:\n{str(e)}")

                def generate_full_report(self):
                    """生成完整报告"""
                    report = "基本信息:\n"
                    for key, value in self.features.items():
                        if key != 'beat_points' and key != 'mfcc_mean':
                            report += f"  {key}: {value}\n"

                    report += "\n编辑建议:\n"
                    report += self.generate_suggestions()

                    return report

            # 显示对话框
            dialog = AudioAnalysisDialog(self, audio_path, features)
            dialog.exec()

        except Exception as e:
            logger.error(f"显示音频分析结果失败: {e}")
            QMessageBox.critical(self, "错误", f"显示分析结果失败:\n{str(e)}")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于 SWANKSALON",
            "SWANKSALON v1.0\n\n专业理发店视频编辑器\n\n基于PySide6开发"
        )

    # 添加一些可能遗漏的菜单功能方法
    def copy_selection(self):
        """复制选择"""
        QMessageBox.information(self, "复制", "复制功能开发中...")

    def paste_selection(self):
        """粘贴选择"""
        QMessageBox.information(self, "粘贴", "粘贴功能开发中...")

    def undo_action(self):
        """撤销操作"""
        QMessageBox.information(self, "撤销", "撤销功能开发中...")

    def redo_action(self):
        """重做操作"""
        QMessageBox.information(self, "重做", "重做功能开发中...")

    def show_help(self):
        """显示帮助文档"""
        QMessageBox.information(self, "帮助", "帮助文档功能开发中...")

    def quick_import_videos(self):
        """快速导入文件夹"""
        QMessageBox.information(self, "快速导入", "快速导入文件夹功能开发中...")

    def preprocess_video(self):
        """视频预处理"""
        QMessageBox.information(self, "视频预处理", "视频预处理功能开发中...")

    def show_effects_panel(self):
        """显示视频特效面板"""
        QMessageBox.information(self, "视频特效", "视频特效功能开发中...")

    def update_track_display(self, track_index: int):
        """更新轨道显示 - 创建VideoThumbnailBlock和AudioWaveformBlock"""
        if not hasattr(self, 'timeline') or track_index >= len(self.timeline.tracks):
            return

        track = self.timeline.tracks[track_index]
        track_widget = self.timeline.track_widgets[track_index]

        # 彻底清除现有内容 - 修复残留问题
        # 清除所有VideoThumbnailBlock
        for child in track_widget.findChildren(VideoThumbnailBlock):
            child.setParent(None)  # 立即解除父子关系
            child.deleteLater()

        # 清除所有AudioWaveformBlock
        for child in track_widget.findChildren(AudioWaveformBlock):
            child.setParent(None)  # 立即解除父子关系
            child.deleteLater()

        # 清除所有QLabel（兼容旧版本）
        for child in track_widget.findChildren(QLabel):
            child.setParent(None)  # 立即解除父子关系
            child.deleteLater()

        # 强制处理挂起的删除操作
        from PySide6.QtWidgets import QApplication
        QApplication.processEvents()

        # 为每个媒体片段创建显示块
        for i, media_item in enumerate(track['media_files']):
            if isinstance(media_item, dict):
                # 新格式：包含时间和位置信息
                file_path = media_item['file_path']
                start_time = media_item['start_time']
                duration = media_item['duration']
                name = media_item['name']
            else:
                # 兼容旧格式：只有文件路径
                file_path = media_item
                start_time = i * 100  # 简单排列
                duration = 10.0
                name = Path(file_path).stem

            # 精确的时间轴对齐计算 - 素材左边缘精确对应时间刻度
            pixels_per_second = self.global_params.pixels_per_second
            x_pos = int(start_time * pixels_per_second)
            width = max(80, int(duration * pixels_per_second))  # 最小宽度80像素

            print(f"素材 {name}: start_time={start_time:.3f}s -> x_pos={x_pos}px (像素比={pixels_per_second})")

            # 根据文件类型创建相应的媒体块
            file_ext = Path(file_path).suffix.lower()
            if file_ext in ['.mp3', '.wav', '.aac', '.m4a', '.flac', '.ogg']:
                # 音频文件使用AudioWaveformBlock
                media_block = AudioWaveformBlock(media_item, track_index, i, self.timeline)
                media_block.setParent(track_widget)
                print(f"Created AudioWaveformBlock for: {name}")
            else:
                # 视频文件使用VideoThumbnailBlock
                media_block = VideoThumbnailBlock(media_item, track_index, i, self.timeline, self.video_processor)
                media_block.setParent(track_widget)
                media_block.lower()  # 降低层级，确保overlay在上面
                if hasattr(track_widget, 'trim_handles_overlay'):
                    track_widget.trim_handles_overlay.raise_()  # overlay始终在最顶层

            # 精确位置：素材左边缘直接对应时间轴刻度，无偏移
            media_block.setGeometry(x_pos, 0, width, 64)  # 高度64px，无上下间距
            media_block.show()

            # 添加时长信息和工具提示
            duration_text = f"{duration:.1f}s"
            media_block.setToolTip(f"{name}\n时长: {duration_text}\n开始: {start_time:.1f}s\n提示: 拖动可重新定位")

            print(f"轨道 {track['name']} 中显示媒体块: {name} 位置({x_pos}, 0) 大小({width}, 64)")

        # 强制轨道组件重绘，确保游标显示
        track_widget.update()

        # 更新专门的游标绘制层
        if hasattr(track_widget, 'update_trim_handles'):
            track_widget.update_trim_handles()

    def on_media_dropped_to_timeline(self, track_index: int, media_item: dict):
        """处理媒体拖放到时间轴的信号"""
        print(f"媒体拖放到时间轴: 轨道{track_index}, 媒体{media_item.get('name', 'Unknown')}")

        # 🔧 修复：不要重复调用update_track_display，handle_track_drop中已经调用过了
        # self.update_track_display(track_index)  # 注释掉重复调用

        # 如果是视频文件，自动预览
        file_path = media_item.get('file_path', '')
        if file_path and Path(file_path).suffix.lower() in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']:
            try:
                # 创建简单的video_info对象
                class VideoInfo:
                    def __init__(self, duration):
                        self.duration = duration

                duration = media_item.get('duration', 0)
                video_info = VideoInfo(duration)

                try:
                    self.video_player.load_video(file_path, video_info)
                except TypeError:
                    # 如果load_video不接受video_info参数，尝试只传file_path
                    try:
                        self.video_player.load_video(file_path)
                    except:
                        # 如果还是失败，就跳过预览
                        pass

                # 跳转到媒体开始时间
                start_time = media_item.get('start_time', 0)
                if hasattr(self.video_player, 'set_position'):
                    self.video_player.set_position(start_time)
                print(f"自动预览视频: {Path(file_path).name} at {start_time:.1f}s")
            except Exception as e:
                print(f"自动预览视频失败: {e}")

    def on_media_file_selected(self, file_path: str):
        """处理媒体库文件选择"""
        try:
            # 在视频播放器中预览
            # 创建简单的video_info对象
            class VideoInfo:
                def __init__(self, duration, file_path, name):
                    self.duration = duration
                    self.file_path = file_path
                    self.name = name

            duration = self.timeline.get_media_duration(file_path)
            video_info = VideoInfo(duration, file_path, Path(file_path).name)

            try:
                self.video_player.load_video(file_path, video_info)
            except TypeError:
                # 如果load_video不接受video_info参数，尝试只传file_path
                try:
                    self.video_player.load_video(file_path)
                except:
                    # 如果还是失败，就跳过预览
                    pass

            print(f"预览媒体文件: {Path(file_path).name}")
        except Exception as e:
            print(f"预览媒体文件失败: {e}")

    def set_position(self, position: float):
        """设置播放位置 - 防止循环调用"""
        # 🔧 修复：防止循环调用
        if self._updating_position:
            return

        self._updating_position = True
        try:
            self.current_position = position

            # 🔧 新设计：播放头通过全局参数自动同步
            # if hasattr(self, 'unified_playhead'):
            #     self.unified_playhead.set_position(position)

            # 更新时间轴位置
            if hasattr(self, 'timeline'):
                self.timeline.set_position(position)

            # 更新视频播放器位置
            if hasattr(self, 'video_player'):
                self.video_player.set_position(position)
        finally:
            self._updating_position = False

    def toggle_playback(self):
        """切换播放/暂停 - 统一播放状态管理"""
        # 🔧 修复：防止状态冲突
        if self._playback_state_lock:
            return

        self._playback_state_lock = True
        try:
            if self._is_playing:
                # 暂停播放
                self._is_playing = False

                # 暂停视频播放器（音频）
                if hasattr(self, 'video_player'):
                    self.video_player.pause()

                # 🔧 修复：暂停所有视频播放器（视频画面）
                if hasattr(self, 'video_canvas'):
                    self.video_canvas.pause_all_videos()

                # 暂停分离的音频播放器
                self.pause_separated_audio()

                # 停止时间轴播放
                self.stop_timeline_playback()

                self.play_pause_btn.setText("播放")
                print("🎵 播放暂停（视频+音频+时间轴）")
            else:
                # 开始播放
                self._is_playing = True

                # 从当前时间轴位置开始播放
                self.play_pause_btn.setText("暂停")
                print("🎵 开始播放（视频+音频+时间轴）")
                self.start_playback_from_current_position()
        finally:
            self._playback_state_lock = False

    def reset_playback(self):
        """重置播放到开始位置"""
        print("🔸 重置播放")

        # 停止播放
        if hasattr(self, 'video_player'):
            self.video_player.stop()

        # 停止时间轴播放
        self.stop_timeline_playback()

        # 重置位置到0
        self.current_position = 0.0
        if hasattr(self, 'timeline'):
            self.timeline.set_position(0.0)

        # 更新按钮状态
        self.play_pause_btn.setText("播放")

        print("✅ 播放已重置到开始位置")

    def increase_playback_speed(self):
        """增加播放速度"""
        self.global_params.increase_playback_speed()

    def decrease_playback_speed(self):
        """减少播放速度"""
        self.global_params.decrease_playback_speed()

    def update_playback_speed(self):
        """更新播放速度显示和实际播放速度 - 委托给全局参数管理器"""
        # 这个方法现在主要由全局参数变化信号驱动
        speed = self.global_params.current_playback_speed

        # 更新视频播放器的播放速度
        if hasattr(self, 'video_player'):
            self.video_player.set_playback_speed(speed)

        # 更新时间轴播放速度
        if hasattr(self, 'timeline_timer'):
            # 调整时间轴更新间隔以匹配播放速度
            base_interval = 50  # 基础间隔50ms (20fps)
            new_interval = max(10, int(base_interval / speed))
            if self.timeline_timer.isActive():
                self.timeline_timer.setInterval(new_interval)

        print(f"✅ 播放速度已更新到: {speed}x")

    def get_first_media(self):
        """获取第一个媒体文件"""
        first_media = None
        min_start_time = float('inf')

        # 使用时间轴的轨道数据
        timeline_tracks = getattr(self.timeline, 'tracks', [])
        print(f"🔍 查找第一个媒体，时间轴轨道数量: {len(timeline_tracks)}")

        for i, track in enumerate(timeline_tracks):
            media_files = track.get('media_files', [])
            print(f"🔍 轨道 {i}: {len(media_files)} 个媒体文件")

            for j, item in enumerate(media_files):
                print(f"🔍 媒体 {j}: {type(item)} - {item}")

                if isinstance(item, dict) and 'start_time' in item and 'file_path' in item:
                    start_time = item['start_time']
                    if start_time < min_start_time:
                        min_start_time = start_time
                        first_media = item
                        print(f"✅ 找到更早的媒体: {item.get('name', 'Unknown')} at {start_time}s")

        if first_media:
            print(f"✅ 第一个媒体: {first_media.get('name', 'Unknown')} at {first_media['start_time']}s")
        else:
            print("❌ 没有找到任何媒体文件")

        return first_media

    def start_playback_from_current_position(self):
        """从当前时间轴位置开始播放 - 支持音视频分离"""
        # 获取当前时间轴位置
        current_timeline_position = self.global_params.current_position

        # 查找当前时间轴位置对应的视频素材
        current_video_media = self.timeline.get_video_media_at_position(current_timeline_position)

        # 查找当前时间轴位置对应的音频素材
        current_audio_media = self.timeline.get_audio_media_at_position(current_timeline_position)

        print(f"🎵 开始播放 - 时间轴位置: {current_timeline_position:.2f}s")
        print(f"🎬 视频素材: {current_video_media['name'] if current_video_media else '无'}")
        print(f"🎵 音频素材: {len(current_audio_media)}个" if current_audio_media else "🎵 音频素材: 无")

        # 🔧 新逻辑：处理视频播放，如果没有素材则显示黑屏
        if current_video_media:
            file_path = current_video_media['file_path']

            # 检查是否是占位符或不存在的文件
            if file_path.startswith('placeholder_') or not os.path.exists(file_path):
                print(f"跳过占位符或不存在的视频文件: {file_path}")
            else:
                # 计算在素材内的相对位置
                media_start_time = current_video_media['start_time']
                relative_position = current_timeline_position - media_start_time

                # 考虑裁剪偏移
                trim_start = current_video_media.get('trim_start', 0)
                actual_position = trim_start + relative_position

                print(f"🎬 播放视频素材: {Path(file_path).name} at {actual_position:.2f}s")

                # 创建video_info对象
                class VideoInfo:
                    def __init__(self, duration, file_path, name):
                        self.duration = duration
                        self.file_path = file_path
                        self.name = name

                duration = current_video_media.get('duration', 0)
                video_info = VideoInfo(duration, file_path, Path(file_path).name)

                # 加载并播放视频
                try:
                    self.video_player.load_video(file_path, video_info)

                    # 🔧 修复：检查视频轨道是否被静音
                    video_track_index = self.find_track_index_by_media(current_video_media)
                    is_video_muted = False
                    if video_track_index >= 0 and video_track_index < len(self.timeline.tracks):
                        is_video_muted = self.timeline.tracks[video_track_index].get('muted', False)

                    if is_video_muted:
                        print("🔇 视频轨道已静音，视频播放器静音")
                        self.video_player.set_volume(0.0)  # 静音视频播放器
                    else:
                        print("🎬 视频轨道正常播放，包含音频")
                        self.video_player.set_volume(1.0)  # 正常音量

                except TypeError:
                    # 如果load_video不接受video_info参数，尝试只传file_path
                    try:
                        self.video_player.load_video(file_path)
                    except:
                        print(f"❌ 无法加载视频: {file_path}")

                if hasattr(self.video_player, 'set_position'):
                    self.video_player.set_position(actual_position)
                if hasattr(self.video_player, 'play'):
                    self.video_player.play()

                # 🔧 新增：同时启动视频画布播放
                if hasattr(self, 'video_canvas'):
                    self.video_canvas.play_all_videos()
        else:
            # 🔧 新逻辑：没有视频素材，停止视频播放器并显示黑屏
            if hasattr(self, 'video_player'):
                self.video_player.stop()
                # 清空视频显示，显示黑屏
                self.video_player.clear_display()
            print("🖤 当前位置无视频素材，显示黑屏")

        # 🔧 修复：处理独立音频文件播放（不包括视频中的音频）
        if current_audio_media:
            print(f"🎵 检测到 {len(current_audio_media)} 个独立音频轨道")

            # 过滤出未静音的音频轨道
            active_audio_tracks = []
            for audio_item in current_audio_media:
                track_index = self.find_track_index_by_media(audio_item)
                if track_index >= 0 and track_index < len(self.timeline.tracks):
                    is_muted = self.timeline.tracks[track_index].get('muted', False)
                    if not is_muted:
                        active_audio_tracks.append(audio_item)

            if active_audio_tracks:
                # 播放第一个未静音的音频轨道（后续可扩展为多轨道混合）
                first_audio = active_audio_tracks[0]
                audio_file_path = first_audio['file_path']
                audio_start_time = first_audio['start_time']

                if os.path.exists(audio_file_path):
                    # 计算音频播放位置
                    audio_relative_position = current_timeline_position - audio_start_time
                    print(f"🎵 播放独立音频轨道: {Path(audio_file_path).name} 位置: {audio_relative_position:.2f}s")

                    # 使用专门的音频播放器播放独立音频
                    self.play_separated_audio(audio_file_path, audio_relative_position)
                else:
                    print(f"❌ 音频文件不存在: {audio_file_path}")
            else:
                print("🔇 所有音频轨道已静音")
                self.stop_separated_audio()
        else:
            print("🎵 当前位置无独立音频素材")
            # 停止独立音频播放器
            self.stop_separated_audio()

        # 启动时间轴播放
        self.start_timeline_playback()

    def play_blank_from_position(self, timeline_position: float):
        """从指定时间轴位置播放空白 - 显示黑屏"""
        print(f"🖤 播放空白区域（黑屏），从时间轴位置: {timeline_position:.2f}s")

        # 停止视频播放器并显示黑屏
        if hasattr(self, 'video_player'):
            self.video_player.stop()
            # 清空视频显示，显示黑屏
            self.video_player.clear_display()

        # 停止分离音频播放
        self.stop_separated_audio()

        # 启动时间轴播放（即使没有视频也要移动播放头）
        self.start_timeline_playback()

    def start_timeline_playback(self):
        """启动主窗口的时间轴播放 - 统一状态管理"""
        # 🔧 修复：统一播放状态
        self._is_playing = True

        if not hasattr(self, 'timeline_timer'):
            self.timeline_timer = QTimer()
            self.timeline_timer.timeout.connect(self.update_timeline_playback)

        if not self.timeline_timer.isActive():
            # 🔧 性能优化：优化更新频率，平衡流畅性和性能
            base_interval = 50  # 恢复到50ms (20fps)，确保播放流畅
            interval = max(33, int(base_interval / self.global_params.current_playback_speed))  # 最小33ms (30fps)
            self.timeline_timer.start(interval)
            print(f"启动时间轴播放，速度: {self.global_params.current_playback_speed}x，间隔: {interval}ms")

            # 初始化性能优化变量
            self._last_sync_time = 0
            self._sync_interval = 0.5  # 每500ms同步一次，保持响应性

    def update_timeline_playback(self):
        """更新主窗口的时间轴播放 - 防止回退优化版"""
        # 🔧 修复：记录上一次位置，防止回退
        if not hasattr(self, '_last_timeline_position'):
            self._last_timeline_position = self.global_params.current_position

        # 🔧 性能优化：根据实际定时器间隔计算时间增量
        timer_interval = self.timeline_timer.interval() / 1000.0  # 转换为秒
        time_increment = timer_interval * self.global_params.current_playback_speed
        new_position = self.global_params.current_position + time_increment

        # 🔧 修复：确保位置单调递增，防止回退
        if new_position >= self._last_timeline_position:
            # 🔧 关键修复：设置更新标志，防止其他播放器的位置变化信号干扰
            self._updating_position = True
            try:
                self.global_params.set_current_position(new_position)
                self._last_timeline_position = new_position

                # 更新时间轴显示
                self.timeline.set_position(new_position)
            finally:
                self._updating_position = False
        else:
            print(f"⚠️ 防止时间轴回退: {new_position:.3f}s < {self._last_timeline_position:.3f}s")

        # 🔧 新逻辑：检查是否到达轨道内容结尾（最后一个素材块的结束时间）
        last_media_end_time = self.timeline.get_last_media_end_time()
        current_position = self.global_params.current_position

        if current_position >= self.global_params.total_duration or (last_media_end_time > 0 and current_position >= last_media_end_time):
            print(f"🏁 播放到达结尾: 当前位置{current_position:.1f}s >= 最后素材结束时间{last_media_end_time:.1f}s")
            self.stop_timeline_playback()
            return

        # 🔧 性能优化：优化同步频率，平衡流畅性和性能
        current_time = self.global_params.current_position
        if not hasattr(self, '_last_sync_time'):
            self._last_sync_time = 0
            self._sync_interval = 0.5  # 每500ms同步一次，保持响应性

        should_sync = (current_time - self._last_sync_time) >= self._sync_interval

        if should_sync:
            self._last_sync_time = current_time

            # 🔧 性能优化：缓存媒体查找结果，避免重复查找，使用适当的缓存阈值
            if not hasattr(self, '_cached_media_position') or abs(self._cached_media_position - current_time) > 0.2:
                self._cached_media_position = current_time
                self._cached_video_media = self.timeline.get_video_media_at_position(current_time)
                self._cached_audio_media = self.timeline.get_audio_media_at_position(current_time)

            # 🔧 修复：改进视频播放器同步逻辑，支持多视频切换
            if self._cached_video_media and hasattr(self, 'video_player'):
                try:
                    # 检查是否需要切换视频文件
                    current_file_path = self._cached_video_media['file_path']
                    current_loaded_file = getattr(self.video_player, 'current_file_path', None)

                    if current_loaded_file != current_file_path:
                        # 需要切换到新的视频文件
                        print(f"🎬 切换视频: {os.path.basename(current_loaded_file or 'None')} -> {os.path.basename(current_file_path)}")

                        if os.path.exists(current_file_path) and not current_file_path.startswith('placeholder_'):
                            # 创建video_info对象
                            class VideoInfo:
                                def __init__(self, duration, file_path, name):
                                    self.duration = duration
                                    self.file_path = file_path
                                    self.name = name

                            duration = self._cached_video_media.get('duration', 0)
                            video_info = VideoInfo(duration, current_file_path, os.path.basename(current_file_path))

                            try:
                                self.video_player.load_video(current_file_path, video_info)
                                # 记录当前加载的文件
                                self.video_player.current_file_path = current_file_path
                            except Exception as load_error:
                                print(f"❌ 视频加载失败: {load_error}")
                                return

                    # 🔧 修复：正确计算视频播放器应该在的位置
                    # 基于时间轴的绝对位置计算相对于当前视频文件的播放位置
                    media_start_time = self._cached_video_media['start_time']
                    relative_position = current_time - media_start_time
                    trim_start = self._cached_video_media.get('trim_start', 0)
                    expected_video_position = trim_start + relative_position

                    # 确保播放位置在视频文件的有效范围内
                    video_duration = self._cached_video_media.get('duration', 0)
                    if expected_video_position < 0:
                        expected_video_position = 0
                    elif expected_video_position > video_duration:
                        expected_video_position = video_duration

                    print(f"🎬 播放位置计算: 时间轴{current_time:.2f}s, 素材开始{media_start_time:.2f}s, 相对位置{relative_position:.2f}s, 视频位置{expected_video_position:.2f}s")

                    # 🔧 修复：确保视频已加载并正在播放
                    if not self.video_player.is_playing():
                        print(f"🎬 启动视频播放: {os.path.basename(current_file_path)}")
                        try:
                            self.video_player.set_position(expected_video_position)
                            self.video_player.play()
                        except Exception as play_error:
                            print(f"❌ 视频播放失败: {play_error}")

                    # 获取视频播放器当前位置
                    current_video_position = getattr(self.video_player, 'current_position', 0.0)

                    # 🔧 性能优化：使用适当的同步阈值，避免频繁调整但保持同步精度
                    if abs(current_video_position - expected_video_position) > 0.3:  # 使用0.3s阈值，平衡精度和性能
                        self.video_player.set_position(expected_video_position)
                        print(f"🎬 视频同步: {current_video_position:.2f}s -> {expected_video_position:.2f}s")
                except Exception as e:
                    print(f"❌ 视频同步失败: {e}")
            elif not self._cached_video_media and hasattr(self, 'video_player') and self.video_player.is_playing():
                # 🔧 修复：如果当前位置没有视频媒体，停止视频播放并显示黑屏
                print("🎬 当前位置无视频媒体，停止播放并显示黑屏")
                self.video_player.pause()
                if hasattr(self.video_player, 'clear_display'):
                    self.video_player.clear_display()

            # 同步音频播放器（降低频率）
            if self._cached_audio_media and hasattr(self, 'separated_audio_player'):
                try:
                    # 同步第一个音频轨道
                    first_audio = self._cached_audio_media[0]
                    audio_start_time = first_audio['start_time']
                    audio_relative_position = current_time - audio_start_time

                    current_audio_pos = self.separated_audio_player.position() / 1000.0
                    expected_audio_pos = audio_relative_position

                    # 🔧 性能优化：进一步增大同步阈值，减少频繁调整
                    if abs(current_audio_pos - expected_audio_pos) > 1.0:  # 从0.5s改为1.0s
                        self.separated_audio_player.setPosition(int(expected_audio_pos * 1000))
                        print(f"🎵 音频同步: {current_audio_pos:.2f}s -> {expected_audio_pos:.2f}s")
                except Exception as e:
                    print(f"❌ 音频同步失败: {e}")

    def stop_timeline_playback(self):
        """停止主窗口的时间轴播放 - 统一状态管理"""
        # 🔧 修复：统一播放状态
        self._is_playing = False

        if hasattr(self, 'timeline_timer') and self.timeline_timer.isActive():
            self.timeline_timer.stop()
            print("停止时间轴播放")

        # 停止视频播放器
        if hasattr(self, 'video_player'):
            self.video_player.pause()

        # 停止分离的音频播放器
        self.stop_separated_audio()

    def get_current_media_at_position(self, position: float):
        """获取指定位置对应的媒体 - 按照原版逻辑（向后兼容）"""
        # 直接使用时间轴的轨道数据
        if not hasattr(self, 'timeline') or not hasattr(self.timeline, 'tracks'):
            return None

        for track in self.timeline.tracks:
            for media_item in track['media_files']:
                if isinstance(media_item, dict):
                    start_time = media_item['start_time']
                    duration = media_item['duration']
                    end_time = start_time + duration

                    if start_time <= position <= end_time:
                        return media_item

        return None

    def get_video_media_at_position(self, position: float):
        """获取指定位置对应的视频媒体"""
        if not hasattr(self, 'timeline') or not hasattr(self.timeline, 'tracks'):
            return None

        for track in self.timeline.tracks:
            if track['type'] == 'video':
                for media_item in track['media_files']:
                    if isinstance(media_item, dict):
                        start_time = media_item['start_time']
                        duration = media_item['duration']
                        end_time = start_time + duration

                        if start_time <= position <= end_time:
                            return media_item

        return None

    def get_audio_media_at_position(self, position: float):
        """获取指定位置对应的所有音频媒体"""
        if not hasattr(self, 'timeline') or not hasattr(self.timeline, 'tracks'):
            return []

        audio_items = []
        for track in self.timeline.tracks:
            if track['type'] == 'audio':
                for media_item in track['media_files']:
                    if isinstance(media_item, dict):
                        start_time = media_item['start_time']
                        duration = media_item['duration']
                        end_time = start_time + duration

                        if start_time <= position <= end_time:
                            audio_items.append(media_item)

        return audio_items

    def play_separated_audio(self, audio_file_path: str, position: float):
        """播放分离的音频文件"""
        try:
            from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
            from PySide6.QtCore import QUrl

            # 创建专门的音频播放器（如果不存在）
            if not hasattr(self, 'separated_audio_player'):
                self.separated_audio_player = QMediaPlayer()
                self.separated_audio_output = QAudioOutput()
                self.separated_audio_player.setAudioOutput(self.separated_audio_output)
                print("🎵 创建分离音频播放器")

            # 停止当前播放
            self.separated_audio_player.stop()

            # 加载音频文件
            audio_url = QUrl.fromLocalFile(os.path.abspath(audio_file_path))
            self.separated_audio_player.setSource(audio_url)

            # 设置播放位置
            if position > 0:
                self.separated_audio_player.setPosition(int(position * 1000))  # 转换为毫秒

            # 开始播放
            self.separated_audio_player.play()
            print(f"🎵 开始播放分离音频: {Path(audio_file_path).name} 位置: {position:.2f}s")

        except Exception as e:
            print(f"❌ 播放分离音频失败: {e}")

    def stop_separated_audio(self):
        """停止分离的音频播放"""
        try:
            if hasattr(self, 'separated_audio_player'):
                self.separated_audio_player.stop()
                print("🎵 停止分离音频播放")
        except Exception as e:
            print(f"❌ 停止分离音频失败: {e}")

    def pause_separated_audio(self):
        """暂停分离的音频播放"""
        try:
            if hasattr(self, 'separated_audio_player'):
                self.separated_audio_player.pause()
                print("🎵 暂停分离音频播放")
        except Exception as e:
            print(f"❌ 暂停分离音频失败: {e}")

    def on_track_mute_changed(self, track_index: int, track_type: str, is_muted: bool):
        """处理轨道静音状态变化"""
        try:
            # 更新轨道数据
            if track_index < len(self.timeline.tracks):
                self.timeline.tracks[track_index]['muted'] = is_muted

                if track_type == 'video':
                    # 🔧 视频轨道静音：影响视频播放器的音频
                    print(f"🔇 视频轨道 {track_index} 静音状态: {is_muted}")
                    # 如果当前正在播放这个轨道的视频，更新音量
                    if hasattr(self, 'video_player'):
                        self.video_player.set_volume(0.0 if is_muted else 1.0)

                    # 同时更新视频画布中的播放器音量
                    if hasattr(self, 'video_canvas'):
                        for video_path, player in self.video_canvas.video_players.items():
                            if hasattr(player, 'video_player_thread'):
                                # 这里需要为DraggableVideoPlayer的播放线程添加音量控制
                                pass

                elif track_type == 'audio':
                    # 🔧 音频轨道静音：影响独立音频播放器
                    print(f"🔇 音频轨道 {track_index} 静音状态: {is_muted}")
                    if hasattr(self, 'separated_audio_player'):
                        if is_muted:
                            self.separated_audio_player.setVolume(0)
                        else:
                            self.separated_audio_player.setVolume(100)

        except Exception as e:
            print(f"❌ 处理轨道静音状态变化失败: {e}")

    def find_track_index_by_media(self, media_item):
        """根据媒体项查找轨道索引"""
        try:
            for track_index, track in enumerate(self.timeline.tracks):
                for media in track.get('media_files', []):
                    if media == media_item:
                        return track_index
            return -1
        except Exception as e:
            print(f"❌ 查找轨道索引失败: {e}")
            return -1

    def export_video(self):
        """导出视频 - 带质量选择的版本"""
        # 检查时间轴是否有内容
        if not self.timeline.tracks or not any(track['media_files'] for track in self.timeline.tracks):
            QMessageBox.information(self, "提示", "时间轴上没有任何媒体文件")
            return

        # 创建导出设置对话框
        dialog = self.create_export_dialog()
        if dialog.exec() == QDialog.DialogCode.Accepted:
            export_settings = dialog.get_export_settings()
            self.perform_timeline_export(export_settings)

    def create_export_dialog(self):
        """创建导出设置对话框"""

        # 🔧 修改：使用自定义对话框，解决头部颜色问题
        dialog = CustomDialog(self, "导出视频设置")
        dialog.setModal(True)
        dialog.resize(400, 300)

        # 🔧 修改：使用自定义对话框的内容布局
        layout = dialog.content_layout

        # 输出文件设置
        file_group = QGroupBox("输出文件")
        file_layout = QGridLayout(file_group)

        file_layout.addWidget(QLabel("保存路径:"), 0, 0)
        path_edit = QLineEdit("output.mp4")
        file_layout.addWidget(path_edit, 0, 1)

        browse_btn = QPushButton("浏览...")
        def browse_file():
            file_path, _ = QFileDialog.getSaveFileName(
                dialog, "导出视频", "output.mp4", "视频文件 (*.mp4);;所有文件 (*)"
            )
            if file_path:
                path_edit.setText(file_path)
        browse_btn.clicked.connect(browse_file)
        file_layout.addWidget(browse_btn, 0, 2)

        layout.addWidget(file_group)

        # 视频质量设置
        quality_group = QGroupBox("视频质量")
        quality_layout = QGridLayout(quality_group)

        quality_layout.addWidget(QLabel("分辨率:"), 0, 0)
        resolution_combo = QComboBox()
        resolution_combo.addItems([
            "480p (854x480)",    # 🔧 新增：添加480p选项
            "720p (1280x720)",
            "1080p (1920x1080)",
            "2K (2560x1440)",
            "4K (3840x2160)"
        ])
        resolution_combo.setCurrentIndex(2)  # 默认1080p（现在是索引2）
        quality_layout.addWidget(resolution_combo, 0, 1)

        quality_layout.addWidget(QLabel("帧率:"), 1, 0)
        fps_combo = QComboBox()
        fps_combo.addItems(["24", "25", "30", "60"])
        fps_combo.setCurrentIndex(2)  # 默认30fps
        quality_layout.addWidget(fps_combo, 1, 1)

        # 🔧 新增：导出比例选择
        quality_layout.addWidget(QLabel("导出比例:"), 2, 0)
        export_ratio_combo = QComboBox()
        export_ratio_combo.addItems(["16:9", "4:3", "9:16", "1:1", "21:9"])
        # 使用当前预览比例作为默认值
        if hasattr(self, 'preview_ratio_combo'):
            export_ratio_combo.setCurrentText(self.preview_ratio_combo.currentText())
        else:
            export_ratio_combo.setCurrentText("9:16")  # 默认9:16
        quality_layout.addWidget(export_ratio_combo, 2, 1)

        quality_layout.addWidget(QLabel("质量:"), 3, 0)
        quality_combo = QComboBox()
        quality_combo.addItems([
            "低质量 (快速)",
            "中等质量 (平衡)",
            "高质量 (慢速)",
            "最高质量 (很慢)"
        ])
        quality_combo.setCurrentIndex(1)  # 默认中等质量
        quality_layout.addWidget(quality_combo, 3, 1)

        layout.addWidget(quality_group)

        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(dialog.reject)
        button_layout.addWidget(cancel_btn)

        export_btn = QPushButton("导出")
        export_btn.clicked.connect(dialog.accept)
        button_layout.addWidget(export_btn)

        layout.addLayout(button_layout)

        # 添加获取设置的方法
        def get_export_settings():
            # 🔧 修改：更新分辨率映射，添加480p
            resolution_map = {
                0: (854, 480),    # 480p
                1: (1280, 720),   # 720p
                2: (1920, 1080),  # 1080p
                3: (2560, 1440),  # 2K
                4: (3840, 2160)   # 4K
            }

            # 🔧 新增：比例映射
            aspect_ratio_map = {
                '16:9': (16, 9),
                '4:3': (4, 3),
                '9:16': (9, 16),
                '1:1': (1, 1),
                '21:9': (21, 9)
            }

            quality_map = {
                0: {'preset': 'ultrafast', 'crf': '28'},  # 低质量
                1: {'preset': 'medium', 'crf': '23'},     # 中等质量
                2: {'preset': 'slow', 'crf': '20'},       # 高质量
                3: {'preset': 'veryslow', 'crf': '18'}    # 最高质量
            }

            base_width, base_height = resolution_map[resolution_combo.currentIndex()]
            quality_settings = quality_map[quality_combo.currentIndex()]

            # 🔧 新增：根据选择的比例调整分辨率
            selected_ratio = export_ratio_combo.currentText()
            aspect_w, aspect_h = aspect_ratio_map[selected_ratio]

            # 根据比例调整分辨率，保持较大的尺寸
            if aspect_w > aspect_h:
                # 横向比例（如16:9, 21:9）
                width = base_width
                height = int(base_width * aspect_h / aspect_w)
            else:
                # 纵向比例（如9:16）或正方形（1:1）
                height = base_height
                width = int(base_height * aspect_w / aspect_h)

            # 确保尺寸是偶数（视频编码要求）
            width = width if width % 2 == 0 else width - 1
            height = height if height % 2 == 0 else height - 1

            # 🔧 修复：获取所有视频的变换参数
            video_transforms = self.get_video_transforms()

            # 🔧 新增：获取视频画布容器的变换信息
            canvas_transforms = {}
            if hasattr(self, 'video_canvas') and hasattr(self.video_canvas, 'get_video_transforms'):
                canvas_transforms = self.video_canvas.get_video_transforms()

            # 合并变换信息
            all_transforms = {**video_transforms, **canvas_transforms}

            return {
                'output_path': path_edit.text(),
                'width': width,
                'height': height,
                'aspect_ratio': selected_ratio,
                'fps': int(fps_combo.currentText()),
                'preset': quality_settings['preset'],
                'crf': quality_settings['crf'],
                'video_transforms': all_transforms  # 所有视频的变换参数
            }

        dialog.get_export_settings = get_export_settings
        return dialog

    def create_progress_dialog(self):
        """创建导出进度对话框"""
        self.progress_dialog = QProgressDialog("正在准备导出...", "取消", 0, 100, self)
        self.progress_dialog.setWindowTitle("导出进度")
        self.progress_dialog.setModal(True)
        self.progress_dialog.setMinimumDuration(0)  # 立即显示
        self.progress_dialog.setValue(0)

        # 🔧 新增：应用统一的弹窗样式
        self.progress_dialog.setStyleSheet(DialogStyleManager.get_dialog_style())

        # 🔧 修复：添加标志来区分用户取消和自动完成
        self.export_cancelled_by_user = False

        # 连接取消按钮
        self.progress_dialog.canceled.connect(self.cancel_export)

        # 显示对话框
        self.progress_dialog.show()

    def cancel_export(self):
        """取消导出"""
        # 🔧 修复：检查是否真的是用户取消
        if hasattr(self, 'export_thread') and self.export_thread.isRunning():
            # 真正的用户取消
            self.export_cancelled_by_user = True
            self.export_thread.terminate()
            self.export_thread.wait()
            print("🎬 用户取消导出")
            QMessageBox.information(self, "提示", "导出已取消")
        else:
            # 可能是进度条自动关闭触发的，不是真正的取消
            print("🎬 进度条自动关闭，不是用户取消")

    def on_export_progress(self, progress):
        """更新导出进度"""
        if hasattr(self, 'progress_dialog'):
            # 🔧 修复：避免设置100%，防止自动关闭触发取消信号
            # 当进度达到100%时，我们在导出完成回调中手动关闭对话框
            if progress >= 100:
                progress = 99  # 设置为99%，避免自动关闭
            self.progress_dialog.setValue(progress)

    def on_export_status(self, status):
        """更新导出状态"""
        if hasattr(self, 'progress_dialog'):
            self.progress_dialog.setLabelText(status)

    def perform_timeline_export(self, settings: dict):
        """执行时间轴导出 - 处理所有轨道的片段"""
        output_path = settings['output_path']

        # 显示进度
        self.statusBar().showMessage("正在导出时间轴...")

        # 使用QThread进行线程安全的导出
        from PySide6.QtCore import QThread, Signal

        class ExportThread(QThread):
            finished_signal = Signal(bool, str)
            error_signal = Signal(str)
            progress_signal = Signal(int)  # 新增：进度信号 (0-100)
            status_signal = Signal(str)    # 新增：状态信号

            def __init__(self, main_window, export_settings):
                super().__init__()
                self.main_window = main_window
                self.export_settings = export_settings
                self.output_path = export_settings['output_path']

            def run(self):
                try:
                    # 收集所有视频轨道的媒体文件
                    video_segments = []
                    for track in self.main_window.timeline.tracks:
                        if track['type'] == 'video':
                            for media_item in track['media_files']:
                                if isinstance(media_item, dict):
                                    file_path = media_item['file_path']
                                    if not file_path.startswith('placeholder_') and os.path.exists(file_path):
                                        video_segments.append({
                                            'file_path': file_path,
                                            'start_time': media_item['start_time'],
                                            'duration': media_item['duration'],
                                            'trim_start': media_item.get('trim_start', 0),
                                            'trim_end': media_item.get('trim_end', 0)
                                        })

                    if not video_segments:
                        self.error_signal.emit("没有找到可导出的视频片段")
                        return

                    # 按时间排序
                    video_segments.sort(key=lambda x: x['start_time'])

                    # 🔧 修复：检查是否可以直接复制（考虑用户导出设置）
                    if len(video_segments) == 1:
                        segment = video_segments[0]
                        trim_start = segment.get('trim_start', 0)
                        trim_end = segment.get('trim_end', 0)

                        # 🔧 重要修复：检查用户导出设置，如果有自定义设置就不能直接复制
                        target_width = self.export_settings.get('width', 1920)
                        target_height = self.export_settings.get('height', 1080)
                        target_fps = self.export_settings.get('fps', 30)

                        # 获取原视频信息进行比较
                        import cv2
                        cap = cv2.VideoCapture(segment['file_path'])
                        original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)) if cap.isOpened() else 0
                        original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)) if cap.isOpened() else 0
                        original_fps = cap.get(cv2.CAP_PROP_FPS) if cap.isOpened() else 0
                        cap.release()

                        # 🔧 修复：检查是否有视频变换（移动、缩放）
                        video_path = segment['file_path']
                        video_transforms = self.export_settings.get('video_transforms', {})
                        has_transforms = False

                        if video_path in video_transforms:
                            transform = video_transforms[video_path]
                            pos_x = transform.get('position', {}).get('x', 0)
                            pos_y = transform.get('position', {}).get('y', 0)
                            scale = transform.get('scale', 1.0)

                            # 检查是否有移动或缩放
                            has_transforms = (pos_x != 0 or pos_y != 0 or abs(scale - 1.0) > 0.01)
                            print(f"🔧 变换检查: 位置({pos_x}, {pos_y}), 缩放{scale:.2f}, 有变换: {has_transforms}")

                        # 检查是否可以直接复制（所有参数都匹配且无裁剪且无变换）
                        can_direct_copy = (
                            trim_start == 0 and
                            trim_end == 0 and
                            not has_transforms and
                            abs(original_fps - target_fps) <= 0.1 and
                            original_width == target_width and
                            original_height == target_height
                        )

                        print(f"🎬 直接复制检查:")
                        print(f"   - 原视频: {original_width}x{original_height}@{original_fps:.1f}fps")
                        print(f"   - 目标: {target_width}x{target_height}@{target_fps}fps")
                        print(f"   - 无裁剪: {trim_start == 0 and trim_end == 0}")
                        print(f"   - 可直接复制: {can_direct_copy}")

                        if can_direct_copy:
                            # 所有参数匹配，可以直接复制
                            import shutil
                            try:
                                shutil.copy2(segment['file_path'], self.output_path)
                                print(f"🎬 直接复制完成: {segment['file_path']} -> {self.output_path}")
                                self.finished_signal.emit(True, "导出完成")
                                return
                            except Exception as copy_error:
                                print(f"🎬 直接复制失败: {copy_error}")
                                # 复制失败，继续使用ffmpeg
                        else:
                            print(f"🎬 需要重新编码，使用ffmpeg处理")

                    # 多片段或需要裁剪，使用ffmpeg
                    self.export_with_ffmpeg(video_segments)

                except Exception as e:
                    self.error_signal.emit(f"导出失败: {str(e)}")

            def export_with_ffmpeg(self, segments):
                """使用ffmpeg导出 - 修复音频同步问题"""
                try:
                    import subprocess
                    import tempfile

                    # 🔧 修复：获取ffmpeg路径
                    ffmpeg_path = 'ffmpeg'  # 默认值
                    if hasattr(self.main_window, 'video_processor') and hasattr(self.main_window.video_processor, 'ffmpeg_path'):
                        ffmpeg_path = self.main_window.video_processor.ffmpeg_path

                    print(f"🎬 使用ffmpeg路径: {ffmpeg_path}")
                    print(f"🎬 导出片段数量: {len(segments)}")

                    # 🔧 修复：使用filter_complex确保音视频同步
                    if len(segments) == 1:
                        # 单个视频，检查是否需要重新编码（帧率或分辨率改变）
                        segment = segments[0]
                        target_width = self.export_settings.get('width', 1920)
                        target_height = self.export_settings.get('height', 1080)
                        target_fps = self.export_settings.get('fps', 30)

                        # 获取原视频信息
                        import cv2
                        cap = cv2.VideoCapture(segment['file_path'])
                        original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)) if cap.isOpened() else 0
                        original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)) if cap.isOpened() else 0
                        original_fps = cap.get(cv2.CAP_PROP_FPS) if cap.isOpened() else 0
                        cap.release()

                        # 🔧 调试信息：显示检测到的视频参数
                        print(f"🎬 原视频参数: {original_width}x{original_height}@{original_fps:.1f}fps")
                        print(f"🎬 目标参数: {target_width}x{target_height}@{target_fps}fps")
                        print(f"🎬 裁剪参数: trim_start={segment.get('trim_start', 0)}, trim_end={segment.get('trim_end', 0)}")

                        # 检查是否需要重新编码
                        fps_different = abs(original_fps - target_fps) > 0.1
                        resolution_different = original_width != target_width or original_height != target_height
                        needs_trim = segment.get('trim_start', 0) > 0 or segment.get('trim_end', 0) > 0

                        # 🔧 修复：如果用户明确选择了不同的参数，强制重新编码
                        # 即使检测到的参数相同，也要重新编码以确保应用用户设置
                        need_reencode = fps_different or resolution_different or needs_trim

                        # 🔧 额外检查：如果原视频信息获取失败，也强制重新编码
                        if original_fps == 0 or original_width == 0 or original_height == 0:
                            need_reencode = True
                            print("🎬 原视频信息获取失败，强制重新编码")

                        print(f"🎬 重新编码检查:")
                        print(f"   - 帧率不同: {fps_different} (原:{original_fps:.1f} vs 目标:{target_fps})")
                        print(f"   - 分辨率不同: {resolution_different} (原:{original_width}x{original_height} vs 目标:{target_width}x{target_height})")
                        print(f"   - 需要裁剪: {needs_trim}")
                        print(f"   - 最终决定: {'需要重新编码' if need_reencode else '直接复制'}")

                        cmd = [ffmpeg_path, '-i', segment['file_path']]

                        # 🔧 重要修复：对于单个视频，如果用户选择了特定的导出参数，总是重新编码
                        # 这确保用户的选择（帧率、分辨率、质量）能够正确应用
                        force_reencode = True  # 强制重新编码以确保用户设置生效

                        if force_reencode or need_reencode:
                            print(f"🎬 重新编码: 原始{original_width}x{original_height}@{original_fps:.1f}fps -> 目标{target_width}x{target_height}@{target_fps}fps")

                            # 添加裁剪参数
                            if segment.get('trim_start', 0) > 0:
                                cmd.extend(['-ss', str(segment['trim_start'])])

                            if segment.get('trim_end', 0) > 0:
                                duration = segment['duration'] - segment.get('trim_start', 0) - segment.get('trim_end', 0)
                                if duration > 0:
                                    cmd.extend(['-t', str(duration)])

                            # 使用用户选择的质量设置
                            preset = self.export_settings.get('preset', 'medium')
                            crf = self.export_settings.get('crf', '23')

                            # 🔧 修复：应用视频变换（移动、缩放、裁剪）
                            video_path = segment['file_path']
                            video_transforms = self.export_settings.get('video_transforms', {})
                            video_filter = ""

                            if video_path in video_transforms:
                                transform = video_transforms[video_path]

                                # 获取变换参数
                                scale = transform.get('scale', 1.0)
                                pos_x = transform.get('position', {}).get('x', 0)
                                pos_y = transform.get('position', {}).get('y', 0)
                                video_width = transform.get('size', {}).get('width', 400)
                                video_height = transform.get('size', {}).get('height', 225)
                                container_width = transform.get('container_size', {}).get('width', 800)
                                container_height = transform.get('container_size', {}).get('height', 600)

                                print(f"🔧 应用视频变换和裁剪: {video_path}")
                                print(f"   - 视频位置: ({pos_x}, {pos_y})")
                                print(f"   - 视频尺寸: {video_width}x{video_height}")
                                print(f"   - 容器尺寸: {container_width}x{container_height}")
                                print(f"   - 缩放比例: {scale:.2f}")

                                # 🔧 关键修复：正确计算视频变换和裁剪
                                # 重新理解裁剪逻辑：
                                # 当视频移动时，我们需要计算容器可视区域对应原始视频的哪一部分

                                print(f"   - 容器可视区域: (0, 0) -> ({container_width}, {container_height})")
                                print(f"   - 视频在容器中的位置: ({pos_x}, {pos_y}) 尺寸 {video_width}x{video_height}")

                                # 🔧 修复：正确计算视频裁剪逻辑
                                # 当视频移动到(pos_x, pos_y)时：
                                # - 如果pos_x > 0，视频向右移动，左边部分移出容器，需要裁剪视频的左边
                                # - 如果pos_y > 0，视频向下移动，上边部分移出容器，需要裁剪视频的上边

                                print(f"   - 分析视频移动: 位置({pos_x}, {pos_y})")

                                # 计算需要从视频中裁剪的区域
                                # 视频移动到(pos_x, pos_y)意味着视频的(0,0)点现在在容器的(pos_x, pos_y)位置
                                # 容器的可视区域(0,0)对应视频的(-pos_x, -pos_y)位置

                                # 但是视频坐标不能为负，所以：
                                crop_left = max(0, -pos_x)    # 如果pos_x>0，需要裁剪左边pos_x像素
                                crop_top = max(0, -pos_y)     # 如果pos_y>0，需要裁剪上边pos_y像素
                                crop_right = min(video_width, container_width - pos_x)   # 右边界
                                crop_bottom = min(video_height, container_height - pos_y) # 下边界

                                # 确保有效的裁剪区域
                                crop_right = max(crop_left, crop_right)
                                crop_bottom = max(crop_top, crop_bottom)

                                crop_width = crop_right - crop_left
                                crop_height = crop_bottom - crop_top

                                print(f"   - 视频裁剪分析:")
                                print(f"     * 左边裁剪: {crop_left}px (pos_x={pos_x})")
                                print(f"     * 上边裁剪: {crop_top}px (pos_y={pos_y})")
                                print(f"     * 裁剪区域: ({crop_left}, {crop_top}) -> ({crop_right}, {crop_bottom})")
                                print(f"     * 裁剪尺寸: {crop_width}x{crop_height}")

                                if crop_width > 0 and crop_height > 0:

                                    # 🔧 修复：将播放器裁剪坐标转换为原始视频坐标
                                    original_video_width = original_width  # 使用前面获取的原始尺寸
                                    original_video_height = original_height

                                    print(f"   - 坐标转换:")
                                    print(f"     * 原始视频尺寸: {original_video_width}x{original_video_height}")
                                    print(f"     * 播放器尺寸: {video_width}x{video_height}")

                                    # 计算播放器相对于原始视频的缩放比例
                                    player_scale_x = video_width / original_video_width
                                    player_scale_y = video_height / original_video_height

                                    print(f"     * 播放器缩放比例: x={player_scale_x:.3f}, y={player_scale_y:.3f}")

                                    # 将播放器裁剪坐标转换为原始视频坐标
                                    original_crop_x = crop_left / player_scale_x
                                    original_crop_y = crop_top / player_scale_y
                                    original_crop_width = crop_width / player_scale_x
                                    original_crop_height = crop_height / player_scale_y

                                    # 转换为FFmpeg的crop参数（相对于原始视频尺寸的比例）
                                    crop_x_ratio = original_crop_x / original_video_width
                                    crop_y_ratio = original_crop_y / original_video_height
                                    crop_width_ratio = original_crop_width / original_video_width
                                    crop_height_ratio = original_crop_height / original_video_height

                                    print(f"   - 最终裁剪参数:")
                                    print(f"     * 原始视频裁剪: x={original_crop_x:.1f}, y={original_crop_y:.1f}, w={original_crop_width:.1f}, h={original_crop_height:.1f}")
                                    print(f"     * FFmpeg裁剪比例: x={crop_x_ratio:.3f}, y={crop_y_ratio:.3f}, w={crop_width_ratio:.3f}, h={crop_height_ratio:.3f}")

                                    # 应用裁剪和缩放到目标尺寸
                                    video_filter = f"crop=iw*{crop_width_ratio:.6f}:ih*{crop_height_ratio:.6f}:iw*{crop_x_ratio:.6f}:ih*{crop_y_ratio:.6f},scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2:black"
                                else:
                                    # 视频完全不可见，创建黑色背景
                                    print(f"   - 视频完全不可见，使用黑色背景")
                                    video_filter = f"color=black:size={target_width}x{target_height}:duration=1"
                            else:
                                # 没有变换信息，使用默认缩放
                                print(f"🔧 使用默认缩放: {video_path}")
                                video_filter = f"scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2:black"

                            cmd.extend([
                                '-vf', video_filter,
                                '-r', str(target_fps),
                                '-c:v', 'libx264',
                                '-c:a', 'aac',
                                '-preset', preset,
                                '-crf', crf,
                                '-y', self.output_path
                            ])
                        else:
                            # 这个分支现在不会被执行，因为我们强制重新编码
                            print(f"🎬 直接复制: 分辨率和帧率匹配，无需重新编码")
                            cmd.extend(['-c', 'copy', '-y', self.output_path])

                    else:
                        # 多个视频，使用filter_complex确保音视频同步并统一分辨率
                        cmd = [ffmpeg_path]

                        # 添加所有输入文件
                        for segment in segments:
                            cmd.extend(['-i', segment['file_path']])

                        # 🔧 修复：使用用户选择的分辨率，确保所有视频兼容
                        target_width = self.export_settings.get('width', 1920)
                        target_height = self.export_settings.get('height', 1080)

                        # 构建filter_complex
                        filter_parts = []

                        for i, segment in enumerate(segments):
                            # 处理视频流：裁剪 + 缩放 + 时间戳重置
                            video_filter = f"[{i}:v]"

                            # 添加裁剪
                            if segment['trim_start'] > 0 or segment['trim_end'] > 0:
                                trim_start = segment['trim_start']
                                duration = segment['duration'] - segment['trim_start'] - segment['trim_end']
                                if duration > 0:
                                    video_filter += f"trim=start={trim_start}:duration={duration},"

                            # 🔧 修复：应用视频变换（裁剪可视区域）
                            video_path = segment['file_path']
                            video_transforms = self.export_settings.get('video_transforms', {})

                            if video_path in video_transforms:
                                transform = video_transforms[video_path]

                                # 获取变换参数
                                scale = transform.get('scale', 1.0)
                                pos_x = transform.get('position', {}).get('x', 0)
                                pos_y = transform.get('position', {}).get('y', 0)
                                video_width = transform.get('size', {}).get('width', 400)
                                video_height = transform.get('size', {}).get('height', 225)
                                container_width = transform.get('container_size', {}).get('width', 800)
                                container_height = transform.get('container_size', {}).get('height', 600)

                                print(f"🔧 应用视频变换和裁剪: {video_path}")
                                print(f"   - 视频位置: ({pos_x}, {pos_y})")
                                print(f"   - 视频尺寸: {video_width}x{video_height}")
                                print(f"   - 容器尺寸: {container_width}x{container_height}")
                                print(f"   - 缩放比例: {scale:.2f}")

                                # 🔧 关键修复：正确计算视频变换和裁剪
                                # 计算视频在容器中的位置和可见区域

                                # 容器中的可视区域边界
                                container_left = 0
                                container_top = 0
                                container_right = container_width
                                container_bottom = container_height

                                # 视频在容器中的边界
                                video_left = pos_x
                                video_top = pos_y
                                video_right = pos_x + video_width
                                video_bottom = pos_y + video_height

                                print(f"   - 容器区域: (0, 0) -> ({container_width}, {container_height})")
                                print(f"   - 视频区域: ({video_left}, {video_top}) -> ({video_right}, {video_bottom})")

                                # 计算交集（可见区域）
                                visible_left = max(container_left, video_left)
                                visible_top = max(container_top, video_top)
                                visible_right = min(container_right, video_right)
                                visible_bottom = min(container_bottom, video_bottom)

                                visible_width = max(0, visible_right - visible_left)
                                visible_height = max(0, visible_bottom - visible_top)

                                print(f"   - 可见区域: ({visible_left}, {visible_top}) -> ({visible_right}, {visible_bottom})")
                                print(f"   - 可见尺寸: {visible_width}x{visible_height}")

                                if visible_width > 0 and visible_height > 0:
                                    # 计算在原始视频中需要裁剪的区域
                                    # 相对于视频左上角的偏移
                                    crop_x = visible_left - video_left  # 在视频中的X偏移
                                    crop_y = visible_top - video_top    # 在视频中的Y偏移
                                    crop_width = visible_width          # 裁剪宽度
                                    crop_height = visible_height        # 裁剪高度

                                    # 🔧 修复：获取原始视频尺寸用于裁剪计算
                                    # 从媒体项获取原始视频尺寸
                                    original_video_width = 960  # 默认值
                                    original_video_height = 540  # 默认值

                                    # 尝试从媒体项获取真实的原始视频尺寸
                                    for media_item in self.timeline.tracks[track_index].media_items:
                                        if media_item.file_path == video_path:
                                            if hasattr(media_item, 'video_width') and hasattr(media_item, 'video_height'):
                                                original_video_width = media_item.video_width
                                                original_video_height = media_item.video_height
                                            break

                                    print(f"   - 原始视频尺寸: {original_video_width}x{original_video_height}")
                                    print(f"   - 播放器尺寸: {video_width}x{video_height}")

                                    # 计算播放器相对于原始视频的缩放比例
                                    player_scale_x = video_width / original_video_width
                                    player_scale_y = video_height / original_video_height

                                    # 将播放器坐标转换为原始视频坐标
                                    original_crop_x = crop_x / player_scale_x
                                    original_crop_y = crop_y / player_scale_y
                                    original_crop_width = crop_width / player_scale_x
                                    original_crop_height = crop_height / player_scale_y

                                    # 转换为FFmpeg的crop参数（相对于原始视频尺寸的比例）
                                    crop_x_ratio = original_crop_x / original_video_width
                                    crop_y_ratio = original_crop_y / original_video_height
                                    crop_width_ratio = original_crop_width / original_video_width
                                    crop_height_ratio = original_crop_height / original_video_height

                                    print(f"   - 裁剪参数: x={crop_x}, y={crop_y}, w={crop_width}, h={crop_height}")
                                    print(f"   - 裁剪比例: x={crop_x_ratio:.3f}, y={crop_y_ratio:.3f}, w={crop_width_ratio:.3f}, h={crop_height_ratio:.3f}")

                                    # 应用裁剪和缩放到目标尺寸
                                    video_filter += f"crop=iw*{crop_width_ratio:.6f}:ih*{crop_height_ratio:.6f}:iw*{crop_x_ratio:.6f}:ih*{crop_y_ratio:.6f},"
                                    video_filter += f"scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,"
                                    video_filter += f"pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2:black,"
                                else:
                                    # 视频完全不可见，创建黑色背景
                                    print(f"   - 视频完全不可见，使用黑色背景")
                                    video_filter += f"color=black:size={target_width}x{target_height}:duration=1,"
                            else:
                                # 没有变换信息，使用默认缩放
                                print(f"🔧 使用默认缩放: {video_path}")
                                video_filter += f"scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,"
                                video_filter += f"pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2:black,"

                            video_filter += "setpts=PTS-STARTPTS"
                            video_filter += f"[v{i}]"
                            filter_parts.append(video_filter)

                            # 处理音频流：裁剪 + 时间戳重置
                            audio_filter = f"[{i}:a]"
                            if segment['trim_start'] > 0 or segment['trim_end'] > 0:
                                trim_start = segment['trim_start']
                                duration = segment['duration'] - segment['trim_start'] - segment['trim_end']
                                if duration > 0:
                                    audio_filter += f"atrim=start={trim_start}:duration={duration},"

                            audio_filter += "asetpts=PTS-STARTPTS"
                            audio_filter += f"[a{i}]"
                            filter_parts.append(audio_filter)

                        # 拼接所有视频和音频流
                        video_inputs = "".join([f"[v{i}]" for i in range(len(segments))])
                        audio_inputs = "".join([f"[a{i}]" for i in range(len(segments))])

                        filter_parts.append(f"{video_inputs}concat=n={len(segments)}:v=1:a=0[outv]")
                        filter_parts.append(f"{audio_inputs}concat=n={len(segments)}:v=0:a=1[outa]")

                        filter_complex = ";".join(filter_parts)

                        print(f"🎬 Filter complex: {filter_complex}")

                        # 使用用户选择的质量设置
                        preset = self.export_settings.get('preset', 'medium')
                        crf = self.export_settings.get('crf', '23')
                        fps = self.export_settings.get('fps', 30)

                        cmd.extend([
                            '-filter_complex', filter_complex,
                            '-map', '[outv]',
                            '-map', '[outa]',
                            '-c:v', 'libx264',
                            '-c:a', 'aac',
                            '-preset', preset,
                            '-crf', crf,
                            '-r', str(fps),
                            '-y', self.output_path
                        ])

                    print(f"🎬 执行命令: {' '.join(cmd)}")

                    # 🔧 新功能：使用实时进度监控
                    self.status_signal.emit("正在导出视频...")
                    self.progress_signal.emit(0)

                    # 计算总时长用于进度计算
                    total_duration = sum(seg.get('duration', 0) - seg.get('trim_start', 0) - seg.get('trim_end', 0) for seg in segments)

                    try:
                        # 使用Popen进行实时输出监控
                        process = subprocess.Popen(
                            cmd,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            text=True,
                            encoding='utf-8',
                            errors='ignore',
                            universal_newlines=True
                        )

                        # 监控ffmpeg输出
                        while True:
                            output = process.stderr.readline()
                            if output == '' and process.poll() is not None:
                                break
                            if output:
                                # 解析ffmpeg进度信息
                                if 'time=' in output:
                                    try:
                                        # 提取时间信息 (格式: time=00:01:23.45)
                                        time_match = output.split('time=')[1].split()[0]
                                        time_parts = time_match.split(':')
                                        if len(time_parts) == 3:
                                            hours = float(time_parts[0])
                                            minutes = float(time_parts[1])
                                            seconds = float(time_parts[2])
                                            current_time = hours * 3600 + minutes * 60 + seconds

                                            if total_duration > 0:
                                                progress = min(int((current_time / total_duration) * 100), 100)
                                                self.progress_signal.emit(progress)
                                                self.status_signal.emit(f"正在导出视频... {progress}%")
                                    except:
                                        pass

                        # 等待进程完成
                        return_code = process.wait()

                        if return_code == 0:
                            self.progress_signal.emit(100)
                            self.status_signal.emit("导出完成")
                            print(f"🎬 导出成功: {self.output_path}")
                            self.finished_signal.emit(True, "导出完成")
                        else:
                            stderr_output = process.stderr.read() if process.stderr else "未知错误"
                            print(f"🎬 ffmpeg错误: {stderr_output}")
                            self.error_signal.emit(f"ffmpeg错误: {stderr_output}")

                    except Exception as process_error:
                        print(f"🎬 进程执行错误: {process_error}")
                        self.error_signal.emit(f"进程执行错误: {process_error}")

                except Exception as e:
                    print(f"🎬 导出异常: {str(e)}")
                    self.error_signal.emit(f"ffmpeg导出失败: {str(e)}")

        # 🔧 新功能：创建进度条对话框
        self.create_progress_dialog()

        # 创建并启动导出线程
        self.export_thread = ExportThread(self, settings)
        self.export_thread.finished_signal.connect(self.on_export_finished)
        self.export_thread.error_signal.connect(self.on_export_error)
        self.export_thread.progress_signal.connect(self.on_export_progress)  # 连接进度信号
        self.export_thread.status_signal.connect(self.on_export_status)      # 连接状态信号
        self.export_thread.start()

    def on_export_finished(self, success: bool, message: str):
        """导出完成处理"""
        # 🔧 修复：正确关闭进度条，避免触发取消信号
        if hasattr(self, 'progress_dialog'):
            # 先断开取消信号连接，避免关闭时触发
            self.progress_dialog.canceled.disconnect()
            # 设置为100%并关闭
            self.progress_dialog.setValue(100)
            self.progress_dialog.close()

        self.statusBar().showMessage(message)
        if success:
            output_path = self.export_thread.output_path if hasattr(self, 'export_thread') else "未知位置"
            QMessageBox.information(self, "导出完成", f"视频已成功导出！\n\n文件保存位置:\n{output_path}")
        else:
            QMessageBox.warning(self, "导出失败", message)

    def on_export_error(self, error_message: str):
        """导出错误处理"""
        # 🔧 修复：正确关闭进度条，避免触发取消信号
        if hasattr(self, 'progress_dialog'):
            # 先断开取消信号连接，避免关闭时触发
            self.progress_dialog.canceled.disconnect()
            self.progress_dialog.close()

        self.statusBar().showMessage("导出失败")
        QMessageBox.critical(self, "导出错误", error_message)

    def show_swanksalon_dialog(self):
        """显示SWANKSALON专业剪辑对话框"""
        try:
            dialog = SwankSalonDialog(self)
            dialog.exec()
        except Exception as e:
            QMessageBox.information(self, "SWANKSALON", f"SWANKSALON功能开发中...\n{e}")

    def show_template_manager(self):
        """显示模板管理对话框"""
        try:
            QMessageBox.information(self, "模板管理", "模板管理功能开发中...")
        except Exception as e:
            logger.error(f"模板管理失败: {e}")

    def create_new_template(self):
        """创建新模板"""
        try:
            QMessageBox.information(self, "新建模板", "新建模板功能开发中...")
        except Exception as e:
            logger.error(f"创建模板失败: {e}")

    def show_batch_processor(self):
        """显示批量处理对话框"""
        try:
            # 创建简化的批量处理对话框
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QListWidget, QFileDialog, QProgressBar, QTextEdit

            class BatchProcessorDialog(QDialog):
                def __init__(self, parent=None):
                    super().__init__(parent)
                    self.setWindowTitle("批量处理")
                    self.setFixedSize(600, 500)
                    self.parent_window = parent

                    layout = QVBoxLayout(self)

                    # 文件列表
                    layout.addWidget(QLabel("选择要处理的视频文件:"))
                    self.file_list = QListWidget()
                    layout.addWidget(self.file_list)

                    # 按钮区域
                    btn_layout = QHBoxLayout()

                    add_files_btn = QPushButton("添加文件")
                    add_files_btn.clicked.connect(self.add_files)
                    btn_layout.addWidget(add_files_btn)

                    add_folder_btn = QPushButton("添加文件夹")
                    add_folder_btn.clicked.connect(self.add_folder)
                    btn_layout.addWidget(add_folder_btn)

                    clear_btn = QPushButton("清空列表")
                    clear_btn.clicked.connect(self.clear_list)
                    btn_layout.addWidget(clear_btn)

                    layout.addLayout(btn_layout)

                    # 处理选项
                    layout.addWidget(QLabel("处理选项:"))

                    process_layout = QHBoxLayout()

                    auto_cut_btn = QPushButton("自动剪辑")
                    auto_cut_btn.clicked.connect(self.auto_cut_videos)
                    process_layout.addWidget(auto_cut_btn)

                    apply_template_btn = QPushButton("应用模板")
                    apply_template_btn.clicked.connect(self.apply_template_batch)
                    process_layout.addWidget(apply_template_btn)

                    export_all_btn = QPushButton("批量导出")
                    export_all_btn.clicked.connect(self.export_all)
                    process_layout.addWidget(export_all_btn)

                    layout.addLayout(process_layout)

                    # 进度条
                    self.progress_bar = QProgressBar()
                    self.progress_bar.setVisible(False)
                    layout.addWidget(self.progress_bar)

                    # 日志区域
                    layout.addWidget(QLabel("处理日志:"))
                    self.log_text = QTextEdit()
                    self.log_text.setMaximumHeight(100)
                    layout.addWidget(self.log_text)

                    # 关闭按钮
                    close_btn = QPushButton("关闭")
                    close_btn.clicked.connect(self.close)
                    layout.addWidget(close_btn)

                def add_files(self):
                    files, _ = QFileDialog.getOpenFileNames(
                        self, "选择视频文件", "",
                        "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv);;所有文件 (*)"
                    )
                    for file in files:
                        self.file_list.addItem(file)
                    self.log(f"添加了 {len(files)} 个文件")

                def add_folder(self):
                    folder = QFileDialog.getExistingDirectory(self, "选择文件夹")
                    if folder:
                        video_files = self.scan_video_files(folder)
                        for file in video_files:
                            self.file_list.addItem(file)
                        self.log(f"从文件夹添加了 {len(video_files)} 个视频文件")

                def scan_video_files(self, folder):
                    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']
                    video_files = []
                    for root, dirs, files in os.walk(folder):
                        for file in files:
                            if any(file.lower().endswith(ext) for ext in video_extensions):
                                video_files.append(os.path.join(root, file))
                    return video_files

                def clear_list(self):
                    self.file_list.clear()
                    self.log("清空文件列表")

                def auto_cut_videos(self):
                    if self.file_list.count() == 0:
                        QMessageBox.information(self, "提示", "请先添加视频文件")
                        return

                    self.log("开始自动剪辑...")
                    self.progress_bar.setVisible(True)
                    self.progress_bar.setRange(0, self.file_list.count())

                    for i in range(self.file_list.count()):
                        file_path = self.file_list.item(i).text()
                        self.log(f"处理文件: {os.path.basename(file_path)}")

                        # 简化的自动剪辑：添加到时间轴
                        if self.parent_window:
                            self.parent_window.media_library.add_media_item(file_path)

                        self.progress_bar.setValue(i + 1)
                        QApplication.processEvents()

                    self.log("自动剪辑完成")
                    self.progress_bar.setVisible(False)

                def apply_template_batch(self):
                    if self.file_list.count() == 0:
                        QMessageBox.information(self, "提示", "请先添加视频文件")
                        return

                    # 选择模板
                    templates = ["basic_video", "short_video", "tutorial"]
                    template, ok = QInputDialog.getItem(
                        self, "选择模板", "请选择要应用的模板:", templates, 0, False
                    )

                    if ok and template:
                        self.log(f"应用模板: {template}")
                        if self.parent_window:
                            self.parent_window.load_template(template)
                        self.log("模板应用完成")

                def export_all(self):
                    if self.file_list.count() == 0:
                        QMessageBox.information(self, "提示", "请先添加视频文件")
                        return

                    output_dir = QFileDialog.getExistingDirectory(self, "选择输出目录")
                    if output_dir:
                        self.log(f"批量导出到: {output_dir}")
                        # 简化的批量导出
                        QMessageBox.information(self, "批量导出", "批量导出功能开发中...")

                def log(self, message):
                    self.log_text.append(f"[{QTime.currentTime().toString()}] {message}")

            # 显示对话框
            dialog = BatchProcessorDialog(self)
            dialog.exec()

        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            QMessageBox.critical(self, "错误", f"批量处理失败:\n{str(e)}")

    def show_smart_batch_dialog(self):
        """显示智能批量处理对话框"""
        try:
            QMessageBox.information(self, "智能批量处理", "智能批量处理功能开发中...")
        except Exception as e:
            logger.error(f"智能批量处理失败: {e}")

    def auto_detect_highlights(self):
        """自动识别高亮片段"""
        try:
            # 检查是否有加载的视频
            if not hasattr(self.video_player, 'current_video') or not self.video_player.current_video:
                QMessageBox.information(self, "提示", "请先加载一个视频文件")
                return

            video_path = self.video_player.current_video

            # 显示配置对话框
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QSpinBox, QDoubleSpinBox, QPushButton, QCheckBox

            class HighlightDetectionDialog(QDialog):
                def __init__(self, parent=None):
                    super().__init__(parent)
                    self.setWindowTitle("高亮片段识别设置")
                    self.setFixedSize(400, 300)
                    self.parent_window = parent

                    layout = QVBoxLayout(self)

                    # 视频信息
                    layout.addWidget(QLabel(f"视频文件: {os.path.basename(video_path)}"))
                    layout.addWidget(QLabel(""))

                    # 最小片段时长
                    duration_layout = QHBoxLayout()
                    duration_layout.addWidget(QLabel("最小片段时长(秒):"))
                    self.min_duration_spin = QDoubleSpinBox()
                    self.min_duration_spin.setRange(1.0, 30.0)
                    self.min_duration_spin.setValue(3.0)
                    self.min_duration_spin.setSuffix(" 秒")
                    duration_layout.addWidget(self.min_duration_spin)
                    layout.addLayout(duration_layout)

                    # 最大片段数量
                    count_layout = QHBoxLayout()
                    count_layout.addWidget(QLabel("最大片段数量:"))
                    self.max_count_spin = QSpinBox()
                    self.max_count_spin.setRange(1, 20)
                    self.max_count_spin.setValue(5)
                    count_layout.addWidget(self.max_count_spin)
                    layout.addLayout(count_layout)

                    # 检测方法
                    layout.addWidget(QLabel("检测方法:"))
                    self.method_random = QCheckBox("随机选择片段（演示模式）")
                    self.method_random.setChecked(True)
                    layout.addWidget(self.method_random)

                    self.method_interval = QCheckBox("等间隔选择片段")
                    layout.addWidget(self.method_interval)

                    # 自动添加到时间轴
                    self.auto_add_check = QCheckBox("自动添加识别的片段到时间轴")
                    self.auto_add_check.setChecked(True)
                    layout.addWidget(self.auto_add_check)

                    # 按钮
                    button_layout = QHBoxLayout()

                    start_btn = QPushButton("开始识别")
                    start_btn.clicked.connect(self.start_detection)
                    button_layout.addWidget(start_btn)

                    cancel_btn = QPushButton("取消")
                    cancel_btn.clicked.connect(self.close)
                    button_layout.addWidget(cancel_btn)

                    layout.addLayout(button_layout)

                def start_detection(self):
                    min_duration = self.min_duration_spin.value()
                    max_count = self.max_count_spin.value()
                    auto_add = self.auto_add_check.isChecked()

                    # 简化的高亮片段检测
                    highlights = self.detect_highlights_simple(video_path, min_duration, max_count)

                    if highlights:
                        if auto_add:
                            self.add_highlights_to_timeline(highlights)

                        QMessageBox.information(
                            self, "检测完成",
                            f"检测到 {len(highlights)} 个高亮片段"
                        )
                    else:
                        QMessageBox.information(self, "检测完成", "未检测到高亮片段")

                    self.close()

                def detect_highlights_simple(self, video_path, min_duration, max_count):
                    """简化的高亮片段检测"""
                    try:
                        # 获取视频总时长
                        duration = self.parent_window.timeline.get_media_duration(video_path)
                        if duration <= 0:
                            return []

                        highlights = []

                        if self.method_random.isChecked():
                            # 随机选择片段
                            import random
                            for i in range(max_count):
                                start_time = random.uniform(0, max(0, duration - min_duration))
                                highlights.append({
                                    'start_time': start_time,
                                    'duration': min_duration,
                                    'score': random.uniform(0.7, 1.0),
                                    'type': 'random'
                                })

                        elif self.method_interval.isChecked():
                            # 等间隔选择片段
                            interval = duration / (max_count + 1)
                            for i in range(max_count):
                                start_time = (i + 1) * interval - min_duration / 2
                                start_time = max(0, min(start_time, duration - min_duration))
                                highlights.append({
                                    'start_time': start_time,
                                    'duration': min_duration,
                                    'score': 0.8,
                                    'type': 'interval'
                                })

                        return highlights

                    except Exception as e:
                        print(f"检测高亮片段失败: {e}")
                        return []

                def add_highlights_to_timeline(self, highlights):
                    """将高亮片段添加到时间轴"""
                    try:
                        # 确保有视频轨道
                        video_track_index = self.parent_window.find_track_by_type('video')
                        if video_track_index == -1:
                            video_track_index = self.parent_window.timeline.add_track('video', '高亮片段')

                        # 清空现有内容
                        self.parent_window.timeline.tracks[video_track_index]['media_files'].clear()

                        # 添加高亮片段
                        timeline_position = 0.0
                        for i, highlight in enumerate(highlights):
                            media_item = {
                                'file_path': video_path,
                                'start_time': timeline_position,
                                'duration': highlight['duration'],
                                'name': f"高亮片段{i+1}",
                                'trim_start': highlight['start_time'],
                                'trim_end': 0,
                                'highlight_score': highlight['score'],
                                'highlight_type': highlight['type']
                            }

                            self.parent_window.timeline.tracks[video_track_index]['media_files'].append(media_item)
                            timeline_position += highlight['duration'] + 0.5  # 0.5秒间隔

                        # 更新显示
                        self.parent_window.timeline.update_total_duration()
                        self.parent_window.update_track_display(video_track_index)

                    except Exception as e:
                        print(f"添加高亮片段到时间轴失败: {e}")

            # 显示对话框
            dialog = HighlightDetectionDialog(self)
            dialog.exec()

        except Exception as e:
            logger.error(f"自动识别高亮失败: {e}")
            QMessageBox.critical(self, "错误", f"自动识别高亮失败:\n{str(e)}")

    def import_video(self):
        """导入视频文件"""
        try:
            file_paths, _ = QFileDialog.getOpenFileNames(
                self,
                "导入视频文件",
                "",
                "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv);;所有文件 (*)"
            )

            for file_path in file_paths:
                self.media_library.add_media_item(file_path)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导入视频失败:\n{str(e)}")

    def cut_selection(self):
        """剪切选中部分"""
        try:
            selection = self.timeline.get_selection()
            if not selection:
                QMessageBox.information(self, "提示", "请先在时间轴上选择要剪切的媒体")
                return

            # 获取选中的媒体项
            selected_items = []
            for item in selection:
                if item.get('media_item'):
                    selected_items.append(item)

            if not selected_items:
                QMessageBox.information(self, "提示", "没有选中有效的媒体项")
                return

            # 简单的剪切：删除选中的媒体项
            for item in selected_items:
                track_index = item['track_index']
                media_item = item['media_item']

                if track_index < len(self.timeline.tracks):
                    track = self.timeline.tracks[track_index]
                    if media_item in track['media_files']:
                        track['media_files'].remove(media_item)
                        print(f"剪切媒体: {media_item.get('name', 'Unknown')}")

            # 更新显示
            for item in selected_items:
                track_index = item['track_index']
                self.update_track_display(track_index)

            QMessageBox.information(self, "剪切完成", f"已剪切 {len(selected_items)} 个媒体项")

        except Exception as e:
            logger.error(f"剪切失败: {e}")
            QMessageBox.critical(self, "错误", f"剪切失败:\n{str(e)}")

    def copy_selection(self):
        """复制选中部分"""
        try:
            selection = self.timeline.get_selection()
            if not selection:
                QMessageBox.information(self, "提示", "请先在时间轴上选择要复制的媒体")
                return

            # 将选中的媒体项保存到剪贴板
            self.clipboard_items = []
            for item in selection:
                if item.get('media_item'):
                    # 深拷贝媒体项
                    import copy
                    copied_item = copy.deepcopy(item['media_item'])
                    self.clipboard_items.append({
                        'track_type': self.timeline.tracks[item['track_index']]['type'],
                        'media_item': copied_item
                    })

            QMessageBox.information(self, "复制完成", f"已复制 {len(self.clipboard_items)} 个媒体项")

        except Exception as e:
            logger.error(f"复制失败: {e}")
            QMessageBox.critical(self, "错误", f"复制失败:\n{str(e)}")

    def paste_selection(self):
        """粘贴选中部分"""
        try:
            if not hasattr(self, 'clipboard_items') or not self.clipboard_items:
                QMessageBox.information(self, "提示", "剪贴板为空，请先复制媒体项")
                return

            # 获取当前播放位置作为粘贴位置
            paste_position = getattr(self.timeline, 'current_position', 0.0)

            # 粘贴到对应类型的轨道
            pasted_count = 0
            for clipboard_item in self.clipboard_items:
                track_type = clipboard_item['track_type']
                media_item = clipboard_item['media_item']

                # 查找对应类型的轨道
                target_track_index = None
                for i, track in enumerate(self.timeline.tracks):
                    if track['type'] == track_type:
                        target_track_index = i
                        break

                if target_track_index is not None:
                    # 更新媒体项的开始时间
                    media_item['start_time'] = paste_position
                    media_item['id'] = f"{target_track_index}_{len(self.timeline.tracks[target_track_index]['media_files'])}"

                    # 添加到轨道
                    self.timeline.tracks[target_track_index]['media_files'].append(media_item)

                    # 更新显示
                    self.update_track_display(target_track_index)

                    pasted_count += 1
                    paste_position += media_item.get('duration', 5.0)  # 下一个媒体项的位置

            if pasted_count > 0:
                QMessageBox.information(self, "粘贴完成", f"已粘贴 {pasted_count} 个媒体项")
            else:
                QMessageBox.information(self, "提示", "没有找到合适的轨道进行粘贴")

        except Exception as e:
            logger.error(f"粘贴失败: {e}")
            QMessageBox.critical(self, "错误", f"粘贴失败:\n{str(e)}")

    def toggle_preview_mode(self, event=None):
        """切换预览模式"""
        try:
            QMessageBox.information(self, "预览模式", "预览模式功能开发中...")
        except Exception as e:
            logger.error(f"预览模式失败: {e}")

    def show_enhanced_features(self):
        """显示增强功能面板"""
        try:
            QMessageBox.information(self, "增强功能", "增强功能面板开发中...")
        except Exception as e:
            logger.error(f"增强功能失败: {e}")

    def quick_auto_sync(self):
        """快速自动踩点"""
        try:
            # 检查是否有音频文件
            audio_files = []
            for track in self.timeline.tracks:
                if track['type'] == 'audio':
                    for media_item in track['media_files']:
                        if isinstance(media_item, dict) and not media_item.get('is_placeholder', False):
                            audio_files.append(media_item)

            if not audio_files:
                QMessageBox.information(self, "提示", "请先添加音频文件到时间轴")
                return

            # 显示踩点配置对话框
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QSpinBox, QDoubleSpinBox, QPushButton, QCheckBox, QComboBox

            class AutoSyncDialog(QDialog):
                def __init__(self, parent=None, audio_files=None):
                    super().__init__(parent)
                    self.setWindowTitle("自动踩点设置")
                    self.setFixedSize(450, 350)
                    self.parent_window = parent
                    self.audio_files = audio_files or []

                    layout = QVBoxLayout(self)

                    # 音频文件选择
                    layout.addWidget(QLabel("选择音频文件:"))
                    self.audio_combo = QComboBox()
                    for audio_file in self.audio_files:
                        self.audio_combo.addItem(audio_file['name'], audio_file)
                    layout.addWidget(self.audio_combo)
                    layout.addWidget(QLabel(""))

                    # BPM设置
                    bpm_layout = QHBoxLayout()
                    bpm_layout.addWidget(QLabel("BPM (节拍/分钟):"))
                    self.bpm_spin = QSpinBox()
                    self.bpm_spin.setRange(60, 200)
                    self.bpm_spin.setValue(120)
                    bpm_layout.addWidget(self.bpm_spin)

                    self.auto_detect_bpm = QCheckBox("自动检测BPM")
                    self.auto_detect_bpm.setChecked(True)
                    bpm_layout.addWidget(self.auto_detect_bpm)
                    layout.addLayout(bpm_layout)

                    # 踩点间隔
                    interval_layout = QHBoxLayout()
                    interval_layout.addWidget(QLabel("踩点间隔:"))
                    self.interval_combo = QComboBox()
                    self.interval_combo.addItems(["每拍", "每2拍", "每4拍", "每8拍"])
                    self.interval_combo.setCurrentText("每4拍")
                    interval_layout.addWidget(self.interval_combo)
                    layout.addLayout(interval_layout)

                    # 偏移设置
                    offset_layout = QHBoxLayout()
                    offset_layout.addWidget(QLabel("起始偏移(秒):"))
                    self.offset_spin = QDoubleSpinBox()
                    self.offset_spin.setRange(-10.0, 10.0)
                    self.offset_spin.setValue(0.0)
                    self.offset_spin.setSuffix(" 秒")
                    offset_layout.addWidget(self.offset_spin)
                    layout.addLayout(offset_layout)

                    # 踩点类型
                    layout.addWidget(QLabel("踩点类型:"))
                    self.beat_type_combo = QComboBox()
                    self.beat_type_combo.addItems(["强拍", "弱拍", "所有拍"])
                    self.beat_type_combo.setCurrentText("强拍")
                    layout.addWidget(self.beat_type_combo)

                    # 选项
                    self.add_markers_check = QCheckBox("添加踩点标记")
                    self.add_markers_check.setChecked(True)
                    layout.addWidget(self.add_markers_check)

                    self.sync_video_check = QCheckBox("同步视频片段到踩点")
                    self.sync_video_check.setChecked(False)
                    layout.addWidget(self.sync_video_check)

                    # 按钮
                    button_layout = QHBoxLayout()

                    start_btn = QPushButton("开始踩点")
                    start_btn.clicked.connect(self.start_auto_sync)
                    button_layout.addWidget(start_btn)

                    preview_btn = QPushButton("预览")
                    preview_btn.clicked.connect(self.preview_sync)
                    button_layout.addWidget(preview_btn)

                    cancel_btn = QPushButton("取消")
                    cancel_btn.clicked.connect(self.close)
                    button_layout.addWidget(cancel_btn)

                    layout.addLayout(button_layout)

                def start_auto_sync(self):
                    if not self.audio_files:
                        QMessageBox.information(self, "提示", "没有可用的音频文件")
                        return

                    selected_audio = self.audio_combo.currentData()
                    if not selected_audio:
                        QMessageBox.information(self, "提示", "请选择音频文件")
                        return

                    bpm = self.bpm_spin.value()
                    interval = self.interval_combo.currentText()
                    offset = self.offset_spin.value()
                    beat_type = self.beat_type_combo.currentText()

                    # 生成踩点
                    beat_points = self.generate_beat_points(selected_audio, bpm, interval, offset, beat_type)

                    if beat_points:
                        # 添加踩点标记到音频文件
                        selected_audio['beat_points'] = beat_points
                        selected_audio['auto_sync_audio'] = True

                        # 更新显示
                        audio_track_index = self.parent_window.find_track_by_type('audio')
                        if audio_track_index >= 0:
                            self.parent_window.update_track_display(audio_track_index)

                        QMessageBox.information(
                            self, "踩点完成",
                            f"生成了 {len(beat_points)} 个踩点标记"
                        )
                    else:
                        QMessageBox.information(self, "踩点失败", "无法生成踩点标记")

                    self.close()

                def generate_beat_points(self, audio_file, bpm, interval, offset, beat_type):
                    """生成踩点标记"""
                    try:
                        duration = audio_file['duration']
                        beat_interval = 60.0 / bpm  # 每拍的时间间隔（秒）

                        # 根据间隔类型调整
                        interval_multiplier = {
                            "每拍": 1,
                            "每2拍": 2,
                            "每4拍": 4,
                            "每8拍": 8
                        }.get(interval, 4)

                        actual_interval = beat_interval * interval_multiplier

                        # 生成踩点
                        beat_points = []
                        current_time = offset

                        while current_time < duration:
                            if current_time >= 0:  # 只添加正时间的踩点
                                beat_points.append(current_time)
                            current_time += actual_interval

                        return beat_points

                    except Exception as e:
                        print(f"生成踩点失败: {e}")
                        return []

                def preview_sync(self):
                    QMessageBox.information(self, "预览", "踩点预览功能开发中...")

            # 显示对话框
            dialog = AutoSyncDialog(self, audio_files)
            dialog.exec()

        except Exception as e:
            logger.error(f"自动踩点失败: {e}")
            QMessageBox.critical(self, "错误", f"自动踩点失败:\n{str(e)}")

    def on_position_changed(self, position: float):
        """播放位置改变"""
        # 🔧 新设计：播放头通过全局参数自动同步
        # if hasattr(self, 'unified_playhead'):
        #     self.unified_playhead.set_position(position)

        # 同步时间轴位置
        if hasattr(self, 'timeline'):
            self.timeline.set_position(position)



    def on_playback_state_changed(self, state):
        """播放状态改变处理"""
        from PySide6.QtMultimedia import QMediaPlayer

        if state == QMediaPlayer.PlaybackState.PlayingState:
            self.play_pause_btn.setText("暂停")
            print("🔸 播放状态：播放中")
        elif state == QMediaPlayer.PlaybackState.PausedState:
            self.play_pause_btn.setText("播放")
            print("🔸 播放状态：已暂停")
        elif state == QMediaPlayer.PlaybackState.StoppedState:
            self.play_pause_btn.setText("播放")
            print("🔸 播放状态：已停止")

    def on_volume_changed(self, value):
        """视频播放器音量变化处理"""
        if hasattr(self, 'video_player'):
            try:
                if hasattr(self.video_player, 'set_volume'):
                    self.video_player.set_volume(value / 100.0)
                    print(f"🔊 主窗口音量设置为: {value}%")
                elif hasattr(self.video_player, 'audio_output'):
                    self.video_player.audio_output.setVolume(value / 100.0)
                    print(f"🔊 直接设置音频输出音量: {value}%")
                else:
                    print("❌ 找不到音量设置方法")
            except Exception as e:
                print(f"❌ 音量设置失败: {e}")



    def stop_playback(self):
        """停止播放"""
        if hasattr(self, 'video_player'):
            self.video_player.stop()
        self.stop_timeline_playback()

    def on_timeline_position_changed(self, position: float):
        """时间轴位置改变处理 - 真正改变播放点，防止循环调用"""
        # 🔧 修复：防止循环调用
        if self._updating_position:
            return

        # 特殊值-1表示清空预览
        if position == -1:
            print("🎯 收到清空预览信号")
            if hasattr(self, 'video_player'):
                self.video_player.clear_display()
            return

        print(f"🎯 时间轴位置改变: {position:.2f}s")

        self._updating_position = True
        try:
            # 🔧 修复：真正设置时间轴的当前位置
            self.global_params.set_current_position(position)
            if hasattr(self, 'timeline'):
                self.timeline.set_position(position)

            # 🔧 新设计：播放头通过全局参数自动同步
            # if hasattr(self, 'unified_playhead'):
            #     self.unified_playhead.set_position(position)
        finally:
            self._updating_position = False

        # 🔧 修改：使用预览合成器生成合成预览
        if hasattr(self, 'timeline') and self.timeline.tracks:
            self.update_preview_at_position(position)
        else:
            print(f"🎯 没有轨道信息，清空视频预览")
            # 清空视频预览窗口
            if hasattr(self, 'video_player'):
                self.video_player.clear_display()

    def update_preview_at_position(self, position: float):
        """在指定位置更新预览"""
        try:
            # 🔧 修改：为每个轨道上的视频创建独立的播放器窗体
            if not hasattr(self, 'video_canvas'):
                return

            # 记录当前位置应该显示的视频
            active_videos = set()

            # 遍历所有轨道，找到当前位置的视频
            for track in self.timeline.tracks:
                if track.get('type') != 'video':
                    continue

                # 查找当前位置的媒体项
                for media_item in track.get('media_files', []):
                    if not isinstance(media_item, dict):
                        continue

                    start_time = media_item.get('start_time', 0.0)
                    duration = media_item.get('duration', 0.0)
                    end_time = start_time + duration

                    video_path = media_item.get('file_path', '')

                    if video_path and not video_path.startswith('placeholder_'):
                        # 确保视频播放器存在
                        if video_path not in self.video_canvas.video_players:
                            self.video_canvas.add_video_player(video_path, start_time)

                        # 检查是否在播放范围内
                        if start_time <= position < end_time:
                            active_videos.add(video_path)

                            # 🔧 修复：播放时让播放器自己更新帧，暂停时使用静态帧
                            if self._is_playing:
                                # 播放状态：显示播放器，让其播放线程自动更新帧
                                self.video_canvas.show_video_player(video_path)

                                # 确保播放器在正确的时间位置
                                player = self.video_canvas.video_players.get(video_path)
                                if player and hasattr(player, 'video_player_thread'):
                                    video_position = position - start_time
                                    # 同步播放位置（如果偏差较大）
                                    if abs(player.video_player_thread.current_position - video_position) > 0.5:
                                        player.seek_to_position(video_position)
                                    # 确保播放状态
                                    if not player.is_playing:
                                        player.play()
                            else:
                                # 暂停状态：获取静态帧并更新播放器
                                frame = self.get_video_frame_at_position(media_item, position)
                                if frame is not None:
                                    self.video_canvas.update_video_frame(video_path, frame)
                                # 显示播放器
                                self.video_canvas.show_video_player(video_path)
                        # 🔧 修复：不要立即隐藏播放器，让用户能看到视频
                        # else:
                        #     # 隐藏不在当前时间范围的播放器
                        #     self.video_canvas.hide_video_player(video_path)

            # 🔧 修复：只隐藏明确不在当前时间范围内的视频播放器
            # 不要在这里隐藏，因为上面的逻辑已经处理了显示/隐藏
            # for video_path in self.video_canvas.video_players.keys():
            #     if video_path not in active_videos:
            #         self.video_canvas.hide_video_player(video_path)

            print(f"🎬 预览已更新到位置: {position:.2f}s, 活跃视频: {len(active_videos)}个")

        except Exception as e:
            print(f"❌ 更新预览失败: {e}")

    def get_video_frame_at_position(self, media_item: dict, timeline_position: float):
        """获取指定位置的视频帧"""
        try:
            file_path = media_item.get('file_path', '')

            if not file_path or file_path.startswith('placeholder_') or not os.path.exists(file_path):
                return None

            # 计算在视频文件内的相对位置
            start_time = media_item.get('start_time', 0.0)
            relative_position = timeline_position - start_time
            trim_start = media_item.get('trim_start', 0.0)
            actual_position = trim_start + relative_position

            # 打开视频文件
            cap = cv2.VideoCapture(file_path)
            if not cap.isOpened():
                return None

            # 跳转到指定位置
            fps = cap.get(cv2.CAP_PROP_FPS) or 30.0
            frame_number = int(actual_position * fps)
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)

            # 读取帧
            ret, frame = cap.read()
            cap.release()

            if ret:
                return frame
            else:
                return None

        except Exception as e:
            print(f"❌ 获取视频帧失败: {e}")
            return None

    def on_preview_frame_ready(self, frame):
        """处理预览帧准备就绪"""
        try:
            # 🔧 修改：播放时强制更新所有视频播放器
            if hasattr(self, 'video_canvas') and hasattr(self, 'timeline'):
                current_position = self.timeline.current_position

                # 强制更新所有活跃的视频播放器
                self.force_update_video_players(current_position)

                print(f"🎬 播放时强制更新视频播放器: {current_position:.2f}s")
        except Exception as e:
            print(f"❌ 处理预览帧失败: {e}")

    def force_update_video_players(self, position: float):
        """强制更新所有视频播放器"""
        try:
            # 遍历所有轨道，强制更新当前时间的视频
            for track in self.timeline.tracks:
                if track.get('type') != 'video':
                    continue

                for media_item in track.get('media_files', []):
                    if not isinstance(media_item, dict):
                        continue

                    start_time = media_item.get('start_time', 0.0)
                    duration = media_item.get('duration', 0.0)
                    end_time = start_time + duration
                    video_path = media_item.get('file_path', '')

                    # 检查是否在播放范围内
                    if start_time <= position < end_time and video_path:
                        if not video_path.startswith('placeholder_'):
                            # 强制获取当前帧
                            video_frame = self.get_video_frame_at_position(media_item, position)
                            if video_frame is not None:
                                # 强制更新播放器
                                self.video_canvas.update_video_frame(video_path, video_frame)
                                print(f"🎬 强制更新播放器帧: {video_path} at {position:.2f}s")
                            else:
                                print(f"❌ 无法获取视频帧: {video_path} at {position:.2f}s")

        except Exception as e:
            print(f"❌ 强制更新视频播放器失败: {e}")

    def update_preview_for_selected_media(self):
        """为选中的媒体更新预览"""
        try:
            if hasattr(self, 'current_selected_media') and hasattr(self, 'timeline'):
                # 跳转到选中媒体的开始时间
                start_time = self.current_selected_media.get('start_time', 0.0)
                self.timeline.set_position(start_time)

                # 更新预览
                self.update_preview_at_position(start_time)
                print(f"🎬 已跳转到选中媒体位置: {start_time:.2f}s")
        except Exception as e:
            print(f"❌ 更新选中媒体预览失败: {e}")

    def apply_effect_to_media(self, effect_type: str, effect_value: float):
        """应用效果到当前选中的媒体"""
        try:
            if not hasattr(self, 'current_selected_media'):
                print("❌ 没有选中的媒体项")
                return

            # 查找对应的媒体项
            media_item = self.find_media_item_by_path_and_time(
                self.current_selected_media['file_path'],
                self.current_selected_media['start_time']
            )

            if not media_item:
                print("❌ 未找到对应的媒体项")
                return

            # 确保effects列表存在
            if 'effects' not in media_item:
                media_item['effects'] = []

            # 查找或创建效果
            effect_found = False
            for effect in media_item['effects']:
                if effect.get('type') == effect_type:
                    effect['value'] = effect_value
                    effect_found = True
                    break

            if not effect_found:
                media_item['effects'].append({
                    'type': effect_type,
                    'value': effect_value
                })

            # 标记项目已修改
            self.mark_project_modified()

            # 更新预览
            current_position = self.global_params.current_position
            self.update_preview_at_position(current_position)

            print(f"✅ 已应用效果 {effect_type}: {effect_value}")

        except Exception as e:
            print(f"❌ 应用效果失败: {e}")

    def find_media_item_by_path_and_time(self, file_path: str, start_time: float):
        """根据文件路径和开始时间查找媒体项"""
        try:
            if not hasattr(self, 'timeline'):
                return None

            for track in self.timeline.tracks:
                for media_item in track.get('media_files', []):
                    if (isinstance(media_item, dict) and
                        media_item.get('file_path') == file_path and
                        abs(media_item.get('start_time', 0.0) - start_time) < 0.1):  # 允许小误差
                        return media_item

            return None
        except Exception as e:
            print(f"❌ 查找媒体项失败: {e}")
            return None

    def on_brightness_changed(self, value: int):
        """亮度滑块变化"""
        self.brightness_label.setText(str(value))
        self.apply_effect_to_media('brightness', float(value))

    def on_contrast_changed(self, value: int):
        """对比度滑块变化"""
        self.contrast_label.setText(str(value))
        # 对比度范围转换：滑块0-200 -> 实际0.0-2.0
        contrast_value = value / 100.0
        self.apply_effect_to_media('contrast', contrast_value)

    def on_saturation_changed(self, value: int):
        """饱和度滑块变化"""
        self.saturation_label.setText(str(value))
        # 饱和度范围转换：滑块0-200 -> 实际0.0-2.0
        saturation_value = value / 100.0
        self.apply_effect_to_media('saturation', saturation_value)



    def on_preview_quality_changed(self, quality_text: str):
        """预览质量改变"""
        quality_map = {
            "原画": "original",
            "高清": "high",
            "流畅": "smooth"
        }
        quality = quality_map.get(quality_text, "original")

        # 更新预览合成器质量
        if hasattr(self, 'preview_compositor'):
            self.preview_compositor.set_preview_quality(quality)

        # 🔧 修改：更新交互式视频画布质量
        if hasattr(self, 'video_canvas'):
            self.video_canvas.set_preview_quality(quality)

        # 刷新当前预览
        if hasattr(self, 'timeline'):
            current_position = self.global_params.current_position
            self.update_preview_at_position(current_position)

        print(f"🎬 预览质量已切换为: {quality_text}")

    def on_preview_ratio_changed(self, ratio_text: str):
        """预览比例改变"""
        # 更新预览合成器比例
        if hasattr(self, 'preview_compositor'):
            self.preview_compositor.set_preview_aspect_ratio(ratio_text)

        # 🔧 修改：更新交互式视频画布比例
        if hasattr(self, 'video_canvas'):
            self.video_canvas.set_preview_aspect_ratio(ratio_text)

        # 刷新当前预览
        if hasattr(self, 'timeline'):
            current_position = self.global_params.current_position
            self.update_preview_at_position(current_position)

        print(f"📐 比例已切换为: {ratio_text}")

    def on_video_transform_changed(self, scale: float, offset_x: float, offset_y: float):
        """视频变换改变"""
        try:
            # 存储当前变换参数（用于导出）
            if not hasattr(self, 'video_transform'):
                self.video_transform = {}

            self.video_transform['scale'] = scale
            self.video_transform['offset_x'] = offset_x
            self.video_transform['offset_y'] = offset_y

            # 标记项目已修改
            self.mark_project_modified()

        except Exception as e:
            print(f"❌ 处理视频变换失败: {e}")

    def on_video_transform_updated(self, video_path: str, transform_info: dict):
        """视频变换信息更新 - 来自可拖动播放器"""
        try:
            # 初始化视频变换存储
            if not hasattr(self, 'video_transforms'):
                self.video_transforms = {}

            # 存储变换信息
            self.video_transforms[video_path] = transform_info.copy()

            print(f"🔧 主窗口记录视频变换: {video_path}")
            print(f"   - 位置: ({transform_info['position']['x']}, {transform_info['position']['y']})")
            print(f"   - 尺寸: {transform_info['size']['width']}x{transform_info['size']['height']}")
            print(f"   - 缩放: {transform_info['scale']:.2f}")

            # 标记项目已修改
            self.mark_project_modified()

        except Exception as e:
            print(f"❌ 更新视频变换信息失败: {e}")

    def get_video_transforms(self) -> dict:
        """获取所有视频的变换信息"""
        return getattr(self, 'video_transforms', {})

    def calculate_actual_playback_position(self, timeline_position: float) -> float:
        """计算轨道上素材的实际播放位置 - 按照原版逻辑"""
        # 如果没有轨道数据，直接返回时间轴位置
        if not hasattr(self, 'timeline') or not self.timeline.tracks:
            return timeline_position

        # 查找时间轴位置对应的素材
        for track in self.timeline.tracks:
            for media_item in track['media_files']:
                if isinstance(media_item, dict):
                    start_time = media_item['start_time']
                    duration = media_item['duration']
                    end_time = start_time + duration

                    # 如果时间轴位置在这个素材范围内
                    if start_time <= timeline_position <= end_time:
                        # 计算在素材内的相对位置
                        relative_position = timeline_position - start_time

                        # 考虑裁剪偏移
                        trim_start = media_item.get('trim_start', 0)
                        playback_position = trim_start + relative_position

                        return playback_position

        # 如果没有找到对应的素材，返回播放位置
        return timeline_position

    # 移除复杂的预览更新方法

        # 查找当前位置的媒体并同步播放器
        current_media = self.get_current_media_at_position(position)
        if current_media and hasattr(self, 'video_player'):
            file_path = current_media['file_path']
            if not file_path.startswith('placeholder_') and os.path.exists(file_path):
                # 计算在素材内的相对位置
                media_start_time = current_media['start_time']
                relative_position = position - media_start_time
                trim_start = current_media.get('trim_start', 0)
                actual_position = trim_start + relative_position

                # 如果当前播放的不是这个文件，则加载它
                if getattr(self.video_player, 'current_file', None) != file_path:
                    # 创建video_info对象
                    class VideoInfo:
                        def __init__(self, duration, file_path, name):
                            self.duration = duration
                            self.file_path = file_path
                            self.name = name

                    duration = current_media.get('duration', 0)
                    video_info = VideoInfo(duration, file_path, Path(file_path).name)

                    try:
                        self.video_player.load_video(file_path, video_info)
                    except TypeError:
                        try:
                            self.video_player.load_video(file_path)
                        except:
                            pass

                # 设置播放位置
                self.video_player.set_position(actual_position)

    def quick_import_videos(self):
        """快速导入视频"""
        try:
            folder_path = QFileDialog.getExistingDirectory(
                self,
                "选择视频文件夹",
                ""
            )

            if folder_path:
                video_files = self.scan_video_folder(folder_path)
                if video_files:
                    for file_path in video_files[:10]:  # 限制最多10个文件
                        self.media_library.add_media_item(file_path)
                    QMessageBox.information(self, "导入完成", f"已导入 {len(video_files[:10])} 个视频文件")
                else:
                    QMessageBox.information(self, "提示", "文件夹中没有找到视频文件")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"快速导入失败:\n{str(e)}")

    def scan_video_folder(self, folder_path):
        """扫描文件夹中的视频文件"""
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']
        video_files = []

        try:
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in video_extensions):
                        video_files.append(os.path.join(root, file))
        except Exception as e:
            print(f"扫描文件夹失败: {e}")

        return video_files

    def quick_preview_results(self):
        """快速预览结果"""
        try:
            QMessageBox.information(self, "预览结果", "预览结果功能开发中...")
        except Exception as e:
            logger.error(f"预览结果失败: {e}")

    def show_tutorial(self):
        """显示教程"""
        try:
            QMessageBox.information(self, "教程", "教程功能开发中...")
        except Exception as e:
            logger.error(f"教程失败: {e}")

    def keyPressEvent(self, event):
        """键盘快捷键处理"""
        try:
            # 播放控制快捷键
            if event.key() == Qt.Key.Key_Space:
                # 空格键：播放/暂停
                self.toggle_playback()
                event.accept()
                return

            # 编辑快捷键
            elif event.key() == Qt.Key.Key_X and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                # Ctrl+X：剪切
                self.cut_selection()
                event.accept()
                return

            elif event.key() == Qt.Key.Key_C and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                # Ctrl+C：复制
                self.copy_selection()
                event.accept()
                return

            elif event.key() == Qt.Key.Key_V and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                # Ctrl+V：粘贴
                self.paste_selection()
                event.accept()
                return

            # 导入导出快捷键
            elif event.key() == Qt.Key.Key_I and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                # Ctrl+I：导入视频
                self.import_video()
                event.accept()
                return

            elif event.key() == Qt.Key.Key_E and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                # Ctrl+E：导出视频
                self.export_video()
                event.accept()
                return

            # 时间轴控制快捷键
            elif event.key() == Qt.Key.Key_Home:
                # Home：跳转到开始
                self.global_params.set_current_position(0.0)
                self.timeline.set_position(0.0)
                event.accept()
                return

            elif event.key() == Qt.Key.Key_End:
                # End：跳转到结尾
                self.global_params.set_current_position(self.global_params.total_duration)
                self.timeline.set_position(self.global_params.total_duration)
                event.accept()
                return

            elif event.key() == Qt.Key.Key_Left:
                # 左箭头：后退1秒
                new_pos = max(0, self.global_params.current_position - 1.0)
                self.global_params.set_current_position(new_pos)
                self.timeline.set_position(new_pos)
                event.accept()
                return

            elif event.key() == Qt.Key.Key_Right:
                # 右箭头：前进1秒
                new_pos = min(self.global_params.total_duration, self.global_params.current_position + 1.0)
                self.global_params.set_current_position(new_pos)
                self.timeline.set_position(new_pos)
                event.accept()
                return

            # 缩放快捷键
            elif event.key() == Qt.Key.Key_Plus and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                # Ctrl++：放大
                self.timeline.zoom_in()
                event.accept()
                return

            elif event.key() == Qt.Key.Key_Minus and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                # Ctrl+-：缩小
                self.timeline.zoom_out()
                event.accept()
                return

            elif event.key() == Qt.Key.Key_0 and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                # Ctrl+0：适合窗口
                self.timeline.zoom_to_fit()
                event.accept()
                return

            # 轨道管理快捷键
            elif event.key() == Qt.Key.Key_T and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                # Ctrl+T：添加视频轨道
                self.timeline.add_track('video')
                event.accept()
                return

            elif event.key() == Qt.Key.Key_A and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                # Ctrl+A：选择所有
                event.accept()
                return

            # 删除快捷键
            elif event.key() == Qt.Key.Key_Delete:
                # Delete：删除选中项
                self.cut_selection()
                event.accept()
                return

            # ESC：取消选择
            elif event.key() == Qt.Key.Key_Escape:
                self.timeline.clear_selection()
                event.accept()
                return

        except Exception as e:
            logger.error(f"键盘快捷键处理失败: {e}")

        # 调用父类处理其他按键
        super().keyPressEvent(event)

    def save_project(self):
        """保存项目"""
        try:
            # 如果没有当前项目路径，则另存为
            if not self.project_manager.current_project_path:
                self.save_project_as()
                return

            # 保存当前状态到项目管理器
            self.save_current_project_state()

            # 使用项目管理器保存
            if self.project_manager.save_project():
                self.project_modified = False
                # 更新窗口标题，去掉*标记
                current_title = self.windowTitle()
                if current_title.endswith("*"):
                    self.setWindowTitle(current_title[:-1])

                self.status_bar.showMessage(f"项目已保存: {os.path.basename(str(self.project_manager.current_project_path))}")
                print("✅ 项目保存成功")
            else:
                QMessageBox.warning(self, "警告", "保存项目失败！")

        except Exception as e:
            logger.error(f"保存项目失败: {e}")
            QMessageBox.critical(self, "错误", f"保存项目失败:\n{str(e)}")

    def save_project_as(self):
        """另存为项目"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "另存为项目",
                str(self.project_manager.projects_dir / "新项目.json"),
                "项目文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                # 保存当前状态到项目管理器
                self.save_current_project_state()

                # 使用项目管理器另存为
                if self.project_manager.save_project(file_path):
                    project_name = Path(file_path).stem
                    self.setWindowTitle(f"SWANKSALON - {project_name}")
                    self.project_modified = False

                    self.status_bar.showMessage(f"项目已保存: {os.path.basename(file_path)}")
                    QMessageBox.information(self, "保存完成", f"项目已保存到:\n{file_path}")
                else:
                    QMessageBox.warning(self, "警告", "保存项目失败！")

        except Exception as e:
            logger.error(f"另存为项目失败: {e}")
            QMessageBox.critical(self, "错误", f"另存为项目失败:\n{str(e)}")

    def load_project(self):
        """加载项目"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "打开项目",
                str(self.project_manager.projects_dir),
                "项目文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                # 保存当前项目状态
                self.save_current_project_state()

                # 使用项目管理器加载项目
                project_data = self.project_manager.load_project(file_path)
                if not project_data:
                    QMessageBox.warning(self, "警告", "加载项目失败！")
                    return

                # 应用项目数据
                self.apply_project_data(project_data)

                # 更新窗口标题
                project_name = project_data.get("project_info", {}).get("name", "未知项目")
                self.setWindowTitle(f"SWANKSALON - {project_name}")
                self.project_modified = False

                self.status_bar.showMessage(f"项目已加载: {os.path.basename(file_path)}")
                print(f"✅ 项目加载成功: {project_name}")

        except Exception as e:
            logger.error(f"加载项目失败: {e}")
            QMessageBox.critical(self, "错误", f"加载项目失败:\n{str(e)}")

    def get_project_data(self):
        """获取项目数据"""
        try:
            project_data = {
                'version': '1.0',
                'name': 'SWANKSALON Project',
                'created_time': QTime.currentTime().toString(),
                'timeline': {
                    'total_duration': self.global_params.total_duration,
                    'pixels_per_second': self.global_params.pixels_per_second,
                    'current_position': self.global_params.current_position,
                    'tracks': []
                },
                'media_library': {
                    'files': []
                }
            }

            # 保存轨道数据
            for track in self.timeline.tracks:
                track_data = {
                    'type': track['type'],
                    'name': track['name'],
                    'muted': track.get('muted', False),
                    'visible': track.get('visible', True),
                    'media_files': []
                }

                # 保存媒体文件数据
                for media_item in track['media_files']:
                    if isinstance(media_item, dict):
                        # 只保存必要的数据，排除临时数据
                        media_data = {
                            'file_path': media_item['file_path'],
                            'start_time': media_item['start_time'],
                            'duration': media_item['duration'],
                            'name': media_item['name'],
                            'trim_start': media_item.get('trim_start', 0),
                            'trim_end': media_item.get('trim_end', 0),
                            'is_placeholder': media_item.get('is_placeholder', False),
                            'template_segment': media_item.get('template_segment', False),
                            'segment_type': media_item.get('segment_type', ''),
                            'placeholder_text': media_item.get('placeholder_text', ''),
                            'beat_points': media_item.get('beat_points', []),
                            'auto_sync_audio': media_item.get('auto_sync_audio', False)
                        }
                        track_data['media_files'].append(media_data)

                project_data['timeline']['tracks'].append(track_data)

            # 保存媒体库数据（简化版本）
            if hasattr(self, 'media_library'):
                # 这里可以添加媒体库的保存逻辑
                pass

            return project_data

        except Exception as e:
            logger.error(f"获取项目数据失败: {e}")
            return {}

    def load_project_data(self, project_data):
        """加载项目数据"""
        try:
            if not project_data:
                raise ValueError("项目数据为空")

            # 清空当前项目
            self.timeline.tracks.clear()
            self.timeline.track_widgets.clear()

            # 清空轨道显示
            for i in reversed(range(self.timeline.tracks_layout.count())):
                item = self.timeline.tracks_layout.takeAt(i)
                if item and item.widget():
                    item.widget().deleteLater()

            for i in reversed(range(self.timeline.labels_layout.count())):
                item = self.timeline.labels_layout.takeAt(i)
                if item and item.widget():
                    item.widget().deleteLater()

            # 加载时间轴设置
            timeline_data = project_data.get('timeline', {})
            self.global_params.set_total_duration(timeline_data.get('total_duration', 30.0), emit_signal=False)
            self.global_params.set_pixels_per_second(timeline_data.get('pixels_per_second', 100), emit_signal=False)
            self.global_params.set_current_position(timeline_data.get('current_position', 0.0), emit_signal=False)

            # 加载轨道数据
            tracks_data = timeline_data.get('tracks', [])
            for track_data in tracks_data:
                # 重建轨道
                track_index = self.timeline.add_track(
                    track_data['type'],
                    track_data['name']
                )

                if track_index >= 0:
                    track = self.timeline.tracks[track_index]
                    track['muted'] = track_data.get('muted', False)
                    track['visible'] = track_data.get('visible', True)

                    # 加载媒体文件
                    for media_data in track_data.get('media_files', []):
                        track['media_files'].append(media_data)

            # 更新显示
            self.timeline.apply_zoom()
            for i in range(len(self.timeline.tracks)):
                self.update_track_display(i)

            # 设置播放位置
            self.timeline.set_position(self.timeline.current_position)

        except Exception as e:
            logger.error(f"加载项目数据失败: {e}")
            raise

    def new_project(self):
        """新建项目"""
        try:
            # 询问是否保存当前项目
            if self.project_modified or self.has_unsaved_changes():
                reply = QMessageBox.question(
                    self, "新建项目",
                    "当前项目已修改，是否保存？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No | QMessageBox.StandardButton.Cancel
                )

                if reply == QMessageBox.StandardButton.Cancel:
                    return
                elif reply == QMessageBox.StandardButton.Yes:
                    self.save_project()

            # 🔧 修改：打开项目创建页面
            from gui.dialogs.project_dialog import ProjectDialog

            # 保存当前项目状态
            self.save_current_project_state()

            # 显示项目创建对话框
            project_dialog = ProjectDialog(self.project_manager, self)
            if project_dialog.exec() == QDialog.DialogCode.Accepted:
                selected_project = project_dialog.get_selected_project()
                if selected_project:
                    # 应用选中的项目
                    self.apply_project_data(selected_project)

                    # 更新窗口标题
                    project_name = selected_project.get("project_info", {}).get("name", "未知项目")
                    self.setWindowTitle(f"SWANKSALON - {project_name}")
                    self.project_modified = False

                    self.status_bar.showMessage(f"项目已切换: {project_name}")
                    print(f"✅ 项目切换成功: {project_name}")

        except Exception as e:
            logger.error(f"新建项目失败: {e}")
            QMessageBox.critical(self, "错误", f"新建项目失败:\n{str(e)}")

    def has_unsaved_changes(self) -> bool:
        """检查是否有未保存的更改"""
        try:
            # 检查时间轴是否有内容
            if hasattr(self, 'timeline') and self.timeline:
                for track in self.timeline.tracks:
                    if track.get('media_files'):
                        return True

            # 检查是否有自定义模板
            if hasattr(self, 'media_library') and hasattr(self.media_library, 'templates'):
                if len(self.media_library.templates) > 4:  # 超过默认模板数量
                    return True

            return False
        except Exception as e:
            print(f"❌ 检查未保存更改失败: {e}")
            return False

    def mark_project_modified(self):
        """标记项目已修改"""
        self.project_modified = True
        # 更新窗口标题显示修改状态
        current_title = self.windowTitle()
        if not current_title.endswith("*"):
            self.setWindowTitle(current_title + "*")

    def save_current_project_state(self):
        """保存当前项目状态"""
        try:
            # 保存时间轴数据
            if hasattr(self, 'timeline') and self.timeline:
                tracks = getattr(self.timeline, 'tracks', [])
                total_duration = getattr(self.timeline, 'total_duration', 30.0)
                global_settings = {
                    "fps": 30,
                    "resolution": "1920x1080",
                    "sample_rate": 44100
                }
                self.project_manager.save_timeline_data(tracks, total_duration, global_settings)

            # 保存模板数据
            if hasattr(self, 'media_library') and hasattr(self.media_library, 'templates'):
                self.project_manager.save_templates_data(self.media_library.templates)

            # 保存媒体文件数据
            media_files = []
            if hasattr(self, 'media_library') and hasattr(self.media_library, 'media_files'):
                media_files = [item['file_path'] for item in self.media_library.media_files if 'file_path' in item]
            self.project_manager.save_media_files_data(media_files)

            print("💾 当前项目状态已保存")

        except Exception as e:
            print(f"❌ 保存项目状态失败: {e}")

    def apply_project_data(self, project_data: dict):
        """应用项目数据"""
        try:
            # 清空当前界面
            self.clear_timeline()

            # 应用时间轴数据
            timeline_data = project_data.get("timeline", {})
            if timeline_data:
                self.apply_timeline_data(timeline_data)

            # 应用模板数据
            templates_data = project_data.get("templates", [])
            if templates_data:
                self.apply_templates_data(templates_data)

            # 应用媒体文件数据
            media_files_data = project_data.get("media_files", [])
            if media_files_data:
                self.apply_media_files_data(media_files_data)

            print("✅ 项目数据应用成功")

        except Exception as e:
            print(f"❌ 应用项目数据失败: {e}")

    def apply_timeline_data(self, timeline_data: dict):
        """应用时间轴数据"""
        try:
            tracks_data = timeline_data.get("tracks", [])
            total_duration = timeline_data.get("total_duration", 30.0)

            if hasattr(self, 'timeline') and self.timeline:
                # 清空现有轨道
                for track in self.timeline.tracks:
                    track['media_files'].clear()

                # 应用轨道数据
                for i, track_data in enumerate(tracks_data):
                    if i < len(self.timeline.tracks):
                        track = self.timeline.tracks[i]
                        track['name'] = track_data.get('name', track['name'])
                        track['enabled'] = track_data.get('enabled', True)
                        track['volume'] = track_data.get('volume', 1.0)

                        # 应用媒体文件
                        for media_data in track_data.get('media_files', []):
                            media_item = {
                                'file_path': media_data.get('file_path', ''),
                                'name': media_data.get('name', ''),
                                'start_time': media_data.get('start_time', 0.0),
                                'duration': media_data.get('duration', 0.0),
                                'is_placeholder': media_data.get('is_placeholder', False),
                                'template_segment': media_data.get('template_segment', False),
                                'segment_type': media_data.get('segment_type', 'normal'),
                                'volume': media_data.get('volume', 1.0),
                                'effects': media_data.get('effects', [])
                            }

                            if media_data.get('is_placeholder'):
                                media_item['placeholder_text'] = media_data.get('placeholder_text', '')

                            track['media_files'].append(media_item)

                # 更新总时长
                self.timeline.total_duration = total_duration

                print(f"✅ 时间轴数据已应用: {len(tracks_data)} 个轨道, 总时长 {total_duration}s")

        except Exception as e:
            print(f"❌ 应用时间轴数据失败: {e}")

    def apply_templates_data(self, templates_data: list):
        """应用模板数据"""
        try:
            if hasattr(self, 'media_library'):
                # 清空现有模板（保留默认模板）
                default_templates = [t for t in self.media_library.templates if t.get('id', '').startswith('default_')]
                self.media_library.templates = default_templates.copy()

                # 添加项目中的模板
                for template_data in templates_data:
                    if not template_data.get('id', '').startswith('default_'):  # 不重复添加默认模板
                        self.media_library.templates.append(template_data)

                # 刷新模板显示
                if hasattr(self.media_library, 'refresh_templates_display'):
                    self.media_library.refresh_templates_display()

                print(f"✅ 模板数据已应用: {len(templates_data)} 个模板")

        except Exception as e:
            print(f"❌ 应用模板数据失败: {e}")

    def apply_media_files_data(self, media_files_data: list):
        """应用媒体文件数据"""
        try:
            if hasattr(self, 'media_library'):
                # 清空现有媒体文件
                self.media_library.media_files.clear()

                # 添加项目中的媒体文件（仅添加存在的文件）
                for file_info in media_files_data:
                    file_path = file_info.get('path', '')
                    if os.path.exists(file_path):
                        media_item = {
                            'file_path': file_path,
                            'name': file_info.get('name', os.path.basename(file_path)),
                            'size': file_info.get('size', 0),
                            'modified_time': file_info.get('modified_time', '')
                        }
                        self.media_library.media_files.append(media_item)
                    else:
                        print(f"⚠️ 媒体文件不存在，跳过: {file_path}")

                # 刷新媒体文件显示
                if hasattr(self.media_library, 'refresh_media_display'):
                    self.media_library.refresh_media_display()

                print(f"✅ 媒体文件数据已应用: {len(self.media_library.media_files)} 个文件")

        except Exception as e:
            print(f"❌ 应用媒体文件数据失败: {e}")

    def clear_timeline(self):
        """清空时间轴"""
        try:
            if hasattr(self, 'timeline') and self.timeline:
                for track in self.timeline.tracks:
                    track['media_files'].clear()
                self.timeline.total_duration = 30.0
                print("🧹 时间轴已清空")
        except Exception as e:
            print(f"❌ 清空时间轴失败: {e}")

    def auto_load_last_project(self):
        """自动加载最后的项目"""
        try:
            project_data = self.project_manager.auto_load_last_project()
            if project_data:
                self.apply_project_data(project_data)
                project_name = project_data.get("project_info", {}).get("name", "未知项目")
                self.setWindowTitle(f"SWANKSALON - {project_name}")
                self.project_modified = False
                print(f"🔄 自动加载项目成功: {project_name}")
            else:
                # 没有最后的项目，创建默认项目
                default_project = self.project_manager.create_new_project("默认项目")
                self.project_modified = False
                print("📝 创建默认项目")
        except Exception as e:
            print(f"❌ 自动加载项目失败: {e}")
            # 创建默认项目作为备选
            try:
                self.project_manager.create_new_project("默认项目")
                self.project_modified = False
            except Exception as e2:
                print(f"❌ 创建默认项目也失败: {e2}")

    def preprocess_video(self):
        """视频预处理"""
        try:
            # 选择视频文件
            video_path, _ = QFileDialog.getOpenFileName(
                self, "选择要预处理的视频", "",
                "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv);;所有文件 (*)"
            )

            if not video_path:
                return

            # 显示预处理配置对话框
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QSpinBox, QDoubleSpinBox, QPushButton, QCheckBox, QComboBox, QProgressDialog, QSlider

            class VideoPreprocessDialog(QDialog):
                def __init__(self, parent=None, video_path=""):
                    super().__init__(parent)
                    self.setWindowTitle("视频预处理")
                    self.setFixedSize(500, 600)
                    self.parent_window = parent
                    self.video_path = video_path

                    layout = QVBoxLayout(self)

                    # 视频信息
                    layout.addWidget(QLabel(f"视频文件: {os.path.basename(video_path)}"))
                    layout.addWidget(QLabel(""))

                    # 基本处理选项
                    layout.addWidget(QLabel("基本处理:"))

                    # 分辨率调整
                    resolution_layout = QHBoxLayout()
                    resolution_layout.addWidget(QLabel("输出分辨率:"))
                    self.resolution_combo = QComboBox()
                    self.resolution_combo.addItems([
                        "保持原分辨率", "1920x1080 (1080p)", "1280x720 (720p)",
                        "854x480 (480p)", "640x360 (360p)", "自定义"
                    ])
                    resolution_layout.addWidget(self.resolution_combo)
                    layout.addLayout(resolution_layout)

                    # 帧率调整
                    fps_layout = QHBoxLayout()
                    fps_layout.addWidget(QLabel("输出帧率:"))
                    self.fps_combo = QComboBox()
                    self.fps_combo.addItems(["保持原帧率", "60fps", "30fps", "24fps", "15fps"])
                    fps_layout.addWidget(self.fps_combo)
                    layout.addLayout(fps_layout)

                    # 码率设置
                    bitrate_layout = QHBoxLayout()
                    bitrate_layout.addWidget(QLabel("视频码率:"))
                    self.bitrate_combo = QComboBox()
                    self.bitrate_combo.addItems(["自动", "8000k", "5000k", "3000k", "1500k", "800k"])
                    bitrate_layout.addWidget(self.bitrate_combo)
                    layout.addLayout(bitrate_layout)

                    layout.addWidget(QLabel(""))

                    # 视频增强选项
                    layout.addWidget(QLabel("视频增强:"))

                    self.stabilize_check = QCheckBox("视频防抖")
                    layout.addWidget(self.stabilize_check)

                    self.denoise_check = QCheckBox("降噪处理")
                    layout.addWidget(self.denoise_check)

                    self.sharpen_check = QCheckBox("锐化增强")
                    layout.addWidget(self.sharpen_check)

                    # 亮度对比度调整
                    brightness_layout = QHBoxLayout()
                    brightness_layout.addWidget(QLabel("亮度调整:"))
                    self.brightness_slider = QSlider(Qt.Orientation.Horizontal)
                    self.brightness_slider.setRange(-50, 50)
                    self.brightness_slider.setValue(0)
                    brightness_layout.addWidget(self.brightness_slider)
                    self.brightness_label = QLabel("0")
                    brightness_layout.addWidget(self.brightness_label)
                    layout.addLayout(brightness_layout)

                    contrast_layout = QHBoxLayout()
                    contrast_layout.addWidget(QLabel("对比度调整:"))
                    self.contrast_slider = QSlider(Qt.Orientation.Horizontal)
                    self.contrast_slider.setRange(-50, 50)
                    self.contrast_slider.setValue(0)
                    contrast_layout.addWidget(self.contrast_slider)
                    self.contrast_label = QLabel("0")
                    contrast_layout.addWidget(self.contrast_label)
                    layout.addLayout(contrast_layout)

                    # 连接滑块信号
                    self.brightness_slider.valueChanged.connect(lambda v: self.brightness_label.setText(str(v)))
                    self.contrast_slider.valueChanged.connect(lambda v: self.contrast_label.setText(str(v)))

                    layout.addWidget(QLabel(""))

                    # 音频处理选项
                    layout.addWidget(QLabel("音频处理:"))

                    self.audio_normalize_check = QCheckBox("音频标准化")
                    self.audio_normalize_check.setChecked(True)
                    layout.addWidget(self.audio_normalize_check)

                    self.audio_denoise_check = QCheckBox("音频降噪")
                    layout.addWidget(self.audio_denoise_check)

                    # 音量调整
                    volume_layout = QHBoxLayout()
                    volume_layout.addWidget(QLabel("音量调整:"))
                    self.volume_slider = QSlider(Qt.Orientation.Horizontal)
                    self.volume_slider.setRange(-20, 20)
                    self.volume_slider.setValue(0)
                    volume_layout.addWidget(self.volume_slider)
                    self.volume_label = QLabel("0dB")
                    volume_layout.addWidget(self.volume_label)
                    layout.addLayout(volume_layout)

                    self.volume_slider.valueChanged.connect(lambda v: self.volume_label.setText(f"{v}dB"))

                    layout.addWidget(QLabel(""))

                    # 输出设置
                    layout.addWidget(QLabel("输出设置:"))

                    # 输出格式
                    format_layout = QHBoxLayout()
                    format_layout.addWidget(QLabel("输出格式:"))
                    self.format_combo = QComboBox()
                    self.format_combo.addItems(["MP4", "AVI", "MOV", "MKV"])
                    format_layout.addWidget(self.format_combo)
                    layout.addLayout(format_layout)

                    # 编码器
                    codec_layout = QHBoxLayout()
                    codec_layout.addWidget(QLabel("视频编码:"))
                    self.codec_combo = QComboBox()
                    self.codec_combo.addItems(["H.264", "H.265", "VP9", "AV1"])
                    codec_layout.addWidget(self.codec_combo)
                    layout.addLayout(codec_layout)

                    # 按钮区域
                    button_layout = QHBoxLayout()

                    preview_btn = QPushButton("预览效果")
                    preview_btn.clicked.connect(self.preview_effects)
                    button_layout.addWidget(preview_btn)

                    start_btn = QPushButton("开始处理")
                    start_btn.clicked.connect(self.start_processing)
                    button_layout.addWidget(start_btn)

                    cancel_btn = QPushButton("取消")
                    cancel_btn.clicked.connect(self.close)
                    button_layout.addWidget(cancel_btn)

                    layout.addLayout(button_layout)

                def preview_effects(self):
                    """预览处理效果"""
                    QMessageBox.information(self, "预览", "效果预览功能开发中...")

                def start_processing(self):
                    """开始视频处理"""
                    try:
                        # 选择输出路径
                        output_path, _ = QFileDialog.getSaveFileName(
                            self, "保存处理后的视频",
                            f"{Path(self.video_path).stem}_processed.mp4",
                            "视频文件 (*.mp4 *.avi *.mov *.mkv);;所有文件 (*)"
                        )

                        if not output_path:
                            return

                        # 收集处理参数
                        params = {
                            'input_path': self.video_path,
                            'output_path': output_path,
                            'resolution': self.resolution_combo.currentText(),
                            'fps': self.fps_combo.currentText(),
                            'bitrate': self.bitrate_combo.currentText(),
                            'stabilize': self.stabilize_check.isChecked(),
                            'denoise': self.denoise_check.isChecked(),
                            'sharpen': self.sharpen_check.isChecked(),
                            'brightness': self.brightness_slider.value(),
                            'contrast': self.contrast_slider.value(),
                            'audio_normalize': self.audio_normalize_check.isChecked(),
                            'audio_denoise': self.audio_denoise_check.isChecked(),
                            'volume': self.volume_slider.value(),
                            'format': self.format_combo.currentText(),
                            'codec': self.codec_combo.currentText()
                        }

                        # 显示进度对话框
                        progress = QProgressDialog("正在处理视频...", "取消", 0, 100, self)
                        progress.setWindowModality(Qt.WindowModality.WindowModal)
                        progress.show()

                        # 简化的视频处理
                        success = self.process_video_simple(params, progress)

                        progress.close()

                        if success:
                            QMessageBox.information(
                                self, "处理完成",
                                f"视频处理完成！\n输出文件: {output_path}"
                            )

                            # 询问是否添加到媒体库
                            reply = QMessageBox.question(
                                self, "添加到媒体库",
                                "是否将处理后的视频添加到媒体库？",
                                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                            )

                            if reply == QMessageBox.StandardButton.Yes:
                                self.parent_window.media_library.add_media_item(output_path)
                        else:
                            QMessageBox.critical(self, "处理失败", "视频处理失败，请检查设置和文件")

                        self.close()

                    except Exception as e:
                        QMessageBox.critical(self, "错误", f"视频处理失败:\n{str(e)}")

                def process_video_simple(self, params, progress):
                    """简化的视频处理"""
                    try:
                        import shutil
                        import time

                        # 模拟处理过程
                        for i in range(101):
                            if progress.wasCanceled():
                                return False

                            progress.setValue(i)
                            time.sleep(0.02)  # 模拟处理时间
                            QApplication.processEvents()

                        # 简单复制文件（实际应用中这里会是真正的视频处理）
                        shutil.copy2(params['input_path'], params['output_path'])

                        return True

                    except Exception as e:
                        print(f"视频处理失败: {e}")
                        return False

            # 显示对话框
            dialog = VideoPreprocessDialog(self, video_path)
            dialog.exec()

        except Exception as e:
            logger.error(f"视频预处理失败: {e}")
            QMessageBox.critical(self, "错误", f"视频预处理失败:\n{str(e)}")

    def quick_import_videos(self):
        """快速导入文件夹中的视频"""
        try:
            folder_path = QFileDialog.getExistingDirectory(self, "选择视频文件夹")
            if not folder_path:
                return

            # 扫描视频文件
            video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.m4v', '.3gp']
            video_files = []

            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in video_extensions):
                        video_files.append(os.path.join(root, file))

            if not video_files:
                QMessageBox.information(self, "导入完成", "在选择的文件夹中未找到视频文件")
                return

            # 显示导入对话框
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QListWidget, QProgressBar, QCheckBox

            class QuickImportDialog(QDialog):
                def __init__(self, parent=None, video_files=None):
                    super().__init__(parent)
                    self.setWindowTitle("快速导入视频")
                    self.setFixedSize(600, 500)
                    self.parent_window = parent
                    self.video_files = video_files or []

                    layout = QVBoxLayout(self)

                    # 信息
                    layout.addWidget(QLabel(f"找到 {len(self.video_files)} 个视频文件:"))

                    # 文件列表
                    self.file_list = QListWidget()
                    for video_file in self.video_files:
                        self.file_list.addItem(os.path.basename(video_file))
                    layout.addWidget(self.file_list)

                    # 选项
                    self.auto_add_timeline = QCheckBox("自动添加到时间轴")
                    layout.addWidget(self.auto_add_timeline)

                    self.generate_thumbnails = QCheckBox("生成缩略图")
                    self.generate_thumbnails.setChecked(True)
                    layout.addWidget(self.generate_thumbnails)

                    self.analyze_metadata = QCheckBox("分析媒体信息")
                    self.analyze_metadata.setChecked(True)
                    layout.addWidget(self.analyze_metadata)

                    # 进度条
                    self.progress_bar = QProgressBar()
                    self.progress_bar.setVisible(False)
                    layout.addWidget(self.progress_bar)

                    # 按钮
                    button_layout = QHBoxLayout()

                    import_btn = QPushButton("开始导入")
                    import_btn.clicked.connect(self.start_import)
                    button_layout.addWidget(import_btn)

                    select_all_btn = QPushButton("全选")
                    select_all_btn.clicked.connect(self.select_all)
                    button_layout.addWidget(select_all_btn)

                    cancel_btn = QPushButton("取消")
                    cancel_btn.clicked.connect(self.close)
                    button_layout.addWidget(cancel_btn)

                    layout.addLayout(button_layout)

                def select_all(self):
                    for i in range(self.file_list.count()):
                        self.file_list.item(i).setSelected(True)

                def start_import(self):
                    selected_indices = [i for i in range(self.file_list.count())
                                      if self.file_list.item(i).isSelected()]

                    if not selected_indices:
                        selected_indices = list(range(len(self.video_files)))  # 如果没有选择，导入所有

                    self.progress_bar.setVisible(True)
                    self.progress_bar.setRange(0, len(selected_indices))

                    imported_count = 0
                    for i, index in enumerate(selected_indices):
                        video_file = self.video_files[index]

                        try:
                            # 添加到媒体库
                            self.parent_window.media_library.add_media_item(video_file)

                            # 如果选择了自动添加到时间轴
                            if self.auto_add_timeline.isChecked():
                                self.add_to_timeline(video_file, i)

                            imported_count += 1

                        except Exception as e:
                            print(f"导入文件失败 {video_file}: {e}")

                        self.progress_bar.setValue(i + 1)
                        QApplication.processEvents()

                    QMessageBox.information(
                        self, "导入完成",
                        f"成功导入 {imported_count} 个视频文件"
                    )
                    self.close()

                def add_to_timeline(self, video_file, index):
                    """添加视频到时间轴"""
                    try:
                        # 确保有视频轨道
                        video_track_index = self.parent_window.find_track_by_type('video')
                        if video_track_index == -1:
                            video_track_index = self.parent_window.timeline.add_track('video', '视频轨道')

                        # 获取视频时长
                        duration = self.parent_window.timeline.get_media_duration(video_file)
                        if duration <= 0:
                            duration = 10.0  # 默认时长

                        # 计算开始时间（依次排列）
                        start_time = 0.0
                        for media_item in self.parent_window.timeline.tracks[video_track_index]['media_files']:
                            if isinstance(media_item, dict):
                                end_time = media_item['start_time'] + media_item['duration']
                                start_time = max(start_time, end_time)

                        # 创建媒体项
                        media_item = {
                            'file_path': video_file,
                            'start_time': start_time,
                            'duration': duration,
                            'name': os.path.basename(video_file)
                        }

                        # 添加到轨道
                        self.parent_window.timeline.tracks[video_track_index]['media_files'].append(media_item)

                    except Exception as e:
                        print(f"添加到时间轴失败: {e}")

            # 显示对话框
            dialog = QuickImportDialog(self, video_files)
            dialog.exec()

            # 更新时间轴显示
            self.timeline.update_total_duration()
            for i in range(len(self.timeline.tracks)):
                self.update_track_display(i)

        except Exception as e:
            logger.error(f"快速导入失败: {e}")
            QMessageBox.critical(self, "错误", f"快速导入失败:\n{str(e)}")

    def quick_preview_results(self):
        """快速预览结果"""
        try:
            # 检查时间轴是否有内容
            if not self.timeline.tracks or not any(track['media_files'] for track in self.timeline.tracks):
                QMessageBox.information(self, "提示", "时间轴上没有任何媒体文件")
                return

            # 显示预览选项对话框
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox, QSpinBox, QCheckBox

            class PreviewDialog(QDialog):
                def __init__(self, parent=None):
                    super().__init__(parent)
                    self.setWindowTitle("快速预览")
                    self.setFixedSize(400, 300)
                    self.parent_window = parent

                    layout = QVBoxLayout(self)

                    layout.addWidget(QLabel("预览设置:"))

                    # 预览质量
                    quality_layout = QHBoxLayout()
                    quality_layout.addWidget(QLabel("预览质量:"))
                    self.quality_combo = QComboBox()
                    self.quality_combo.addItems(["低质量(快速)", "中等质量", "高质量(慢速)"])
                    self.quality_combo.setCurrentText("中等质量")
                    quality_layout.addWidget(self.quality_combo)
                    layout.addLayout(quality_layout)

                    # 预览时长
                    duration_layout = QHBoxLayout()
                    duration_layout.addWidget(QLabel("预览时长(秒):"))
                    self.duration_spin = QSpinBox()
                    self.duration_spin.setRange(5, 60)
                    self.duration_spin.setValue(15)
                    duration_layout.addWidget(self.duration_spin)
                    layout.addLayout(duration_layout)

                    # 开始位置
                    start_layout = QHBoxLayout()
                    start_layout.addWidget(QLabel("开始位置(秒):"))
                    self.start_spin = QSpinBox()
                    self.start_spin.setRange(0, int(self.parent_window.timeline.total_duration))
                    start_layout.addWidget(self.start_spin)
                    layout.addLayout(start_layout)

                    # 选项
                    self.include_audio = QCheckBox("包含音频")
                    self.include_audio.setChecked(True)
                    layout.addWidget(self.include_audio)

                    self.add_watermark = QCheckBox("添加水印")
                    layout.addWidget(self.add_watermark)

                    self.save_preview = QCheckBox("保存预览文件")
                    layout.addWidget(self.save_preview)

                    # 按钮
                    button_layout = QHBoxLayout()

                    preview_btn = QPushButton("开始预览")
                    preview_btn.clicked.connect(self.start_preview)
                    button_layout.addWidget(preview_btn)

                    cancel_btn = QPushButton("取消")
                    cancel_btn.clicked.connect(self.close)
                    button_layout.addWidget(cancel_btn)

                    layout.addLayout(button_layout)

                def start_preview(self):
                    try:
                        quality = self.quality_combo.currentText()
                        duration = self.duration_spin.value()
                        start_pos = self.start_spin.value()
                        include_audio = self.include_audio.isChecked()
                        add_watermark = self.add_watermark.isChecked()
                        save_preview = self.save_preview.isChecked()

                        # 显示进度
                        from PySide6.QtWidgets import QProgressDialog
                        progress = QProgressDialog("正在生成预览...", "取消", 0, 100, self)
                        progress.setWindowModality(Qt.WindowModality.WindowModal)
                        progress.show()

                        # 模拟预览生成
                        for i in range(101):
                            if progress.wasCanceled():
                                return
                            progress.setValue(i)
                            QApplication.processEvents()
                            import time
                            time.sleep(0.01)

                        progress.close()

                        if save_preview:
                            # 保存预览文件
                            save_path, _ = QFileDialog.getSaveFileName(
                                self, "保存预览文件",
                                "preview.mp4",
                                "视频文件 (*.mp4);;所有文件 (*)"
                            )

                            if save_path:
                                # 简单复制一个现有文件作为预览（实际应用中会生成真正的预览）
                                first_video = self.get_first_video_file()
                                if first_video:
                                    import shutil
                                    shutil.copy2(first_video, save_path)
                                    QMessageBox.information(
                                        self, "预览完成",
                                        f"预览文件已保存到:\n{save_path}"
                                    )
                        else:
                            # 在视频播放器中播放预览
                            first_video = self.get_first_video_file()
                            if first_video:
                                # 创建video_info对象
                                class VideoInfo:
                                    def __init__(self, duration, file_path, name):
                                        self.duration = duration
                                        self.file_path = file_path
                                        self.name = name

                                duration = self.parent_window.timeline.get_media_duration(first_video)
                                video_info = VideoInfo(duration, first_video, Path(first_video).name)

                                try:
                                    self.parent_window.video_player.load_video(first_video, video_info)
                                except TypeError:
                                    try:
                                        self.parent_window.video_player.load_video(first_video)
                                    except:
                                        pass

                                if hasattr(self.parent_window.video_player, 'set_position'):
                                    self.parent_window.video_player.set_position(start_pos)
                                if hasattr(self.parent_window.video_player, 'play'):
                                    self.parent_window.video_player.play()
                                QMessageBox.information(self, "预览开始", "预览已在视频播放器中开始播放")

                        self.close()

                    except Exception as e:
                        QMessageBox.critical(self, "错误", f"预览失败:\n{str(e)}")

                def get_first_video_file(self):
                    """获取第一个视频文件"""
                    for track in self.parent_window.timeline.tracks:
                        if track['type'] == 'video':
                            for media_item in track['media_files']:
                                if isinstance(media_item, dict):
                                    file_path = media_item['file_path']
                                    if not file_path.startswith('placeholder_') and os.path.exists(file_path):
                                        return file_path
                    return None

            # 显示对话框
            dialog = PreviewDialog(self)
            dialog.exec()

        except Exception as e:
            logger.error(f"快速预览失败: {e}")
            QMessageBox.critical(self, "错误", f"快速预览失败:\n{str(e)}")

    def show_effects_panel(self):
        """显示特效面板"""
        try:
            # 创建特效面板对话框
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QListWidget, QTabWidget, QWidget, QSlider, QComboBox, QCheckBox

            class EffectsPanel(QDialog):
                def __init__(self, parent=None):
                    super().__init__(parent)
                    self.setWindowTitle("视频特效")
                    self.setFixedSize(800, 600)
                    self.parent_window = parent

                    layout = QVBoxLayout(self)

                    # 创建标签页
                    tab_widget = QTabWidget()

                    # 滤镜标签页
                    filters_tab = self.create_filters_tab()
                    tab_widget.addTab(filters_tab, "滤镜")

                    # 转场标签页
                    transitions_tab = self.create_transitions_tab()
                    tab_widget.addTab(transitions_tab, "转场")

                    # 特效标签页
                    effects_tab = self.create_effects_tab()
                    tab_widget.addTab(effects_tab, "特效")

                    # 理发店专用标签页
                    salon_tab = self.create_salon_effects_tab()
                    tab_widget.addTab(salon_tab, "理发店专用")

                    layout.addWidget(tab_widget)

                    # 底部按钮
                    button_layout = QHBoxLayout()

                    preview_btn = QPushButton("预览效果")
                    preview_btn.clicked.connect(self.preview_effects)
                    button_layout.addWidget(preview_btn)

                    apply_btn = QPushButton("应用到选中片段")
                    apply_btn.clicked.connect(self.apply_effects)
                    button_layout.addWidget(apply_btn)

                    reset_btn = QPushButton("重置所有")
                    reset_btn.clicked.connect(self.reset_effects)
                    button_layout.addWidget(reset_btn)

                    close_btn = QPushButton("关闭")
                    close_btn.clicked.connect(self.close)
                    button_layout.addWidget(close_btn)

                    layout.addLayout(button_layout)

                def create_filters_tab(self):
                    """创建滤镜标签页"""
                    widget = QWidget()
                    layout = QVBoxLayout(widget)

                    # 基础滤镜
                    layout.addWidget(QLabel("基础滤镜:"))

                    # 亮度
                    brightness_layout = QHBoxLayout()
                    brightness_layout.addWidget(QLabel("亮度:"))
                    self.brightness_slider = QSlider(Qt.Orientation.Horizontal)
                    self.brightness_slider.setRange(-100, 100)
                    self.brightness_slider.setValue(0)
                    brightness_layout.addWidget(self.brightness_slider)
                    self.brightness_label = QLabel("0")
                    brightness_layout.addWidget(self.brightness_label)
                    layout.addLayout(brightness_layout)

                    # 对比度
                    contrast_layout = QHBoxLayout()
                    contrast_layout.addWidget(QLabel("对比度:"))
                    self.contrast_slider = QSlider(Qt.Orientation.Horizontal)
                    self.contrast_slider.setRange(-100, 100)
                    self.contrast_slider.setValue(0)
                    contrast_layout.addWidget(self.contrast_slider)
                    self.contrast_label = QLabel("0")
                    contrast_layout.addWidget(self.contrast_label)
                    layout.addLayout(contrast_layout)

                    # 饱和度
                    saturation_layout = QHBoxLayout()
                    saturation_layout.addWidget(QLabel("饱和度:"))
                    self.saturation_slider = QSlider(Qt.Orientation.Horizontal)
                    self.saturation_slider.setRange(-100, 100)
                    self.saturation_slider.setValue(0)
                    saturation_layout.addWidget(self.saturation_slider)
                    self.saturation_label = QLabel("0")
                    saturation_layout.addWidget(self.saturation_label)
                    layout.addLayout(saturation_layout)

                    # 连接滑块信号 - 🔧 修改：添加实时效果应用
                    self.brightness_slider.valueChanged.connect(lambda v: self.on_brightness_changed(v))
                    self.contrast_slider.valueChanged.connect(lambda v: self.on_contrast_changed(v))
                    self.saturation_slider.valueChanged.connect(lambda v: self.on_saturation_changed(v))

                    # 预设滤镜
                    layout.addWidget(QLabel(""))
                    layout.addWidget(QLabel("预设滤镜:"))

                    preset_layout = QHBoxLayout()

                    vintage_btn = QPushButton("复古")
                    vintage_btn.clicked.connect(lambda: self.apply_preset_filter("vintage"))
                    preset_layout.addWidget(vintage_btn)

                    grayscale_btn = QPushButton("黑白")
                    grayscale_btn.clicked.connect(lambda: self.apply_preset_filter("grayscale"))
                    preset_layout.addWidget(grayscale_btn)

                    sepia_btn = QPushButton("棕褐色")
                    sepia_btn.clicked.connect(lambda: self.apply_preset_filter("sepia"))
                    preset_layout.addWidget(sepia_btn)

                    layout.addLayout(preset_layout)

                    # 模糊和锐化
                    layout.addWidget(QLabel(""))
                    layout.addWidget(QLabel("模糊和锐化:"))

                    blur_layout = QHBoxLayout()
                    blur_layout.addWidget(QLabel("模糊:"))
                    self.blur_slider = QSlider(Qt.Orientation.Horizontal)
                    self.blur_slider.setRange(0, 10)
                    self.blur_slider.setValue(0)
                    blur_layout.addWidget(self.blur_slider)
                    self.blur_label = QLabel("0")
                    blur_layout.addWidget(self.blur_label)
                    layout.addLayout(blur_layout)

                    sharpen_layout = QHBoxLayout()
                    sharpen_layout.addWidget(QLabel("锐化:"))
                    self.sharpen_slider = QSlider(Qt.Orientation.Horizontal)
                    self.sharpen_slider.setRange(0, 10)
                    self.sharpen_slider.setValue(0)
                    sharpen_layout.addWidget(self.sharpen_slider)
                    self.sharpen_label = QLabel("0")
                    sharpen_layout.addWidget(self.sharpen_label)
                    layout.addLayout(sharpen_layout)

                    self.blur_slider.valueChanged.connect(lambda v: self.blur_label.setText(str(v)))
                    self.sharpen_slider.valueChanged.connect(lambda v: self.sharpen_label.setText(str(v)))

                    layout.addStretch()
                    return widget

                def create_transitions_tab(self):
                    """创建转场标签页"""
                    widget = QWidget()
                    layout = QVBoxLayout(widget)

                    layout.addWidget(QLabel("转场效果:"))

                    # 转场类型选择
                    transition_layout = QHBoxLayout()
                    transition_layout.addWidget(QLabel("转场类型:"))
                    self.transition_combo = QComboBox()
                    self.transition_combo.addItems([
                        "淡入淡出", "交叉溶解", "推拉", "擦除", "旋转",
                        "缩放", "翻页", "马赛克", "光晕", "闪光"
                    ])
                    transition_layout.addWidget(self.transition_combo)
                    layout.addLayout(transition_layout)

                    # 转场时长
                    duration_layout = QHBoxLayout()
                    duration_layout.addWidget(QLabel("转场时长(秒):"))
                    self.transition_duration = QSlider(Qt.Orientation.Horizontal)
                    self.transition_duration.setRange(1, 50)  # 0.1-5.0秒
                    self.transition_duration.setValue(10)  # 默认1.0秒
                    duration_layout.addWidget(self.transition_duration)
                    self.duration_label = QLabel("1.0")
                    duration_layout.addWidget(self.duration_label)
                    layout.addLayout(duration_layout)

                    self.transition_duration.valueChanged.connect(lambda v: self.duration_label.setText(f"{v/10:.1f}"))

                    # 转场方向（对于有方向的转场）
                    direction_layout = QHBoxLayout()
                    direction_layout.addWidget(QLabel("转场方向:"))
                    self.direction_combo = QComboBox()
                    self.direction_combo.addItems(["左到右", "右到左", "上到下", "下到上"])
                    direction_layout.addWidget(self.direction_combo)
                    layout.addLayout(direction_layout)

                    # 转场强度
                    intensity_layout = QHBoxLayout()
                    intensity_layout.addWidget(QLabel("转场强度:"))
                    self.transition_intensity = QSlider(Qt.Orientation.Horizontal)
                    self.transition_intensity.setRange(1, 100)
                    self.transition_intensity.setValue(50)
                    intensity_layout.addWidget(self.transition_intensity)
                    self.intensity_label = QLabel("50%")
                    intensity_layout.addWidget(self.intensity_label)
                    layout.addLayout(intensity_layout)

                    self.transition_intensity.valueChanged.connect(lambda v: self.intensity_label.setText(f"{v}%"))

                    # 自动应用选项
                    self.auto_apply_transitions = QCheckBox("自动在片段间添加转场")
                    layout.addWidget(self.auto_apply_transitions)

                    layout.addStretch()
                    return widget

                def create_effects_tab(self):
                    """创建特效标签页"""
                    widget = QWidget()
                    layout = QVBoxLayout(widget)

                    layout.addWidget(QLabel("视频特效:"))

                    # 特效列表
                    effects_list = QListWidget()
                    effects = [
                        "粒子效果", "光晕效果", "闪光效果", "雨滴效果",
                        "雪花效果", "火花效果", "烟雾效果", "彩虹效果",
                        "镜头光晕", "色彩分离", "故障效果", "扫描线"
                    ]
                    for effect in effects:
                        effects_list.addItem(effect)
                    layout.addWidget(effects_list)

                    # 特效参数
                    layout.addWidget(QLabel("特效参数:"))

                    # 特效强度
                    effect_intensity_layout = QHBoxLayout()
                    effect_intensity_layout.addWidget(QLabel("强度:"))
                    self.effect_intensity = QSlider(Qt.Orientation.Horizontal)
                    self.effect_intensity.setRange(1, 100)
                    self.effect_intensity.setValue(50)
                    effect_intensity_layout.addWidget(self.effect_intensity)
                    self.effect_intensity_label = QLabel("50%")
                    effect_intensity_layout.addWidget(self.effect_intensity_label)
                    layout.addLayout(effect_intensity_layout)

                    # 特效速度
                    effect_speed_layout = QHBoxLayout()
                    effect_speed_layout.addWidget(QLabel("速度:"))
                    self.effect_speed = QSlider(Qt.Orientation.Horizontal)
                    self.effect_speed.setRange(1, 100)
                    self.effect_speed.setValue(50)
                    effect_speed_layout.addWidget(self.effect_speed)
                    self.effect_speed_label = QLabel("50%")
                    effect_speed_layout.addWidget(self.effect_speed_label)
                    layout.addLayout(effect_speed_layout)

                    self.effect_intensity.valueChanged.connect(lambda v: self.effect_intensity_label.setText(f"{v}%"))
                    self.effect_speed.valueChanged.connect(lambda v: self.effect_speed_label.setText(f"{v}%"))

                    return widget

                def create_salon_effects_tab(self):
                    """创建理发店专用特效标签页"""
                    widget = QWidget()
                    layout = QVBoxLayout(widget)

                    layout.addWidget(QLabel("理发店专用特效:"))

                    # 理发店特效列表
                    salon_effects_list = QListWidget()
                    salon_effects = [
                        "剪刀光效", "发丝粒子", "色彩溶解", "镜面反射",
                        "洗发泡泡", "发丝飘动", "理发店闪光", "色彩飞溅",
                        "剪切闪光", "造型揭示"
                    ]
                    for effect in salon_effects:
                        salon_effects_list.addItem(effect)
                    layout.addWidget(salon_effects_list)

                    # 理发店特效参数
                    layout.addWidget(QLabel("特效设置:"))

                    # 品牌色彩
                    color_layout = QHBoxLayout()
                    color_layout.addWidget(QLabel("品牌色彩:"))
                    self.brand_color_combo = QComboBox()
                    self.brand_color_combo.addItems(["金色", "银色", "蓝色", "红色", "绿色", "紫色"])
                    color_layout.addWidget(self.brand_color_combo)
                    layout.addLayout(color_layout)

                    # 特效密度
                    density_layout = QHBoxLayout()
                    density_layout.addWidget(QLabel("特效密度:"))
                    self.effect_density = QSlider(Qt.Orientation.Horizontal)
                    self.effect_density.setRange(1, 100)
                    self.effect_density.setValue(30)
                    density_layout.addWidget(self.effect_density)
                    self.density_label = QLabel("30%")
                    density_layout.addWidget(self.density_label)
                    layout.addLayout(density_layout)

                    self.effect_density.valueChanged.connect(lambda v: self.density_label.setText(f"{v}%"))

                    # 自动检测选项
                    self.auto_detect_cuts = QCheckBox("自动检测剪发动作")
                    layout.addWidget(self.auto_detect_cuts)

                    self.auto_add_sparkle = QCheckBox("在关键时刻自动添加闪光")
                    layout.addWidget(self.auto_add_sparkle)

                    layout.addStretch()
                    return widget

                def apply_preset_filter(self, filter_type):
                    """应用预设滤镜"""
                    presets = {
                        "vintage": {"brightness": -10, "contrast": 20, "saturation": -30},
                        "grayscale": {"brightness": 0, "contrast": 10, "saturation": -100},
                        "sepia": {"brightness": 5, "contrast": 15, "saturation": -20}
                    }

                    if filter_type in presets:
                        preset = presets[filter_type]
                        self.brightness_slider.setValue(preset["brightness"])
                        self.contrast_slider.setValue(preset["contrast"])
                        self.saturation_slider.setValue(preset["saturation"])

                def preview_effects(self):
                    """预览特效"""
                    QMessageBox.information(self, "预览", "特效预览功能开发中...")

                def apply_effects(self):
                    """应用特效到选中片段"""
                    try:
                        # 收集所有特效参数
                        effects_data = {
                            'filters': {
                                'brightness': self.brightness_slider.value(),
                                'contrast': self.contrast_slider.value(),
                                'saturation': self.saturation_slider.value(),
                                'blur': self.blur_slider.value(),
                                'sharpen': self.sharpen_slider.value()
                            },
                            'transitions': {
                                'type': self.transition_combo.currentText(),
                                'duration': self.transition_duration.value() / 10.0,
                                'direction': self.direction_combo.currentText(),
                                'intensity': self.transition_intensity.value()
                            },
                            'effects': {
                                'intensity': self.effect_intensity.value(),
                                'speed': self.effect_speed.value()
                            },
                            'salon_effects': {
                                'brand_color': self.brand_color_combo.currentText(),
                                'density': self.effect_density.value(),
                                'auto_detect_cuts': self.auto_detect_cuts.isChecked(),
                                'auto_add_sparkle': self.auto_add_sparkle.isChecked()
                            }
                        }

                        # 应用到选中的媒体项
                        self.apply_effects_to_selection(effects_data)

                        QMessageBox.information(self, "应用完成", "特效已应用到选中片段")
                        self.close()

                    except Exception as e:
                        QMessageBox.critical(self, "错误", f"应用特效失败:\n{str(e)}")

                def apply_effects_to_selection(self, effects_data):
                    """将特效应用到选中的媒体项"""
                    try:
                        # 遍历所有轨道，查找选中的媒体项
                        for track in self.parent_window.timeline.tracks:
                            for media_item in track['media_files']:
                                if isinstance(media_item, dict):
                                    # 添加特效数据到媒体项
                                    if 'effects' not in media_item:
                                        media_item['effects'] = {}

                                    media_item['effects'].update(effects_data)
                                    media_item['has_effects'] = True

                        # 更新轨道显示
                        for i in range(len(self.parent_window.timeline.tracks)):
                            self.parent_window.update_track_display(i)

                    except Exception as e:
                        print(f"应用特效到选中项失败: {e}")

                def reset_effects(self):
                    """重置所有特效"""
                    # 重置滤镜
                    self.brightness_slider.setValue(0)
                    self.contrast_slider.setValue(0)
                    self.saturation_slider.setValue(0)
                    self.blur_slider.setValue(0)
                    self.sharpen_slider.setValue(0)

                    # 重置转场
                    self.transition_combo.setCurrentIndex(0)
                    self.transition_duration.setValue(10)
                    self.direction_combo.setCurrentIndex(0)
                    self.transition_intensity.setValue(50)

                    # 重置特效
                    self.effect_intensity.setValue(50)
                    self.effect_speed.setValue(50)

                    # 重置理发店特效
                    self.brand_color_combo.setCurrentIndex(0)
                    self.effect_density.setValue(30)
                    self.auto_detect_cuts.setChecked(False)
                    self.auto_add_sparkle.setChecked(False)

            # 显示特效面板
            panel = EffectsPanel(self)
            panel.exec()

        except Exception as e:
            logger.error(f"显示特效面板失败: {e}")
            QMessageBox.critical(self, "错误", f"显示特效面板失败:\n{str(e)}")

    def show_color_correction_panel(self):
        """显示色彩校正面板"""
        try:
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QSlider, QTabWidget, QWidget, QComboBox, QCheckBox, QGroupBox

            class ColorCorrectionPanel(QDialog):
                def __init__(self, parent=None):
                    super().__init__(parent)
                    self.setWindowTitle("色彩校正")
                    self.setFixedSize(600, 700)
                    self.parent_window = parent

                    layout = QVBoxLayout(self)

                    # 创建标签页
                    tab_widget = QTabWidget()

                    # 基础调色标签页
                    basic_tab = self.create_basic_color_tab()
                    tab_widget.addTab(basic_tab, "基础调色")

                    # 高级调色标签页
                    advanced_tab = self.create_advanced_color_tab()
                    tab_widget.addTab(advanced_tab, "高级调色")

                    # 色彩匹配标签页
                    matching_tab = self.create_color_matching_tab()
                    tab_widget.addTab(matching_tab, "色彩匹配")

                    layout.addWidget(tab_widget)

                    # 底部按钮
                    button_layout = QHBoxLayout()

                    preview_btn = QPushButton("实时预览")
                    preview_btn.clicked.connect(self.toggle_preview)
                    button_layout.addWidget(preview_btn)

                    apply_btn = QPushButton("应用调色")
                    apply_btn.clicked.connect(self.apply_color_correction)
                    button_layout.addWidget(apply_btn)

                    reset_btn = QPushButton("重置")
                    reset_btn.clicked.connect(self.reset_color_correction)
                    button_layout.addWidget(reset_btn)

                    close_btn = QPushButton("关闭")
                    close_btn.clicked.connect(self.close)
                    button_layout.addWidget(close_btn)

                    layout.addLayout(button_layout)

                    # 预览状态
                    self.preview_enabled = False

                def create_basic_color_tab(self):
                    """创建基础调色标签页"""
                    widget = QWidget()
                    layout = QVBoxLayout(widget)

                    # 曝光组
                    exposure_group = QGroupBox("曝光")
                    exposure_layout = QVBoxLayout(exposure_group)

                    # 曝光
                    exposure_layout.addWidget(QLabel("曝光:"))
                    exposure_slider_layout = QHBoxLayout()
                    self.exposure_slider = QSlider(Qt.Orientation.Horizontal)
                    self.exposure_slider.setRange(-200, 200)
                    self.exposure_slider.setValue(0)
                    exposure_slider_layout.addWidget(self.exposure_slider)
                    self.exposure_label = QLabel("0")
                    exposure_slider_layout.addWidget(self.exposure_label)
                    exposure_layout.addLayout(exposure_slider_layout)

                    # 高光
                    exposure_layout.addWidget(QLabel("高光:"))
                    highlights_slider_layout = QHBoxLayout()
                    self.highlights_slider = QSlider(Qt.Orientation.Horizontal)
                    self.highlights_slider.setRange(-100, 100)
                    self.highlights_slider.setValue(0)
                    highlights_slider_layout.addWidget(self.highlights_slider)
                    self.highlights_label = QLabel("0")
                    highlights_slider_layout.addWidget(self.highlights_label)
                    exposure_layout.addLayout(highlights_slider_layout)

                    # 阴影
                    exposure_layout.addWidget(QLabel("阴影:"))
                    shadows_slider_layout = QHBoxLayout()
                    self.shadows_slider = QSlider(Qt.Orientation.Horizontal)
                    self.shadows_slider.setRange(-100, 100)
                    self.shadows_slider.setValue(0)
                    shadows_slider_layout.addWidget(self.shadows_slider)
                    self.shadows_label = QLabel("0")
                    shadows_slider_layout.addWidget(self.shadows_label)
                    exposure_layout.addLayout(shadows_slider_layout)

                    layout.addWidget(exposure_group)

                    # 色彩组
                    color_group = QGroupBox("色彩")
                    color_layout = QVBoxLayout(color_group)

                    # 色温
                    color_layout.addWidget(QLabel("色温:"))
                    temperature_slider_layout = QHBoxLayout()
                    self.temperature_slider = QSlider(Qt.Orientation.Horizontal)
                    self.temperature_slider.setRange(-100, 100)
                    self.temperature_slider.setValue(0)
                    temperature_slider_layout.addWidget(self.temperature_slider)
                    self.temperature_label = QLabel("0")
                    temperature_slider_layout.addWidget(self.temperature_label)
                    color_layout.addLayout(temperature_slider_layout)

                    # 色调
                    color_layout.addWidget(QLabel("色调:"))
                    tint_slider_layout = QHBoxLayout()
                    self.tint_slider = QSlider(Qt.Orientation.Horizontal)
                    self.tint_slider.setRange(-100, 100)
                    self.tint_slider.setValue(0)
                    tint_slider_layout.addWidget(self.tint_slider)
                    self.tint_label = QLabel("0")
                    tint_slider_layout.addWidget(self.tint_label)
                    color_layout.addLayout(tint_slider_layout)

                    # 饱和度
                    color_layout.addWidget(QLabel("饱和度:"))
                    vibrance_slider_layout = QHBoxLayout()
                    self.vibrance_slider = QSlider(Qt.Orientation.Horizontal)
                    self.vibrance_slider.setRange(-100, 100)
                    self.vibrance_slider.setValue(0)
                    vibrance_slider_layout.addWidget(self.vibrance_slider)
                    self.vibrance_label = QLabel("0")
                    vibrance_slider_layout.addWidget(self.vibrance_label)
                    color_layout.addLayout(vibrance_slider_layout)

                    layout.addWidget(color_group)

                    # 连接滑块信号
                    self.exposure_slider.valueChanged.connect(lambda v: self.exposure_label.setText(str(v)))
                    self.highlights_slider.valueChanged.connect(lambda v: self.highlights_label.setText(str(v)))
                    self.shadows_slider.valueChanged.connect(lambda v: self.shadows_label.setText(str(v)))
                    self.temperature_slider.valueChanged.connect(lambda v: self.temperature_label.setText(str(v)))
                    self.tint_slider.valueChanged.connect(lambda v: self.tint_label.setText(str(v)))
                    self.vibrance_slider.valueChanged.connect(lambda v: self.vibrance_label.setText(str(v)))

                    # 预设
                    presets_group = QGroupBox("预设")
                    presets_layout = QHBoxLayout(presets_group)

                    warm_btn = QPushButton("暖色调")
                    warm_btn.clicked.connect(lambda: self.apply_preset("warm"))
                    presets_layout.addWidget(warm_btn)

                    cool_btn = QPushButton("冷色调")
                    cool_btn.clicked.connect(lambda: self.apply_preset("cool"))
                    presets_layout.addWidget(cool_btn)

                    natural_btn = QPushButton("自然")
                    natural_btn.clicked.connect(lambda: self.apply_preset("natural"))
                    presets_layout.addWidget(natural_btn)

                    layout.addWidget(presets_group)

                    layout.addStretch()
                    return widget

                def create_advanced_color_tab(self):
                    """创建高级调色标签页"""
                    widget = QWidget()
                    layout = QVBoxLayout(widget)

                    # RGB调整
                    rgb_group = QGroupBox("RGB通道")
                    rgb_layout = QVBoxLayout(rgb_group)

                    # 红色通道
                    rgb_layout.addWidget(QLabel("红色:"))
                    red_slider_layout = QHBoxLayout()
                    self.red_slider = QSlider(Qt.Orientation.Horizontal)
                    self.red_slider.setRange(-100, 100)
                    self.red_slider.setValue(0)
                    red_slider_layout.addWidget(self.red_slider)
                    self.red_label = QLabel("0")
                    red_slider_layout.addWidget(self.red_label)
                    rgb_layout.addLayout(red_slider_layout)

                    # 绿色通道
                    rgb_layout.addWidget(QLabel("绿色:"))
                    green_slider_layout = QHBoxLayout()
                    self.green_slider = QSlider(Qt.Orientation.Horizontal)
                    self.green_slider.setRange(-100, 100)
                    self.green_slider.setValue(0)
                    green_slider_layout.addWidget(self.green_slider)
                    self.green_label = QLabel("0")
                    green_slider_layout.addWidget(self.green_label)
                    rgb_layout.addLayout(green_slider_layout)

                    # 蓝色通道
                    rgb_layout.addWidget(QLabel("蓝色:"))
                    blue_slider_layout = QHBoxLayout()
                    self.blue_slider = QSlider(Qt.Orientation.Horizontal)
                    self.blue_slider.setRange(-100, 100)
                    self.blue_slider.setValue(0)
                    blue_slider_layout.addWidget(self.blue_slider)
                    self.blue_label = QLabel("0")
                    blue_slider_layout.addWidget(self.blue_label)
                    rgb_layout.addLayout(blue_slider_layout)

                    layout.addWidget(rgb_group)

                    # HSL调整
                    hsl_group = QGroupBox("HSL调整")
                    hsl_layout = QVBoxLayout(hsl_group)

                    # 色相
                    hsl_layout.addWidget(QLabel("色相:"))
                    hue_slider_layout = QHBoxLayout()
                    self.hue_slider = QSlider(Qt.Orientation.Horizontal)
                    self.hue_slider.setRange(-180, 180)
                    self.hue_slider.setValue(0)
                    hue_slider_layout.addWidget(self.hue_slider)
                    self.hue_label = QLabel("0°")
                    hue_slider_layout.addWidget(self.hue_label)
                    hsl_layout.addLayout(hue_slider_layout)

                    # 饱和度
                    hsl_layout.addWidget(QLabel("饱和度:"))
                    saturation_slider_layout = QHBoxLayout()
                    self.saturation_slider = QSlider(Qt.Orientation.Horizontal)
                    self.saturation_slider.setRange(-100, 100)
                    self.saturation_slider.setValue(0)
                    saturation_slider_layout.addWidget(self.saturation_slider)
                    self.saturation_label = QLabel("0")
                    saturation_slider_layout.addWidget(self.saturation_label)
                    hsl_layout.addLayout(saturation_slider_layout)

                    # 明度
                    hsl_layout.addWidget(QLabel("明度:"))
                    lightness_slider_layout = QHBoxLayout()
                    self.lightness_slider = QSlider(Qt.Orientation.Horizontal)
                    self.lightness_slider.setRange(-100, 100)
                    self.lightness_slider.setValue(0)
                    lightness_slider_layout.addWidget(self.lightness_slider)
                    self.lightness_label = QLabel("0")
                    lightness_slider_layout.addWidget(self.lightness_label)
                    hsl_layout.addLayout(lightness_slider_layout)

                    layout.addWidget(hsl_group)

                    # 连接滑块信号
                    self.red_slider.valueChanged.connect(lambda v: self.red_label.setText(str(v)))
                    self.green_slider.valueChanged.connect(lambda v: self.green_label.setText(str(v)))
                    self.blue_slider.valueChanged.connect(lambda v: self.blue_label.setText(str(v)))
                    self.hue_slider.valueChanged.connect(lambda v: self.hue_label.setText(f"{v}°"))
                    self.saturation_slider.valueChanged.connect(lambda v: self.saturation_label.setText(str(v)))
                    self.lightness_slider.valueChanged.connect(lambda v: self.lightness_label.setText(str(v)))

                    layout.addStretch()
                    return widget

                def create_color_matching_tab(self):
                    """创建色彩匹配标签页"""
                    widget = QWidget()
                    layout = QVBoxLayout(widget)

                    # 自动色彩匹配
                    auto_group = QGroupBox("自动匹配")
                    auto_layout = QVBoxLayout(auto_group)

                    # 参考片段选择
                    ref_layout = QHBoxLayout()
                    ref_layout.addWidget(QLabel("参考片段:"))
                    self.reference_combo = QComboBox()
                    self.reference_combo.addItems(["选择参考片段...", "片段1", "片段2", "片段3"])
                    ref_layout.addWidget(self.reference_combo)
                    auto_layout.addLayout(ref_layout)

                    # 匹配强度
                    match_layout = QHBoxLayout()
                    match_layout.addWidget(QLabel("匹配强度:"))
                    self.match_strength = QSlider(Qt.Orientation.Horizontal)
                    self.match_strength.setRange(0, 100)
                    self.match_strength.setValue(70)
                    match_layout.addWidget(self.match_strength)
                    self.match_strength_label = QLabel("70%")
                    match_layout.addWidget(self.match_strength_label)
                    auto_layout.addLayout(match_layout)

                    self.match_strength.valueChanged.connect(lambda v: self.match_strength_label.setText(f"{v}%"))

                    # 匹配选项
                    self.match_exposure = QCheckBox("匹配曝光")
                    self.match_exposure.setChecked(True)
                    auto_layout.addWidget(self.match_exposure)

                    self.match_color_temp = QCheckBox("匹配色温")
                    self.match_color_temp.setChecked(True)
                    auto_layout.addWidget(self.match_color_temp)

                    self.match_saturation = QCheckBox("匹配饱和度")
                    self.match_saturation.setChecked(True)
                    auto_layout.addWidget(self.match_saturation)

                    # 应用按钮
                    apply_match_btn = QPushButton("应用自动匹配")
                    apply_match_btn.clicked.connect(self.apply_auto_match)
                    auto_layout.addWidget(apply_match_btn)

                    layout.addWidget(auto_group)

                    # LUT应用
                    lut_group = QGroupBox("LUT调色")
                    lut_layout = QVBoxLayout(lut_group)

                    # LUT选择
                    lut_select_layout = QHBoxLayout()
                    lut_select_layout.addWidget(QLabel("LUT文件:"))
                    self.lut_combo = QComboBox()
                    self.lut_combo.addItems([
                        "无", "电影风格", "复古胶片", "清新自然",
                        "暖色调", "冷色调", "高对比", "柔和"
                    ])
                    lut_select_layout.addWidget(self.lut_combo)
                    lut_layout.addLayout(lut_select_layout)

                    # LUT强度
                    lut_intensity_layout = QHBoxLayout()
                    lut_intensity_layout.addWidget(QLabel("LUT强度:"))
                    self.lut_intensity = QSlider(Qt.Orientation.Horizontal)
                    self.lut_intensity.setRange(0, 100)
                    self.lut_intensity.setValue(100)
                    lut_intensity_layout.addWidget(self.lut_intensity)
                    self.lut_intensity_label = QLabel("100%")
                    lut_intensity_layout.addWidget(self.lut_intensity_label)
                    lut_layout.addLayout(lut_intensity_layout)

                    self.lut_intensity.valueChanged.connect(lambda v: self.lut_intensity_label.setText(f"{v}%"))

                    layout.addWidget(lut_group)

                    layout.addStretch()
                    return widget

                def apply_preset(self, preset_type):
                    """应用预设"""
                    presets = {
                        "warm": {
                            "temperature": 30, "tint": 10, "vibrance": 20,
                            "exposure": 10, "highlights": -20, "shadows": 30
                        },
                        "cool": {
                            "temperature": -30, "tint": -10, "vibrance": 15,
                            "exposure": 5, "highlights": -15, "shadows": 20
                        },
                        "natural": {
                            "temperature": 0, "tint": 0, "vibrance": 10,
                            "exposure": 0, "highlights": -10, "shadows": 15
                        }
                    }

                    if preset_type in presets:
                        preset = presets[preset_type]
                        self.temperature_slider.setValue(preset["temperature"])
                        self.tint_slider.setValue(preset["tint"])
                        self.vibrance_slider.setValue(preset["vibrance"])
                        self.exposure_slider.setValue(preset["exposure"])
                        self.highlights_slider.setValue(preset["highlights"])
                        self.shadows_slider.setValue(preset["shadows"])

                def apply_auto_match(self):
                    """应用自动色彩匹配"""
                    QMessageBox.information(self, "自动匹配", "自动色彩匹配功能开发中...")

                def toggle_preview(self):
                    """切换实时预览"""
                    self.preview_enabled = not self.preview_enabled
                    status = "已启用" if self.preview_enabled else "已禁用"
                    QMessageBox.information(self, "实时预览", f"实时预览{status}")

                def apply_color_correction(self):
                    """应用色彩校正"""
                    try:
                        # 收集所有色彩校正参数
                        color_data = {
                            'basic': {
                                'exposure': self.exposure_slider.value(),
                                'highlights': self.highlights_slider.value(),
                                'shadows': self.shadows_slider.value(),
                                'temperature': self.temperature_slider.value(),
                                'tint': self.tint_slider.value(),
                                'vibrance': self.vibrance_slider.value()
                            },
                            'advanced': {
                                'red': self.red_slider.value(),
                                'green': self.green_slider.value(),
                                'blue': self.blue_slider.value(),
                                'hue': self.hue_slider.value(),
                                'saturation': self.saturation_slider.value(),
                                'lightness': self.lightness_slider.value()
                            },
                            'matching': {
                                'reference': self.reference_combo.currentText(),
                                'strength': self.match_strength.value(),
                                'match_exposure': self.match_exposure.isChecked(),
                                'match_color_temp': self.match_color_temp.isChecked(),
                                'match_saturation': self.match_saturation.isChecked(),
                                'lut': self.lut_combo.currentText(),
                                'lut_intensity': self.lut_intensity.value()
                            }
                        }

                        # 应用到选中的媒体项
                        self.apply_color_to_selection(color_data)

                        QMessageBox.information(self, "应用完成", "色彩校正已应用到选中片段")
                        self.close()

                    except Exception as e:
                        QMessageBox.critical(self, "错误", f"应用色彩校正失败:\n{str(e)}")

                def apply_color_to_selection(self, color_data):
                    """将色彩校正应用到选中的媒体项"""
                    try:
                        # 遍历所有轨道，查找选中的媒体项
                        for track in self.parent_window.timeline.tracks:
                            for media_item in track['media_files']:
                                if isinstance(media_item, dict):
                                    # 添加色彩校正数据到媒体项
                                    if 'color_correction' not in media_item:
                                        media_item['color_correction'] = {}

                                    media_item['color_correction'].update(color_data)
                                    media_item['has_color_correction'] = True

                        # 更新轨道显示
                        for i in range(len(self.parent_window.timeline.tracks)):
                            self.parent_window.update_track_display(i)

                    except Exception as e:
                        print(f"应用色彩校正到选中项失败: {e}")

                def reset_color_correction(self):
                    """重置色彩校正"""
                    # 重置基础调色
                    self.exposure_slider.setValue(0)
                    self.highlights_slider.setValue(0)
                    self.shadows_slider.setValue(0)
                    self.temperature_slider.setValue(0)
                    self.tint_slider.setValue(0)
                    self.vibrance_slider.setValue(0)

                    # 重置高级调色
                    self.red_slider.setValue(0)
                    self.green_slider.setValue(0)
                    self.blue_slider.setValue(0)
                    self.hue_slider.setValue(0)
                    self.saturation_slider.setValue(0)
                    self.lightness_slider.setValue(0)

                    # 重置匹配设置
                    self.reference_combo.setCurrentIndex(0)
                    self.match_strength.setValue(70)
                    self.lut_combo.setCurrentIndex(0)
                    self.lut_intensity.setValue(100)

            # 显示色彩校正面板
            panel = ColorCorrectionPanel(self)
            panel.exec()

        except Exception as e:
            logger.error(f"显示色彩校正面板失败: {e}")
            QMessageBox.critical(self, "错误", f"显示色彩校正面板失败:\n{str(e)}")

    def show_audio_editor(self):
        """显示音频编辑器"""
        try:
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QSlider, QTabWidget, QWidget, QComboBox, QCheckBox, QGroupBox, QListWidget

            class AudioEditor(QDialog):
                def __init__(self, parent=None):
                    super().__init__(parent)
                    self.setWindowTitle("音频编辑器")
                    self.setFixedSize(700, 600)
                    self.parent_window = parent

                    layout = QVBoxLayout(self)

                    # 创建标签页
                    tab_widget = QTabWidget()

                    # 音量控制标签页
                    volume_tab = self.create_volume_tab()
                    tab_widget.addTab(volume_tab, "音量控制")

                    # 音频效果标签页
                    effects_tab = self.create_audio_effects_tab()
                    tab_widget.addTab(effects_tab, "音频效果")

                    # 均衡器标签页
                    equalizer_tab = self.create_equalizer_tab()
                    tab_widget.addTab(equalizer_tab, "均衡器")

                    # 降噪标签页
                    noise_tab = self.create_noise_reduction_tab()
                    tab_widget.addTab(noise_tab, "降噪处理")

                    layout.addWidget(tab_widget)

                    # 底部按钮
                    button_layout = QHBoxLayout()

                    preview_btn = QPushButton("预览音频")
                    preview_btn.clicked.connect(self.preview_audio)
                    button_layout.addWidget(preview_btn)

                    apply_btn = QPushButton("应用设置")
                    apply_btn.clicked.connect(self.apply_audio_settings)
                    button_layout.addWidget(apply_btn)

                    export_btn = QPushButton("导出音频")
                    export_btn.clicked.connect(self.export_audio)
                    button_layout.addWidget(export_btn)

                    close_btn = QPushButton("关闭")
                    close_btn.clicked.connect(self.close)
                    button_layout.addWidget(close_btn)

                    layout.addLayout(button_layout)

                def create_volume_tab(self):
                    """创建音量控制标签页"""
                    widget = QWidget()
                    layout = QVBoxLayout(widget)

                    # 主音量
                    volume_group = QGroupBox("音量控制")
                    volume_layout = QVBoxLayout(volume_group)

                    # 主音量
                    volume_layout.addWidget(QLabel("主音量:"))
                    master_volume_layout = QHBoxLayout()
                    self.master_volume = QSlider(Qt.Orientation.Horizontal)
                    self.master_volume.setRange(0, 200)
                    self.master_volume.setValue(100)
                    master_volume_layout.addWidget(self.master_volume)
                    self.master_volume_label = QLabel("100%")
                    master_volume_layout.addWidget(self.master_volume_label)
                    volume_layout.addLayout(master_volume_layout)

                    # 左右声道平衡
                    volume_layout.addWidget(QLabel("左右平衡:"))
                    balance_layout = QHBoxLayout()
                    balance_layout.addWidget(QLabel("L"))
                    self.balance_slider = QSlider(Qt.Orientation.Horizontal)
                    self.balance_slider.setRange(-100, 100)
                    self.balance_slider.setValue(0)
                    balance_layout.addWidget(self.balance_slider)
                    balance_layout.addWidget(QLabel("R"))
                    self.balance_label = QLabel("0")
                    balance_layout.addWidget(self.balance_label)
                    volume_layout.addLayout(balance_layout)

                    layout.addWidget(volume_group)

                    # 淡入淡出
                    fade_group = QGroupBox("淡入淡出")
                    fade_layout = QVBoxLayout(fade_group)

                    # 淡入时间
                    fade_layout.addWidget(QLabel("淡入时间(秒):"))
                    fade_in_layout = QHBoxLayout()
                    self.fade_in_slider = QSlider(Qt.Orientation.Horizontal)
                    self.fade_in_slider.setRange(0, 100)  # 0-10秒
                    self.fade_in_slider.setValue(0)
                    fade_in_layout.addWidget(self.fade_in_slider)
                    self.fade_in_label = QLabel("0.0")
                    fade_in_layout.addWidget(self.fade_in_label)
                    fade_layout.addLayout(fade_in_layout)

                    # 淡出时间
                    fade_layout.addWidget(QLabel("淡出时间(秒):"))
                    fade_out_layout = QHBoxLayout()
                    self.fade_out_slider = QSlider(Qt.Orientation.Horizontal)
                    self.fade_out_slider.setRange(0, 100)  # 0-10秒
                    self.fade_out_slider.setValue(0)
                    fade_out_layout.addWidget(self.fade_out_slider)
                    self.fade_out_label = QLabel("0.0")
                    fade_out_layout.addWidget(self.fade_out_label)
                    fade_layout.addLayout(fade_out_layout)

                    layout.addWidget(fade_group)

                    # 连接滑块信号
                    self.master_volume.valueChanged.connect(lambda v: self.master_volume_label.setText(f"{v}%"))
                    self.balance_slider.valueChanged.connect(lambda v: self.balance_label.setText(str(v)))
                    self.fade_in_slider.valueChanged.connect(lambda v: self.fade_in_label.setText(f"{v/10:.1f}"))
                    self.fade_out_slider.valueChanged.connect(lambda v: self.fade_out_label.setText(f"{v/10:.1f}"))

                    # 音量标准化
                    normalize_group = QGroupBox("音量标准化")
                    normalize_layout = QVBoxLayout(normalize_group)

                    self.normalize_audio = QCheckBox("启用音量标准化")
                    normalize_layout.addWidget(self.normalize_audio)

                    # 目标音量
                    target_layout = QHBoxLayout()
                    target_layout.addWidget(QLabel("目标音量(dB):"))
                    self.target_volume = QSlider(Qt.Orientation.Horizontal)
                    self.target_volume.setRange(-30, 0)
                    self.target_volume.setValue(-12)
                    target_layout.addWidget(self.target_volume)
                    self.target_volume_label = QLabel("-12")
                    target_layout.addWidget(self.target_volume_label)
                    normalize_layout.addLayout(target_layout)

                    self.target_volume.valueChanged.connect(lambda v: self.target_volume_label.setText(str(v)))

                    layout.addWidget(normalize_group)

                    layout.addStretch()
                    return widget

                def create_audio_effects_tab(self):
                    """创建音频效果标签页"""
                    widget = QWidget()
                    layout = QVBoxLayout(widget)

                    # 音频效果列表
                    layout.addWidget(QLabel("音频效果:"))

                    effects_list = QListWidget()
                    effects = [
                        "混响", "回声", "合唱", "失真", "压缩器",
                        "限制器", "门限", "相位器", "颤音", "延迟"
                    ]
                    for effect in effects:
                        effects_list.addItem(effect)
                    layout.addWidget(effects_list)

                    # 效果参数
                    params_group = QGroupBox("效果参数")
                    params_layout = QVBoxLayout(params_group)

                    # 效果强度
                    params_layout.addWidget(QLabel("效果强度:"))
                    intensity_layout = QHBoxLayout()
                    self.effect_intensity = QSlider(Qt.Orientation.Horizontal)
                    self.effect_intensity.setRange(0, 100)
                    self.effect_intensity.setValue(50)
                    intensity_layout.addWidget(self.effect_intensity)
                    self.effect_intensity_label = QLabel("50%")
                    intensity_layout.addWidget(self.effect_intensity_label)
                    params_layout.addLayout(intensity_layout)

                    # 混合比例
                    params_layout.addWidget(QLabel("干湿比例:"))
                    mix_layout = QHBoxLayout()
                    mix_layout.addWidget(QLabel("干"))
                    self.dry_wet_mix = QSlider(Qt.Orientation.Horizontal)
                    self.dry_wet_mix.setRange(0, 100)
                    self.dry_wet_mix.setValue(50)
                    mix_layout.addWidget(self.dry_wet_mix)
                    mix_layout.addWidget(QLabel("湿"))
                    self.mix_label = QLabel("50%")
                    mix_layout.addWidget(self.mix_label)
                    params_layout.addLayout(mix_layout)

                    layout.addWidget(params_group)

                    # 连接信号
                    self.effect_intensity.valueChanged.connect(lambda v: self.effect_intensity_label.setText(f"{v}%"))
                    self.dry_wet_mix.valueChanged.connect(lambda v: self.mix_label.setText(f"{v}%"))

                    layout.addStretch()
                    return widget

                def create_equalizer_tab(self):
                    """创建均衡器标签页"""
                    widget = QWidget()
                    layout = QVBoxLayout(widget)

                    # 均衡器预设
                    layout.addWidget(QLabel("均衡器预设:"))
                    preset_layout = QHBoxLayout()

                    presets = ["平坦", "流行", "摇滚", "爵士", "古典", "电子", "人声增强"]
                    for preset in presets:
                        btn = QPushButton(preset)
                        btn.clicked.connect(lambda checked, p=preset: self.apply_eq_preset(p))
                        preset_layout.addWidget(btn)

                    layout.addLayout(preset_layout)

                    # 频段控制
                    eq_group = QGroupBox("10段均衡器")
                    eq_layout = QHBoxLayout(eq_group)

                    # 频段标签和滑块
                    self.eq_sliders = []
                    self.eq_labels = []
                    frequencies = ["32Hz", "64Hz", "125Hz", "250Hz", "500Hz", "1kHz", "2kHz", "4kHz", "8kHz", "16kHz"]

                    for freq in frequencies:
                        freq_layout = QVBoxLayout()

                        # 频段标签
                        freq_label = QLabel(freq)
                        freq_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                        freq_layout.addWidget(freq_label)

                        # 增益滑块
                        slider = QSlider(Qt.Orientation.Vertical)
                        slider.setRange(-120, 120)  # -12dB to +12dB
                        slider.setValue(0)
                        slider.setFixedHeight(200)
                        freq_layout.addWidget(slider)

                        # 数值标签
                        value_label = QLabel("0dB")
                        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                        freq_layout.addWidget(value_label)

                        # 连接信号
                        slider.valueChanged.connect(lambda v, label=value_label: label.setText(f"{v/10:.1f}dB"))

                        self.eq_sliders.append(slider)
                        self.eq_labels.append(value_label)

                        eq_layout.addLayout(freq_layout)

                    layout.addWidget(eq_group)

                    layout.addStretch()
                    return widget

                def create_noise_reduction_tab(self):
                    """创建降噪处理标签页"""
                    widget = QWidget()
                    layout = QVBoxLayout(widget)

                    # 降噪设置
                    noise_group = QGroupBox("降噪设置")
                    noise_layout = QVBoxLayout(noise_group)

                    # 降噪强度
                    noise_layout.addWidget(QLabel("降噪强度:"))
                    noise_strength_layout = QHBoxLayout()
                    self.noise_reduction = QSlider(Qt.Orientation.Horizontal)
                    self.noise_reduction.setRange(0, 100)
                    self.noise_reduction.setValue(30)
                    noise_strength_layout.addWidget(self.noise_reduction)
                    self.noise_reduction_label = QLabel("30%")
                    noise_strength_layout.addWidget(self.noise_reduction_label)
                    noise_layout.addLayout(noise_strength_layout)

                    # 噪声门限
                    noise_layout.addWidget(QLabel("噪声门限(dB):"))
                    gate_layout = QHBoxLayout()
                    self.noise_gate = QSlider(Qt.Orientation.Horizontal)
                    self.noise_gate.setRange(-60, 0)
                    self.noise_gate.setValue(-40)
                    gate_layout.addWidget(self.noise_gate)
                    self.noise_gate_label = QLabel("-40")
                    gate_layout.addWidget(self.noise_gate_label)
                    noise_layout.addLayout(gate_layout)

                    layout.addWidget(noise_group)

                    # 高级降噪
                    advanced_group = QGroupBox("高级降噪")
                    advanced_layout = QVBoxLayout(advanced_group)

                    self.spectral_subtraction = QCheckBox("频谱减法降噪")
                    advanced_layout.addWidget(self.spectral_subtraction)

                    self.adaptive_filter = QCheckBox("自适应滤波")
                    advanced_layout.addWidget(self.adaptive_filter)

                    self.wind_noise_reduction = QCheckBox("风噪抑制")
                    advanced_layout.addWidget(self.wind_noise_reduction)

                    layout.addWidget(advanced_group)

                    # 连接信号
                    self.noise_reduction.valueChanged.connect(lambda v: self.noise_reduction_label.setText(f"{v}%"))
                    self.noise_gate.valueChanged.connect(lambda v: self.noise_gate_label.setText(str(v)))

                    layout.addStretch()
                    return widget

                def apply_eq_preset(self, preset):
                    """应用均衡器预设"""
                    presets = {
                        "平坦": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                        "流行": [0, 20, 40, 30, 0, -10, -20, -20, 0, 20],
                        "摇滚": [40, 30, 20, 10, -10, -20, 0, 20, 40, 50],
                        "爵士": [20, 0, 20, 40, 40, 40, 20, 10, 20, 30],
                        "古典": [0, 0, 0, 0, 0, 0, -20, -20, -20, -30],
                        "电子": [30, 40, 20, 0, -20, 20, 0, 20, 40, 50],
                        "人声增强": [-20, -10, 0, 20, 40, 40, 30, 20, 0, -10]
                    }

                    if preset in presets:
                        values = presets[preset]
                        for i, value in enumerate(values):
                            if i < len(self.eq_sliders):
                                self.eq_sliders[i].setValue(value)

                def preview_audio(self):
                    """预览音频"""
                    QMessageBox.information(self, "预览", "音频预览功能开发中...")

                def apply_audio_settings(self):
                    """应用音频设置"""
                    try:
                        # 收集所有音频设置
                        audio_data = {
                            'volume': {
                                'master_volume': self.master_volume.value(),
                                'balance': self.balance_slider.value(),
                                'fade_in': self.fade_in_slider.value() / 10.0,
                                'fade_out': self.fade_out_slider.value() / 10.0,
                                'normalize': self.normalize_audio.isChecked(),
                                'target_volume': self.target_volume.value()
                            },
                            'effects': {
                                'intensity': self.effect_intensity.value(),
                                'dry_wet_mix': self.dry_wet_mix.value()
                            },
                            'equalizer': {
                                'bands': [slider.value() for slider in self.eq_sliders]
                            },
                            'noise_reduction': {
                                'strength': self.noise_reduction.value(),
                                'gate': self.noise_gate.value(),
                                'spectral_subtraction': self.spectral_subtraction.isChecked(),
                                'adaptive_filter': self.adaptive_filter.isChecked(),
                                'wind_noise_reduction': self.wind_noise_reduction.isChecked()
                            }
                        }

                        # 应用到选中的音频项
                        self.apply_audio_to_selection(audio_data)

                        QMessageBox.information(self, "应用完成", "音频设置已应用到选中片段")

                    except Exception as e:
                        QMessageBox.critical(self, "错误", f"应用音频设置失败:\n{str(e)}")

                def apply_audio_to_selection(self, audio_data):
                    """将音频设置应用到选中的音频项"""
                    try:
                        # 遍历所有轨道，查找选中的音频项
                        for track in self.parent_window.timeline.tracks:
                            if track['type'] == 'audio':
                                for media_item in track['media_files']:
                                    if isinstance(media_item, dict):
                                        # 添加音频设置数据到媒体项
                                        if 'audio_settings' not in media_item:
                                            media_item['audio_settings'] = {}

                                        media_item['audio_settings'].update(audio_data)
                                        media_item['has_audio_settings'] = True

                        # 更新轨道显示
                        for i in range(len(self.parent_window.timeline.tracks)):
                            self.parent_window.update_track_display(i)

                    except Exception as e:
                        print(f"应用音频设置到选中项失败: {e}")

                def export_audio(self):
                    """导出音频"""
                    try:
                        output_path, _ = QFileDialog.getSaveFileName(
                            self, "导出音频",
                            "processed_audio.wav",
                            "音频文件 (*.wav *.mp3 *.aac);;所有文件 (*)"
                        )

                        if output_path:
                            QMessageBox.information(self, "导出", f"音频将导出到:\n{output_path}")

                    except Exception as e:
                        QMessageBox.critical(self, "错误", f"导出音频失败:\n{str(e)}")

            # 显示音频编辑器
            editor = AudioEditor(self)
            editor.exec()

        except Exception as e:
            logger.error(f"显示音频编辑器失败: {e}")
            QMessageBox.critical(self, "错误", f"显示音频编辑器失败:\n{str(e)}")

    # 🔧 新增：模板编辑相关方法
    def show_template_editor(self, template_data):
        """显示模板编辑器"""
        try:
            print(f"主窗口收到模板编辑请求: {template_data['name']}")

            # 切换到模板编辑标签页
            for i in range(self.right_panel_tabs.count()):
                if self.right_panel_tabs.tabText(i) == "模板编辑":
                    self.right_panel_tabs.setCurrentIndex(i)
                    break

            # 显示模板编辑界面
            self.show_template_editor_content(template_data)

        except Exception as e:
            print(f"显示模板编辑器失败: {e}")

    def show_template_editor_placeholder(self):
        """显示模板编辑器占位符"""
        # 清空现有内容
        self.clear_template_editor_content()

        # 添加占位符
        placeholder_label = QLabel("请点击素材库中的模板进行编辑")
        placeholder_label.setStyleSheet("""
            QLabel {
                color: #CCCCCC;
                font-size: 14px;
                padding: 40px;
                text-align: center;
            }
        """)
        placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.template_content_layout.addWidget(placeholder_label)
        self.template_content_layout.addStretch()

    def show_template_editor_content(self, template_data):
        """显示模板编辑内容"""
        # 清空现有内容
        self.clear_template_editor_content()

        # 创建模板编辑表单
        self.create_template_editor_form(template_data)

    def clear_template_editor_content(self):
        """清空模板编辑内容"""
        while self.template_content_layout.count():
            child = self.template_content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def create_template_editor_form(self, template_data):
        """创建模板编辑表单"""
        # 🔧 修改：模板信息（去掉卡片样式）
        info_title = QLabel("模板信息")
        info_title.setStyleSheet("""
            QLabel {
                color: #F2ECFF;
                font-size: 14px;
                font-weight: bold;
                margin: 8px 0px 4px 0px;
            }
        """)
        self.template_content_layout.addWidget(info_title)

        # 🔧 修改：模板名称（占满宽度）
        name_layout = QVBoxLayout()
        name_layout.setSpacing(4)
        name_label = QLabel("名称:")
        name_label.setStyleSheet("color: #F2ECFF; font-size: 12px;")
        name_layout.addWidget(name_label)

        self.template_name_edit = QLineEdit(template_data.get("name", ""))
        self.template_name_edit.setPlaceholderText("模板名称...")
        self.template_name_edit.setStyleSheet("""
            QLineEdit {
                background-color: #333333;
                color: #F2ECFF;
                border: 1px solid #666666;
                border-radius: 4px;
                padding: 6px;
            }
            QLineEdit:focus {
                border-color: #00C896;
            }
        """)
        name_layout.addWidget(self.template_name_edit)
        self.template_content_layout.addLayout(name_layout)

        # 🔧 修改：模板图片选择
        image_layout = QVBoxLayout()
        image_layout.setSpacing(4)
        image_label = QLabel("图片:")
        image_label.setStyleSheet("color: #F2ECFF; font-size: 12px;")
        image_layout.addWidget(image_label)

        # 图片预览和选择容器
        image_container = QHBoxLayout()
        image_container.setSpacing(8)

        # 图片预览标签
        self.template_image_preview = QLabel()
        self.template_image_preview.setFixedSize(60, 40)
        self.template_image_preview.setStyleSheet("""
            QLabel {
                background-color: #1A1A1A;
                border: 1px solid #666666;
                border-radius: 4px;
            }
        """)
        self.template_image_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.template_image_preview.setScaledContents(False)  # 不拉伸

        # 设置默认图片或现有图片
        current_image = template_data.get("image_path", "")
        if current_image and Path(current_image).exists():
            self.load_template_image_preview(current_image)
        else:
            self.template_image_preview.setText("无图片")
            self.template_image_preview.setStyleSheet(self.template_image_preview.styleSheet() + "color: #CCCCCC;")

        image_container.addWidget(self.template_image_preview)

        # 选择图片按钮
        select_image_btn = QPushButton("选择图片")
        select_image_btn.setStyleSheet("""
            QPushButton {
                background-color: #555555;
                color: #F2ECFF;
                border: 1px solid #666666;
                border-radius: 4px;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background-color: #666666;
            }
        """)
        select_image_btn.clicked.connect(self.select_template_image)
        image_container.addWidget(select_image_btn)

        # 清除图片按钮
        clear_image_btn = QPushButton("清除")
        clear_image_btn.setStyleSheet(select_image_btn.styleSheet())
        clear_image_btn.clicked.connect(self.clear_template_image)
        image_container.addWidget(clear_image_btn)

        image_container.addStretch()
        image_layout.addLayout(image_container)
        self.template_content_layout.addLayout(image_layout)

        # 🔧 修改：模板描述（占满宽度）
        desc_layout = QVBoxLayout()
        desc_layout.setSpacing(4)
        desc_label = QLabel("描述:")
        desc_label.setStyleSheet("color: #F2ECFF; font-size: 12px;")
        desc_layout.addWidget(desc_label)

        self.template_desc_edit = QTextEdit(template_data.get("description", ""))
        self.template_desc_edit.setPlaceholderText("简短描述模板用途...")
        self.template_desc_edit.setMaximumHeight(50)  # 限制为两行高度
        self.template_desc_edit.setStyleSheet("""
            QTextEdit {
                background-color: #333333;
                color: #F2ECFF;
                border: 1px solid #666666;
                border-radius: 4px;
                padding: 6px;
            }
            QTextEdit:focus {
                border-color: #00C896;
            }
        """)
        desc_layout.addWidget(self.template_desc_edit)
        self.template_content_layout.addLayout(desc_layout)

        # 🔧 新增：分割线
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.Shape.HLine)
        separator1.setStyleSheet("QFrame { color: #666666; }")
        self.template_content_layout.addWidget(separator1)

        # 保存当前图片路径
        self.current_template_image_path = current_image

        # 🔧 修改：模板设置（去掉卡片样式）
        settings_title = QLabel("模板设置")
        settings_title.setStyleSheet("""
            QLabel {
                color: #F2ECFF;
                font-size: 14px;
                font-weight: bold;
                margin: 8px 0px 4px 0px;
            }
        """)
        self.template_content_layout.addWidget(settings_title)

        # 基础设置
        basic_settings_layout = QHBoxLayout()
        basic_settings_layout.setSpacing(8)  # 🔧 修改：设置间隔为8

        # 🔧 修改：总时长（使用+-按钮）
        duration_container = QVBoxLayout()
        duration_container.setSpacing(4)
        duration_label = QLabel("时长:")
        duration_label.setStyleSheet("color: #F2ECFF; font-size: 12px;")
        duration_container.addWidget(duration_label)

        self.template_duration_spin = QDoubleSpinBox()
        self.template_duration_spin.setRange(5.0, 300.0)
        self.template_duration_spin.setValue(template_data.get("settings", {}).get("total_duration", 60.0))
        self.template_duration_spin.setSuffix("s")
        self.template_duration_spin.setButtonSymbols(QDoubleSpinBox.ButtonSymbols.PlusMinus)
        self.template_duration_spin.setStyleSheet("""
            QDoubleSpinBox {
                background-color: #333333;
                color: #F2ECFF;
                border: 1px solid #666666;
                border-radius: 4px;
                padding: 4px;
                max-width: 80px;
            }
            QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {
                width: 12px;
                background-color: #555555;
                border: 1px solid #666666;
            }
            QDoubleSpinBox::up-button:hover, QDoubleSpinBox::down-button:hover {
                background-color: #666666;
            }
        """)
        duration_container.addWidget(self.template_duration_spin)
        basic_settings_layout.addLayout(duration_container)

        # 🔧 修改：分辨率（减小间隔）
        resolution_container = QVBoxLayout()
        resolution_container.setSpacing(4)
        resolution_label = QLabel("分辨率:")
        resolution_label.setStyleSheet("color: #F2ECFF; font-size: 12px;")
        resolution_container.addWidget(resolution_label)

        self.template_resolution_combo = QComboBox()
        self.template_resolution_combo.addItems(["1920x1080", "1280x720", "3840x2160"])
        current_resolution = template_data.get("settings", {}).get("resolution", "1920x1080")
        self.template_resolution_combo.setCurrentText(current_resolution)
        self.template_resolution_combo.setStyleSheet("""
            QComboBox {
                background-color: #333333;
                color: #F2ECFF;
                border: 1px solid #666666;
                border-radius: 4px;
                padding: 4px;
                max-width: 120px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox QAbstractItemView {
                background-color: #333333;
                color: #F2ECFF;
                selection-background-color: #00C896;
            }
        """)
        resolution_container.addWidget(self.template_resolution_combo)
        basic_settings_layout.addLayout(resolution_container)

        # 🔧 修改：帧率（使用+-按钮）
        fps_container = QVBoxLayout()
        fps_container.setSpacing(4)
        fps_label = QLabel("帧率:")
        fps_label.setStyleSheet("color: #F2ECFF; font-size: 12px;")
        fps_container.addWidget(fps_label)

        self.template_fps_spin = QSpinBox()
        self.template_fps_spin.setRange(24, 60)
        self.template_fps_spin.setValue(template_data.get("settings", {}).get("fps", 30))
        self.template_fps_spin.setSuffix("fps")
        self.template_fps_spin.setButtonSymbols(QSpinBox.ButtonSymbols.PlusMinus)
        self.template_fps_spin.setStyleSheet("""
            QSpinBox {
                background-color: #333333;
                color: #F2ECFF;
                border: 1px solid #666666;
                border-radius: 4px;
                padding: 4px;
                max-width: 80px;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                width: 12px;
                background-color: #555555;
                border: 1px solid #666666;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #666666;
            }
        """)
        fps_container.addWidget(self.template_fps_spin)
        basic_settings_layout.addLayout(fps_container)

        basic_settings_layout.addStretch()  # 添加弹性空间
        self.template_content_layout.addLayout(basic_settings_layout)

        # 🔧 新增：分割线
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.Shape.HLine)
        separator2.setStyleSheet("QFrame { color: #666666; }")
        self.template_content_layout.addWidget(separator2)

        # 🔧 修改：视频片段（去掉卡片样式）
        video_title = QLabel("视频片段")
        video_title.setStyleSheet("""
            QLabel {
                color: #F2ECFF;
                font-size: 14px;
                font-weight: bold;
                margin: 8px 0px 4px 0px;
            }
        """)
        self.template_content_layout.addWidget(video_title)

        # 视频片段列表
        self.video_segments_layout = QVBoxLayout()
        self.video_segments_layout.setSpacing(8)

        # 获取现有片段或创建默认片段
        segments = template_data.get("settings", {}).get("segments", [
            {"name": "开场片段", "duration": 5.0, "type": "intro"},
            {"name": "主要内容", "duration": 45.0, "type": "main"},
            {"name": "结尾片段", "duration": 10.0, "type": "outro"}
        ])

        self.video_segment_widgets = []
        for i, segment in enumerate(segments):
            segment_widget = self.create_video_segment_widget(segment, i)
            self.video_segment_widgets.append(segment_widget)
            self.video_segments_layout.addWidget(segment_widget)

        self.template_content_layout.addLayout(self.video_segments_layout)

        # 添加片段按钮
        add_segment_btn = QPushButton("+ 添加视频片段")
        add_segment_btn.setStyleSheet("""
            QPushButton {
                background-color: #00C896;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                margin: 4px 0px;
            }
            QPushButton:hover {
                background-color: #00E8A8;
            }
        """)
        add_segment_btn.clicked.connect(self.add_video_segment)
        self.template_content_layout.addWidget(add_segment_btn)

        # 🔧 新增：分割线
        separator3 = QFrame()
        separator3.setFrameShape(QFrame.Shape.HLine)
        separator3.setStyleSheet("QFrame { color: #666666; }")
        self.template_content_layout.addWidget(separator3)

        # 🔧 修改：音频片段（去掉卡片样式）
        audio_title = QLabel("音频片段")
        audio_title.setStyleSheet("""
            QLabel {
                color: #F2ECFF;
                font-size: 14px;
                font-weight: bold;
                margin: 8px 0px 4px 0px;
            }
        """)
        self.template_content_layout.addWidget(audio_title)

        # 音频片段列表
        self.audio_segments_layout = QVBoxLayout()
        self.audio_segments_layout.setSpacing(8)

        # 获取现有音频片段或创建默认片段
        audio_segments = template_data.get("settings", {}).get("audio_segments", [
            {"name": "背景音乐", "volume": 0.6, "type": "background"}
        ])

        self.audio_segment_widgets = []
        for i, segment in enumerate(audio_segments):
            segment_widget = self.create_audio_segment_widget(segment, i)
            self.audio_segment_widgets.append(segment_widget)
            self.audio_segments_layout.addWidget(segment_widget)

        self.template_content_layout.addLayout(self.audio_segments_layout)

        # 添加音频片段按钮
        add_audio_segment_btn = QPushButton("+ 添加音频片段")
        add_audio_segment_btn.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                margin: 4px 0px;
            }
            QPushButton:hover {
                background-color: #BA68C8;
            }
        """)
        add_audio_segment_btn.clicked.connect(self.add_audio_segment)
        self.template_content_layout.addWidget(add_audio_segment_btn)

        # 🔧 新增：分割线
        separator4 = QFrame()
        separator4.setFrameShape(QFrame.Shape.HLine)
        separator4.setStyleSheet("QFrame { color: #666666; }")
        self.template_content_layout.addWidget(separator4)

        # 🔧 修改：操作按钮组（只保留保存按钮，放在最右边）
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)

        # 添加弹性空间，将保存按钮推到最右边
        button_layout.addStretch()

        # 保存按钮
        save_btn = QPushButton("保存模板")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #00C896;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #00E8A8;
            }
        """)
        save_btn.clicked.connect(lambda: self.save_template(template_data))
        button_layout.addWidget(save_btn)

        self.template_content_layout.addLayout(button_layout)

        # 添加弹性空间
        self.template_content_layout.addStretch()

        # 保存当前编辑的模板数据
        self.current_template_data = template_data

    def select_template_image(self):
        """选择模板图片"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择模板图片",
                "", "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif);;所有文件 (*)"
            )
            if file_path:
                self.current_template_image_path = file_path
                self.load_template_image_preview(file_path)
        except Exception as e:
            print(f"选择模板图片失败: {e}")

    def clear_template_image(self):
        """清除模板图片"""
        self.current_template_image_path = ""
        self.template_image_preview.clear()
        self.template_image_preview.setText("无图片")
        self.template_image_preview.setStyleSheet(
            self.template_image_preview.styleSheet() + "color: #CCCCCC;"
        )

    def load_template_image_preview(self, image_path):
        """加载模板图片预览"""
        try:
            if Path(image_path).exists():
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    # 按比例缩放，不拉伸
                    scaled_pixmap = pixmap.scaled(
                        60, 40,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    self.template_image_preview.setPixmap(scaled_pixmap)
                    self.template_image_preview.setStyleSheet("""
                        QLabel {
                            background-color: #1A1A1A;
                            border: 1px solid #666666;
                            border-radius: 4px;
                        }
                    """)
        except Exception as e:
            print(f"加载图片预览失败: {e}")

    def create_video_segment_widget(self, segment_data, index):
        """创建视频片段设置组件"""
        widget = QWidget()
        # 🔧 修改：去掉边框
        widget.setStyleSheet("""
            QWidget {
                background-color: transparent;
            }
        """)

        layout = QHBoxLayout(widget)
        layout.setContentsMargins(8, 4, 8, 4)  # 🔧 修改：调整边距
        layout.setSpacing(8)

        # 🔧 修改：片段名称（增加长度）
        name_edit = QLineEdit(segment_data.get("name", f"片段{index + 1}"))
        name_edit.setPlaceholderText("名称")
        name_edit.setMaximumWidth(120)  # 🔧 修改：从80改为120，显示更多字
        name_edit.setStyleSheet("""
            QLineEdit {
                background-color: #333333;
                color: #F2ECFF;
                border: 1px solid #666666;
                border-radius: 4px;
                padding: 4px;
            }
        """)
        layout.addWidget(name_edit)

        # 🔧 修改：片段时长（使用模板设置的+-样式）
        duration_spin = QDoubleSpinBox()
        duration_spin.setRange(0.1, 120.0)
        duration_spin.setValue(segment_data.get("duration", 5.0))
        duration_spin.setSuffix("s")
        duration_spin.setMaximumWidth(80)
        duration_spin.setButtonSymbols(QDoubleSpinBox.ButtonSymbols.PlusMinus)
        duration_spin.setStyleSheet("""
            QDoubleSpinBox {
                background-color: #333333;
                color: #F2ECFF;
                border: 1px solid #666666;
                border-radius: 4px;
                padding: 4px;
            }
            QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {
                width: 12px;
                background-color: #555555;
                border: 1px solid #666666;
            }
            QDoubleSpinBox::up-button:hover, QDoubleSpinBox::down-button:hover {
                background-color: #666666;
            }
        """)
        layout.addWidget(duration_spin)

        # 🔧 修改：视频文件显示和操作
        video_container = QVBoxLayout()

        # 视频文件显示标签
        video_label = QLabel("未选择视频")
        video_label.setMaximumWidth(100)
        video_label.setStyleSheet("""
            QLabel {
                background-color: #2A2A2A;
                color: #CCCCCC;
                border: 1px solid #666666;
                border-radius: 4px;
                padding: 2px 4px;
                font-size: 9px;
            }
        """)
        video_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 如果有已选择的视频，显示文件名
        current_video = segment_data.get("video_path", "")
        if current_video:
            video_name = Path(current_video).name
            if len(video_name) > 12:
                video_name = video_name[:9] + "..."
            video_label.setText(video_name)
            video_label.setStyleSheet("""
                QLabel {
                    background-color: #00C896;
                    color: white;
                    border: 1px solid #00C896;
                    border-radius: 4px;
                    padding: 2px 4px;
                    font-size: 9px;
                }
            """)

        video_container.addWidget(video_label)

        # 按钮容器
        button_container = QHBoxLayout()
        button_container.setSpacing(2)

        # 选择视频按钮
        select_video_btn = QPushButton("选择")
        select_video_btn.setMaximumWidth(35)
        select_video_btn.setMaximumHeight(20)
        select_video_btn.setStyleSheet("""
            QPushButton {
                background-color: #3E8BF0;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 2px 4px;
                font-size: 9px;
            }
            QPushButton:hover {
                background-color: #5A9BFF;
            }
        """)
        select_video_btn.clicked.connect(lambda: self.select_segment_video(widget))
        button_container.addWidget(select_video_btn)

        # 移除视频按钮
        remove_video_btn = QPushButton("移除")
        remove_video_btn.setMaximumWidth(35)
        remove_video_btn.setMaximumHeight(20)
        remove_video_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 2px 4px;
                font-size: 9px;
            }
            QPushButton:hover {
                background-color: #FFB74D;
            }
        """)
        remove_video_btn.clicked.connect(lambda: self.remove_segment_video(widget))
        button_container.addWidget(remove_video_btn)

        video_container.addLayout(button_container)
        layout.addLayout(video_container)

        # 🔧 修改：删除按钮（增大尺寸）
        delete_container = QVBoxLayout()
        delete_container.setAlignment(Qt.AlignmentFlag.AlignCenter)

        delete_btn = QPushButton("删\n除")  # 竖直显示
        delete_btn.setMaximumWidth(40)  # 🔧 修改：从30改为40
        delete_btn.setMaximumHeight(50)  # 🔧 修改：从40改为50
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px;
                font-size: 10px;
                line-height: 1.0;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        delete_btn.clicked.connect(lambda: self.remove_video_segment(widget))
        delete_container.addWidget(delete_btn)

        layout.addLayout(delete_container)

        # 保存控件引用和数据
        widget.name_edit = name_edit
        widget.duration_spin = duration_spin
        widget.video_label = video_label
        widget.video_path = current_video

        return widget

    def add_video_segment(self):
        """添加视频片段"""
        segment_data = {
            "name": f"片段 {len(self.video_segment_widgets) + 1}",
            "duration": 5.0,
            "type": "main"
        }

        widget = self.create_video_segment_widget(segment_data, len(self.video_segment_widgets))
        self.video_segment_widgets.append(widget)
        self.video_segments_layout.addWidget(widget)

    def remove_video_segment(self, widget):
        """删除视频片段"""
        if widget in self.video_segment_widgets:
            self.video_segment_widgets.remove(widget)
            self.video_segments_layout.removeWidget(widget)
            widget.deleteLater()

    def select_segment_video(self, widget):
        """选择片段视频文件"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择视频文件",
                "", "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv);;所有文件 (*)"
            )
            if file_path:
                widget.video_path = file_path
                # 更新显示
                video_name = Path(file_path).name
                if len(video_name) > 12:
                    video_name = video_name[:9] + "..."
                widget.video_label.setText(video_name)
                widget.video_label.setStyleSheet("""
                    QLabel {
                        background-color: #00C896;
                        color: white;
                        border: 1px solid #00C896;
                        border-radius: 4px;
                        padding: 2px 4px;
                        font-size: 9px;
                    }
                """)
                print(f"为片段选择视频: {Path(file_path).name}")
        except Exception as e:
            print(f"选择片段视频失败: {e}")

    def remove_segment_video(self, widget):
        """移除片段视频文件"""
        try:
            widget.video_path = ""
            widget.video_label.setText("未选择视频")
            widget.video_label.setStyleSheet("""
                QLabel {
                    background-color: #2A2A2A;
                    color: #CCCCCC;
                    border: 1px solid #666666;
                    border-radius: 4px;
                    padding: 2px 4px;
                    font-size: 9px;
                }
            """)
            print("移除片段视频文件")
        except Exception as e:
            print(f"移除片段视频失败: {e}")

    def create_audio_segment_widget(self, segment_data, index):
        """创建音频片段设置组件"""
        widget = QWidget()
        # 🔧 修改：去掉边框
        widget.setStyleSheet("""
            QWidget {
                background-color: transparent;
            }
        """)

        layout = QHBoxLayout(widget)
        layout.setContentsMargins(8, 4, 8, 4)  # 🔧 修改：调整边距
        layout.setSpacing(8)

        # 🔧 修改：音频名称（增加长度）
        name_edit = QLineEdit(segment_data.get("name", f"音频{index + 1}"))
        name_edit.setPlaceholderText("名称")
        name_edit.setMaximumWidth(120)  # 🔧 修改：从80改为120，显示更多字
        name_edit.setStyleSheet("""
            QLineEdit {
                background-color: #333333;
                color: #F2ECFF;
                border: 1px solid #666666;
                border-radius: 4px;
                padding: 4px;
            }
        """)
        layout.addWidget(name_edit)

        # 🔧 修改：音量滑块（简化）
        volume_slider = QSlider(Qt.Orientation.Horizontal)
        volume_slider.setRange(0, 100)
        volume_slider.setValue(int(segment_data.get("volume", 0.6) * 100))
        volume_slider.setMaximumWidth(100)
        volume_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #666666;
                height: 4px;
                background: #333333;
                border-radius: 2px;
            }
            QSlider::handle:horizontal {
                background: #9C27B0;
                border: 1px solid #9C27B0;
                width: 12px;
                height: 12px;
                margin: -4px 0;
                border-radius: 6px;
            }
        """)
        layout.addWidget(volume_slider)

        # 音量标签
        volume_label = QLabel(f"{volume_slider.value()}%")
        volume_label.setMinimumWidth(35)
        volume_label.setStyleSheet("color: #F2ECFF; font-size: 12px;")
        volume_slider.valueChanged.connect(lambda v: volume_label.setText(f"{v}%"))
        layout.addWidget(volume_label)

        # 🔧 修改：音频文件显示和操作
        audio_container = QVBoxLayout()

        # 音频文件显示标签
        audio_label = QLabel("未选择音频")
        audio_label.setMaximumWidth(100)
        audio_label.setStyleSheet("""
            QLabel {
                background-color: #2A2A2A;
                color: #CCCCCC;
                border: 1px solid #666666;
                border-radius: 4px;
                padding: 2px 4px;
                font-size: 9px;
            }
        """)
        audio_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 如果有已选择的音频，显示文件名
        current_audio = segment_data.get("audio_path", "")
        if current_audio:
            audio_name = Path(current_audio).name
            if len(audio_name) > 12:
                audio_name = audio_name[:9] + "..."
            audio_label.setText(audio_name)
            audio_label.setStyleSheet("""
                QLabel {
                    background-color: #9C27B0;
                    color: white;
                    border: 1px solid #9C27B0;
                    border-radius: 4px;
                    padding: 2px 4px;
                    font-size: 9px;
                }
            """)

        audio_container.addWidget(audio_label)

        # 按钮容器
        button_container = QHBoxLayout()
        button_container.setSpacing(2)

        # 选择音频按钮
        select_audio_btn = QPushButton("选择")
        select_audio_btn.setMaximumWidth(35)
        select_audio_btn.setMaximumHeight(20)
        select_audio_btn.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 2px 4px;
                font-size: 9px;
            }
            QPushButton:hover {
                background-color: #BA68C8;
            }
        """)
        select_audio_btn.clicked.connect(lambda: self.select_segment_audio(widget))
        button_container.addWidget(select_audio_btn)

        # 移除音频按钮
        remove_audio_btn = QPushButton("移除")
        remove_audio_btn.setMaximumWidth(35)
        remove_audio_btn.setMaximumHeight(20)
        remove_audio_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 2px 4px;
                font-size: 9px;
            }
            QPushButton:hover {
                background-color: #FFB74D;
            }
        """)
        remove_audio_btn.clicked.connect(lambda: self.remove_segment_audio(widget))
        button_container.addWidget(remove_audio_btn)

        audio_container.addLayout(button_container)
        layout.addLayout(audio_container)

        # 🔧 修改：删除按钮（增大尺寸）
        delete_container = QVBoxLayout()
        delete_container.setAlignment(Qt.AlignmentFlag.AlignCenter)

        delete_btn = QPushButton("删\n除")  # 竖直显示
        delete_btn.setMaximumWidth(40)  # 🔧 修改：从30改为40
        delete_btn.setMaximumHeight(50)  # 🔧 修改：从40改为50
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px;
                font-size: 10px;
                line-height: 1.0;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        delete_btn.clicked.connect(lambda: self.remove_audio_segment(widget))
        delete_container.addWidget(delete_btn)

        layout.addLayout(delete_container)

        # 保存控件引用和数据
        widget.name_edit = name_edit
        widget.volume_slider = volume_slider
        widget.volume_label = volume_label
        widget.audio_label = audio_label
        widget.audio_path = current_audio

        return widget

    def add_audio_segment(self):
        """添加音频片段"""
        segment_data = {
            "name": f"音频{len(self.audio_segment_widgets) + 1}",
            "volume": 0.6,
            "type": "background"
        }

        widget = self.create_audio_segment_widget(segment_data, len(self.audio_segment_widgets))
        self.audio_segment_widgets.append(widget)
        self.audio_segments_layout.addWidget(widget)

    def remove_audio_segment(self, widget):
        """删除音频片段"""
        if widget in self.audio_segment_widgets:
            self.audio_segment_widgets.remove(widget)
            self.audio_segments_layout.removeWidget(widget)
            widget.deleteLater()

    def select_segment_audio(self, widget):
        """选择片段音频文件"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择音频文件",
                "", "音频文件 (*.mp3 *.wav *.aac *.flac *.ogg);;所有文件 (*)"
            )
            if file_path:
                widget.audio_path = file_path
                # 更新显示
                audio_name = Path(file_path).name
                if len(audio_name) > 12:
                    audio_name = audio_name[:9] + "..."
                widget.audio_label.setText(audio_name)
                widget.audio_label.setStyleSheet("""
                    QLabel {
                        background-color: #9C27B0;
                        color: white;
                        border: 1px solid #9C27B0;
                        border-radius: 4px;
                        padding: 2px 4px;
                        font-size: 9px;
                    }
                """)
                print(f"为片段选择音频: {Path(file_path).name}")
        except Exception as e:
            print(f"选择片段音频失败: {e}")

    def remove_segment_audio(self, widget):
        """移除片段音频文件"""
        try:
            widget.audio_path = ""
            widget.audio_label.setText("未选择音频")
            widget.audio_label.setStyleSheet("""
                QLabel {
                    background-color: #2A2A2A;
                    color: #CCCCCC;
                    border: 1px solid #666666;
                    border-radius: 4px;
                    padding: 2px 4px;
                    font-size: 9px;
                }
            """)
            print("移除片段音频文件")
        except Exception as e:
            print(f"移除片段音频失败: {e}")

    def save_template(self, template_data):
        """保存模板"""
        try:
            # 收集视频片段数据
            segments = []
            for widget in self.video_segment_widgets:
                segments.append({
                    "name": widget.name_edit.text().strip(),
                    "duration": widget.duration_spin.value(),
                    "video_path": getattr(widget, 'video_path', '')
                })

            # 🔧 新增：收集音频片段数据
            audio_segments = []
            for widget in self.audio_segment_widgets:
                audio_segments.append({
                    "name": widget.name_edit.text().strip(),
                    "volume": widget.volume_slider.value() / 100.0,
                    "audio_path": getattr(widget, 'audio_path', '')
                })

            # 获取编辑后的数据
            updated_data = {
                "name": self.template_name_edit.text().strip(),
                "description": self.template_desc_edit.toPlainText().strip(),  # 改为toPlainText
                "image_path": self.current_template_image_path,
                "settings": {
                    "total_duration": self.template_duration_spin.value(),
                    "resolution": self.template_resolution_combo.currentText(),
                    "fps": self.template_fps_spin.value(),
                    "segments": segments,
                    "audio_segments": audio_segments
                }
            }

            # 验证数据
            if not updated_data["name"]:
                QMessageBox.warning(self, "警告", "模板名称不能为空")
                return

            # 🔧 修改：添加默认图标（如果没有图片的话）
            if not updated_data.get("image_path"):
                updated_data["icon"] = "📝"
            else:
                updated_data["icon"] = ""  # 有图片时不需要图标

            # 根据是否为新模板决定操作
            if template_data.get("is_new", False):
                # 新模板 - 添加到素材库
                self.media_library.add_template(updated_data)
                QMessageBox.information(self, "成功", f"模板 '{updated_data['name']}' 创建成功")
            else:
                # 现有模板 - 更新
                template_id = template_data.get("id", "")
                if self.media_library.update_template(template_id, updated_data):
                    QMessageBox.information(self, "成功", f"模板 '{updated_data['name']}' 更新成功")
                else:
                    QMessageBox.warning(self, "失败", "模板更新失败")

            # 返回占位符状态
            self.show_template_editor_placeholder()

        except Exception as e:
            print(f"保存模板失败: {e}")
            QMessageBox.critical(self, "错误", f"保存模板失败: {e}")

    def cancel_template_edit(self):
        """取消模板编辑"""
        self.show_template_editor_placeholder()

    def delete_template(self, template_data):
        """删除模板"""
        try:
            template_name = template_data.get("name", "未知模板")
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除模板 '{template_name}' 吗？\n此操作不可撤销。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                template_id = template_data.get("id", "")
                if self.media_library.delete_template(template_id):
                    QMessageBox.information(self, "成功", f"模板 '{template_name}' 删除成功")
                    # 返回占位符状态
                    self.show_template_editor_placeholder()
                else:
                    QMessageBox.warning(self, "失败", "模板删除失败")

        except Exception as e:
            print(f"删除模板失败: {e}")
            QMessageBox.critical(self, "错误", f"删除模板失败: {e}")
