#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import cv2
from pathlib import Path
from PySide6.QtWidgets import QWidget
from PySide6.QtCore import Qt, Signal, QPoint, QRect, QTimer
from PySide6.QtGui import QPainter, QColor, QPen, QBrush, QPixmap, QFont, QPolygon

class VideoThumbnailBlock(QWidget):
    """显示视频缩略图的媒体块"""

    def __init__(self, media_item, track_index, media_index, timeline, video_processor=None):
        super().__init__()
        self.media_item = media_item
        self.track_index = track_index
        self.media_index = media_index
        self.timeline = timeline
        self.video_processor = video_processor
        self.dragging = False
        self.drag_start_pos = None
        self.original_pos = None

        # 检查是否是占位符
        self.is_placeholder = False
        if isinstance(media_item, dict):
            file_path = media_item.get('file_path', '')
            self.is_placeholder = (file_path.startswith('placeholder_') or
                                 media_item.get('is_placeholder', False))

        # 缩略图缓存管理
        self._thumbnail_cache = {}
        self._max_cache_size = 50  # 最大缓存数量
        self._cache_access_count = {}  # 访问计数，用于LRU清理

        # 设置基本属性 - 增加高度
        self.setMinimumHeight(84)  # 适应更大的轨道高度

        # 缩略图配置 - 按原比例显示
        self.thumbnail_width = 100  # 基础缩略图宽度
        self.thumbnail_height = 60  # 基础高度，实际会根据视频比例调整

        # 异步加载相关
        self._loading_thumbnails = {}  # 正在加载的缩略图 {index: timestamp}
        self._loaded_thumbnails = {}   # 已加载的缩略图 {index: QPixmap}
        self._load_queue = []          # 加载队列
        self._current_timestamps = []  # 当前应该显示的时间戳

        # 创建加载定时器
        self._load_timer = QTimer()
        self._load_timer.timeout.connect(self._load_next_thumbnail)
        self._load_timer.setSingleShot(True)

        # 裁剪游标相关 - 简化版本
        self.trim_handle_width = 24  # 游标宽度，增加宽度使其更容易点击和看到
        self.left_trim_dragging = False
        self.right_trim_dragging = False
        self.trim_drag_start_pos = None

        # 裁剪位置（相对于素材内部的像素位置）
        self.left_trim_pos = 0  # 左裁剪位置（像素）
        self.right_trim_pos = 0  # 右裁剪位置（像素，从右边缘开始）

        # 预览裁剪位置（拖拽时的临时状态）
        self.preview_left_trim_pos = 0
        self.preview_right_trim_pos = 0

        # 裁剪历史记录（支持往外拖恢复）
        self.trim_history = []
        self.current_trim_index = -1

    def get_trim_state(self):
        """暴露裁剪状态给轨道层级使用"""
        return {
            "left_trim": self.preview_left_trim_pos if self.left_trim_dragging else self.left_trim_pos,
            "right_trim": self.preview_right_trim_pos if self.right_trim_dragging else self.right_trim_pos,
            "left_trim_dragging": self.left_trim_dragging,
            "right_trim_dragging": self.right_trim_dragging,
            "geometry": self.geometry(),
            "height": self.height()
        }

    def paintEvent(self, event):
        """绘制缩略图或占位符"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        rect = self.rect()

        # 先清除整个背景，防止重影
        painter.fillRect(rect, QColor(10, 10, 10))  # 轨道背景色

        # 根据轨道类型设置背景色 - 现代化主题
        track = self.timeline.tracks[self.track_index]
        if track['type'] == 'video':
            bg_color = QColor(40, 40, 50)  # 深蓝灰色
        else:
            bg_color = QColor(35, 35, 45)   # 深紫灰色

        # 如果有video_processor且是视频文件，尝试显示缩略图
        if (self.video_processor and
            isinstance(self.media_item, dict) and
            track['type'] == 'video' and
            self.width() > 80):

            if self.draw_video_thumbnails(painter, rect):
                # 启用裁剪预览，显示灰色遮罩
                self.draw_trim_preview(painter, rect)
                self.draw_text_overlay(painter, rect)
                # 游标绘制仍然由轨道层级统一处理，确保在最顶层
                return

        # 回退到单色显示
        painter.fillRect(rect, bg_color)
        painter.setPen(QPen(QColor(119, 119, 119), 1))
        painter.drawRect(rect.adjusted(0, 0, -1, -1))

        # 绘制文本
        if isinstance(self.media_item, dict):
            name = self.media_item.get('name', 'Unknown')
            is_placeholder = self.media_item.get('is_placeholder', False)

            # 如果是占位符，使用特殊样式
            if is_placeholder:
                # 占位符背景
                painter.fillRect(rect, QColor(60, 60, 80, 200))  # 半透明紫色
                painter.setPen(QPen(QColor(150, 150, 200), 2, Qt.PenStyle.DashLine))
                painter.drawRect(rect.adjusted(2, 2, -2, -2))

                # 占位符图标和文本
                painter.setPen(QColor(200, 200, 255))
                font = QFont("Arial", 12, QFont.Weight.Bold)
                painter.setFont(font)

                # 绘制大图标
                icon_rect = QRect(rect.x() + 10, rect.y() + 10, 40, 40)
                painter.drawText(icon_rect, Qt.AlignmentFlag.AlignCenter, "📁")

                # 绘制提示文本
                text_rect = QRect(rect.x() + 55, rect.y() + 10, rect.width() - 65, rect.height() - 20)
                placeholder_text = self.media_item.get('placeholder_text', '拖入视频文件')
                painter.drawText(text_rect, Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter, placeholder_text)

                return  # 占位符不需要继续绘制其他内容
        else:
            name = 'Media'

        text = name[:12] + "..." if len(name) > 12 else name
        painter.setPen(QColor(255, 255, 255))
        font = QFont("Arial", 10, QFont.Weight.Bold)
        painter.setFont(font)
        painter.drawText(rect, Qt.AlignmentFlag.AlignCenter, text)

        # 启用裁剪预览，显示灰色遮罩
        self.draw_trim_preview(painter, rect)

        # 游标绘制仍然由轨道层级统一处理，确保在最顶层

        # 结束绘制
        painter.end()

    def draw_video_thumbnails(self, painter, rect):
        """绘制视频缩略图 - 考虑裁剪偏移的帧选择逻辑"""
        try:
            if not isinstance(self.media_item, dict):
                return False

            file_path = self.media_item['file_path']

            # 检查是否是占位符文件
            if file_path.startswith('placeholder_') or self.media_item.get('is_placeholder', False):
                return False  # 占位符不绘制缩略图，让paintEvent处理

            # 检查文件是否存在
            if not os.path.exists(file_path):
                return False

            duration = self.media_item['duration']
            track_width = rect.width()

            # 获取裁剪偏移信息
            trim_start = self.media_item.get('trim_start', 0)

            # 核心逻辑：根据轨道宽度计算能放下多少帧
            thumb_count = max(1, track_width // self.thumbnail_width)

            # 如果轨道太窄，只显示1帧（中间帧）
            if track_width < self.thumbnail_width:
                thumb_count = 1

            # 正确的时间戳计算：基于裁剪后的时间轴，确保帧的相对位置不变
            timestamps = []
            if thumb_count == 1:
                # 只有一帧时，显示中间位置（基于裁剪后的时间轴）
                relative_timestamp = duration / 2  # 裁剪后视频的中间位置
                timestamp = trim_start + relative_timestamp  # 转换为原视频中的时间
                timestamps.append(timestamp)
            else:
                # 多帧时，严格按照时间轴比例分布（基于裁剪后的时间轴）
                for i in range(thumb_count):
                    # 简化公式：每个缩略图在其位置的中点取样
                    # i=0时: 取第一个缩略图位置的中点
                    # i=last时: 取最后一个缩略图位置的中点
                    progress = (i + 0.5) / thumb_count  # 每个缩略图的中心位置
                    relative_timestamp = progress * duration  # 相对于裁剪后视频的时间（从0开始）
                    timestamp = trim_start + relative_timestamp  # 转换为原视频中的时间
                    # 确保在合理范围内
                    timestamp = max(trim_start + 0.1, min(timestamp, trim_start + duration - 0.1))
                    timestamps.append(timestamp)

            # 获取视频原始尺寸，按比例计算缩略图尺寸
            if isinstance(self.media_item, dict) and self.video_processor:
                try:
                    # 获取视频原始尺寸
                    cap = cv2.VideoCapture(self.media_item['file_path'])
                    if cap.isOpened():
                        video_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        video_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        cap.release()

                        # 按原比例计算缩略图尺寸
                        aspect_ratio = video_width / video_height
                        thumb_width = self.thumbnail_width
                        thumb_height = int(thumb_width / aspect_ratio)

                        # 确保高度在合理范围内
                        max_height = rect.height() - 10
                        if thumb_height > max_height:
                            thumb_height = max_height
                            thumb_width = int(thumb_height * aspect_ratio)
                    else:
                        # 如果无法获取视频尺寸，使用默认比例
                        thumb_width = self.thumbnail_width
                        thumb_height = self.thumbnail_height
                except:
                    # 出错时使用默认尺寸
                    thumb_width = self.thumbnail_width
                    thumb_height = self.thumbnail_height
            else:
                # 默认尺寸
                thumb_width = self.thumbnail_width
                thumb_height = self.thumbnail_height

            # 生成缓存键 - 包含轨道宽度确保正确更新
            cache_key = f"{file_path}_{thumb_count}_{thumb_width}x{thumb_height}_{track_width}"

            # 异步加载缩略图
            self._current_timestamps = timestamps
            cache_key_base = f"{file_path}_{thumb_width}x{thumb_height}_{track_width}"

            # 检查是否需要重新加载
            if (not hasattr(self, '_last_cache_key') or
                self._last_cache_key != cache_key_base):
                self._last_cache_key = cache_key_base
                self._start_async_loading(timestamps, thumb_width, thumb_height)

            # 使用已加载的缩略图
            thumbnails = [self._loaded_thumbnails.get(i) for i in range(len(timestamps))]

            # 绘制缩略图 - 严格按照时间轴位置绘制
            if thumbnails:
                thumb_y = (rect.height() - thumb_height) // 2  # 垂直居中

                for i, thumbnail in enumerate(thumbnails):
                    if i >= len(timestamps):
                        break

                    # 根据时间戳计算每个缩略图的精确位置
                    timestamp = timestamps[i]
                    # 计算相对于裁剪后视频的时间进度 (0-1)
                    relative_timestamp = timestamp - trim_start
                    time_progress = relative_timestamp / duration

                    # 计算在轨道中的像素位置 - 关键修复：基于裁剪后的时间轴
                    # 裁剪后，轨道显示的是从trim_start开始的视频内容
                    # 所以缩略图位置应该基于裁剪后的时间轴计算，确保帧的相对位置不变
                    center_x = time_progress * track_width
                    thumb_x = center_x - thumb_width // 2  # 缩略图中心对齐时间点

                    # 确保缩略图不超出轨道边界
                    thumb_x = max(0, min(thumb_x, track_width - thumb_width))

                    # 绘制区域计算
                    draw_x = max(0, int(thumb_x))
                    draw_width = min(thumb_width, track_width - draw_x)

                    if draw_width > 0:
                        thumb_rect = QRect(draw_x, thumb_y, draw_width, thumb_height)

                        if thumbnail and hasattr(thumbnail, 'isNull') and not thumbnail.isNull():
                            # 如果缩略图被部分裁剪，相应调整源区域
                            if thumb_x < 0:
                                source_x = int(-thumb_x)
                                source_rect = QRect(source_x, 0, draw_width, thumb_height)
                                painter.drawPixmap(thumb_rect, thumbnail, source_rect)
                            else:
                                painter.drawPixmap(thumb_rect, thumbnail)
                        else:
                            # 如果缩略图为空或还在加载中，显示加载提示
                            if i not in self._loading_thumbnails:
                                painter.fillRect(thumb_rect, QColor(80, 80, 80))
                                painter.setPen(QColor(150, 150, 150))
                                painter.drawText(thumb_rect, Qt.AlignmentFlag.AlignCenter, "...")
                            else:
                                # 正在加载，显示略深的灰色
                                painter.fillRect(thumb_rect, QColor(60, 60, 60))

                        # 绘制边框
                        painter.setPen(QPen(QColor(80, 80, 80), 1))
                        painter.drawRect(thumb_rect.adjusted(0, 0, -1, -1))

                return True

        except Exception as e:
            print(f"绘制缩略图失败: {e}")

        return False

    def draw_text_overlay(self, painter, rect):
        """在缩略图上绘制文本叠加"""
        if isinstance(self.media_item, dict):
            name = self.media_item.get('name', 'Unknown')
            duration = self.media_item.get('duration', 0)
        else:
            name = 'Media'
            duration = 0

        # 绘制半透明背景
        overlay_rect = QRect(2, rect.height() - 16, rect.width() - 4, 14)
        painter.fillRect(overlay_rect, QColor(0, 0, 0, 160))

        # 绘制文本
        painter.setPen(QColor(255, 255, 255))
        font = QFont("Arial", 8, QFont.Weight.Bold)
        painter.setFont(font)

        text = f"{name[:8]}... {duration:.1f}s" if len(name) > 8 else f"{name} {duration:.1f}s"
        painter.drawText(overlay_rect, Qt.AlignmentFlag.AlignCenter, text)

    def draw_trim_preview(self, painter, rect):
        """绘制裁剪预览遮罩"""
        # 如果正在拖拽裁剪游标，显示预览效果
        if self.left_trim_dragging or self.right_trim_dragging:
            # 使用预览位置
            left_trim = self.preview_left_trim_pos
            right_trim = self.preview_right_trim_pos
        else:
            # 使用实际位置
            left_trim = self.left_trim_pos
            right_trim = self.right_trim_pos

        # 绘制左侧遮罩
        if left_trim > 0:
            mask_rect = QRect(0, 0, left_trim, rect.height())
            painter.fillRect(mask_rect, QColor(0, 0, 0, 120))

        # 绘制右侧遮罩
        if right_trim > 0:
            mask_rect = QRect(rect.width() - right_trim, 0, right_trim, rect.height())
            painter.fillRect(mask_rect, QColor(0, 0, 0, 120))

    def _start_async_loading(self, timestamps, thumb_width, thumb_height):
        """开始异步加载缩略图"""
        # 清除旧的加载状态
        self._loading_thumbnails.clear()
        self._loaded_thumbnails.clear()
        self._load_queue = []

        # 添加到加载队列
        for i, timestamp in enumerate(timestamps):
            self._load_queue.append((i, timestamp, thumb_width, thumb_height))

        # 开始加载
        if self._load_queue and not self._load_timer.isActive():
            self._load_timer.start(10)  # 10ms后开始加载

    def _load_next_thumbnail(self):
        """加载下一个缩略图"""
        if not self._load_queue:
            return

        index, timestamp, thumb_width, thumb_height = self._load_queue.pop(0)

        # 标记为正在加载
        self._loading_thumbnails[index] = timestamp

        try:
            # 生成缩略图
            thumbnail = self._generate_thumbnail(timestamp, thumb_width, thumb_height)
            if thumbnail:
                self._loaded_thumbnails[index] = thumbnail
                self.update()  # 触发重绘
        except Exception as e:
            print(f"加载缩略图失败: {e}")
        finally:
            # 移除加载标记
            if index in self._loading_thumbnails:
                del self._loading_thumbnails[index]

        # 继续加载下一个
        if self._load_queue:
            self._load_timer.start(50)  # 50ms后加载下一个

    def _generate_thumbnail(self, timestamp, width, height):
        """生成指定时间戳的缩略图"""
        if not self.video_processor or not isinstance(self.media_item, dict):
            return None

        file_path = self.media_item['file_path']
        if not os.path.exists(file_path):
            return None

        try:
            # 使用video_processor生成缩略图
            if hasattr(self.video_processor, 'get_frame_at_time'):
                frame = self.video_processor.get_frame_at_time(file_path, timestamp)
                if frame is not None:
                    # 转换为QPixmap
                    from PySide6.QtGui import QImage
                    height_f, width_f, channel = frame.shape
                    bytes_per_line = 3 * width_f
                    q_image = QImage(frame.data, width_f, height_f, bytes_per_line, QImage.Format.Format_RGB888)
                    pixmap = QPixmap.fromImage(q_image)
                    return pixmap.scaled(width, height, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
        except Exception as e:
            print(f"生成缩略图失败: {e}")

        return None

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        print(f"🔍 mousePressEvent: 鼠标位置={event.pos()}")
        print(f"   - 素材块位置: {self.pos()}")
        print(f"   - 素材块大小: {self.size()}")

        if event.button() == Qt.MouseButton.LeftButton:
            # 检查是否点击了裁剪游标
            print(f"   - 开始检测游标...")
            left_trim_hit = self.is_on_left_trim_handle(event.pos())
            right_trim_hit = self.is_on_right_trim_handle(event.pos())

            print(f"   - 左游标检测: {left_trim_hit}")
            print(f"   - 右游标检测: {right_trim_hit}")

            if left_trim_hit:
                self.left_trim_dragging = True
                self.trim_drag_start_pos = event.pos()
                # 保存当前裁剪状态到历史记录
                self.save_trim_state()
                print("✅ 开始左侧裁剪（预览模式）")
                return
            elif right_trim_hit:
                self.right_trim_dragging = True
                self.trim_drag_start_pos = event.pos()
                # 保存当前裁剪状态到历史记录
                self.save_trim_state()
                print("✅ 开始右侧裁剪（预览模式）")
                return

            print("   - 普通拖拽模式")
            # 普通拖拽
            self.drag_start_pos = event.pos()
            self.original_pos = self.pos()

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        # 处理裁剪游标拖拽 - 剪映风格预览模式
        if self.left_trim_dragging or self.right_trim_dragging:
            print(f"🔍 裁剪拖拽中: left={self.left_trim_dragging}, right={self.right_trim_dragging}")

            if self.trim_drag_start_pos:
                delta_x = event.pos().x() - self.trim_drag_start_pos.x()
                print(f"   - 拖拽偏移: delta_x={delta_x}")

                if self.left_trim_dragging:
                    # 左游标：从左边裁剪，支持负值（扩展）
                    max_extend = self.media_item.get('trim_start', 0) * self.timeline.pixels_per_second if isinstance(self.media_item, dict) else 0
                    old_preview = self.preview_left_trim_pos
                    self.preview_left_trim_pos = max(-max_extend, min(delta_x, self.width() - self.preview_right_trim_pos - 20))
                    print(f"   - 左游标预览: {old_preview} -> {self.preview_left_trim_pos} (max_extend={max_extend})")

                elif self.right_trim_dragging:
                    # 右游标：从右边裁剪，支持负值（扩展）
                    max_extend = self.media_item.get('trim_end', 0) * self.timeline.pixels_per_second if isinstance(self.media_item, dict) else 0
                    old_preview = self.preview_right_trim_pos
                    self.preview_right_trim_pos = max(-max_extend, min(-delta_x, self.width() - self.preview_left_trim_pos - 20))
                    print(f"   - 右游标预览: {old_preview} -> {self.preview_right_trim_pos} (max_extend={max_extend})")

                # 实时更新显示（只更新预览，不修改实际数据）
                self.update()

                # 🔧 修复：更新专门的游标绘制层
                # 这样可以保证拖拽时游标实时更新，显示层级正确
                parent = self.parent()
                if parent and hasattr(parent, 'update_trim_handles'):
                    try:
                        parent.update_trim_handles()
                    except:
                        pass  # 忽略调用错误

                # 实时预览裁剪信息
                if isinstance(self.media_item, dict):
                    total_width = self.width()
                    current_duration = self.media_item['duration']
                    left_trim_time = (self.preview_left_trim_pos / total_width) * current_duration
                    right_trim_time = (self.preview_right_trim_pos / total_width) * current_duration
                    new_duration = current_duration - left_trim_time - right_trim_time
                    print(f"   - 裁剪预览: 左裁={left_trim_time:.2f}s, 右裁={right_trim_time:.2f}s, 新时长={new_duration:.2f}s")
            return

        # 更新鼠标样式
        if not (event.buttons() & Qt.MouseButton.LeftButton):
            self.update_cursor(event.pos())
            return

        if not self.drag_start_pos:
            return

        distance = (event.pos() - self.drag_start_pos).manhattanLength()

        if distance >= 5 and not self.dragging:
            self.dragging = True
            self.setStyleSheet("QWidget { border: 2px solid #4CAF50; }")
            # 显示所有轨道的拖拽指示器
            self.show_all_drag_indicators()

            # 🔧 优化：拖动时禁用绘制，提高性能
            self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, True)
            self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)

        if self.dragging:
            delta = event.pos() - self.drag_start_pos
            new_pos = self.original_pos + delta
            new_x = max(0, new_pos.x())  # 允许移动到x=0，对应时间轴0秒
            new_y = max(-50, new_pos.y())

            # 🔧 优化：减少频繁的move调用，使用更高效的方式
            current_pos = self.pos()
            if abs(current_pos.x() - new_x) > 1 or abs(current_pos.y() - new_y) > 1:
                # 只在位置变化超过1像素时才移动，减少重影
                self.move(new_x, new_y)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        # 处理裁剪游标释放 - 剪映风格应用模式
        if self.left_trim_dragging or self.right_trim_dragging:
            # 应用预览的裁剪到实际数据
            if isinstance(self.media_item, dict):
                self.apply_preview_trim()

            # 重置拖拽状态
            self.left_trim_dragging = False
            self.right_trim_dragging = False
            self.trim_drag_start_pos = None

            print("✅ 裁剪完成，已应用到实际数据")
            return

        # 处理普通拖拽释放
        if self.dragging:
            self.dragging = False
            self.setStyleSheet("")  # 清除拖拽样式

            # 恢复正常绘制
            self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, False)
            self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, False)

            # 隐藏所有轨道的拖拽指示器
            self.hide_all_drag_indicators()

            # 更新媒体项的时间位置
            if isinstance(self.media_item, dict) and hasattr(self.timeline, 'pixels_per_second'):
                new_time = self.x() / self.timeline.pixels_per_second

                # 应用磁性吸附
                if hasattr(self.timeline, 'apply_snap') and callable(self.timeline.apply_snap):
                    snapped_time = self.timeline.apply_snap(new_time)
                    new_time = snapped_time

                # 🔧 新功能：轨道内拖动时也要进行防重叠检测
                timeline = self.timeline
                while timeline and not hasattr(timeline, 'find_non_overlapping_position'):
                    timeline = timeline.parent()

                if timeline and hasattr(timeline, 'find_non_overlapping_position'):
                    # 找到当前素材所在的轨道
                    current_track = None
                    for track in timeline.tracks:
                        if self.media_item in track['media_files']:
                            current_track = track
                            break

                    if current_track:
                        # 临时移除当前素材，避免与自己重叠检测
                        current_track['media_files'].remove(self.media_item)

                        # 检查新位置是否重叠
                        duration = self.media_item.get('duration', 0)
                        final_position = timeline.find_non_overlapping_position(current_track, new_time, duration)

                        # 重新添加素材到轨道
                        current_track['media_files'].append(self.media_item)

                        # 更新素材位置
                        self.media_item['start_time'] = final_position

                        # 更新素材块的显示位置
                        final_x = int(final_position * self.timeline.pixels_per_second)
                        self.move(final_x, self.y())

                        print(f"媒体块移动到时间: {new_time:.2f}s -> {final_position:.2f}s (防重叠)")

                        # 更新轨道显示
                        if hasattr(timeline, 'update_track_display'):
                            track_index = timeline.tracks.index(current_track)
                            timeline.update_track_display(track_index)

                        # 更新总时长
                        if hasattr(timeline, 'update_total_duration'):
                            timeline.update_total_duration()
                    else:
                        # 如果找不到轨道，使用原来的逻辑
                        self.media_item['start_time'] = new_time
                        final_x = int(new_time * self.timeline.pixels_per_second)
                        self.move(final_x, self.y())
                        print(f"媒体块移动到时间: {new_time:.2f}s")
                else:
                    # 如果没有防重叠功能，使用原来的逻辑
                    self.media_item['start_time'] = new_time
                    final_x = int(new_time * self.timeline.pixels_per_second)
                    self.move(final_x, self.y())
                    print(f"媒体块移动到时间: {new_time:.2f}s")

        self.drag_start_pos = None
        self.original_pos = None

    def is_on_left_trim_handle(self, pos):
        """检查是否在左侧裁剪手柄上"""
        return pos.x() <= self.trim_handle_width

    def is_on_right_trim_handle(self, pos):
        """检查是否在右侧裁剪手柄上"""
        return pos.x() >= self.width() - self.trim_handle_width

    def update_cursor(self, pos):
        """更新鼠标光标"""
        if self.is_on_left_trim_handle(pos) or self.is_on_right_trim_handle(pos):
            self.setCursor(Qt.CursorShape.SizeHorCursor)
        else:
            self.setCursor(Qt.CursorShape.ArrowCursor)

    def save_trim_state(self):
        """保存当前裁剪状态到历史记录"""
        state = {
            'left_trim_pos': self.left_trim_pos,
            'right_trim_pos': self.right_trim_pos
        }
        self.trim_history.append(state)
        self.current_trim_index = len(self.trim_history) - 1

    def apply_preview_trim(self):
        """应用预览的裁剪到实际数据"""
        # 将预览位置应用到实际位置
        self.left_trim_pos = self.preview_left_trim_pos
        self.right_trim_pos = self.preview_right_trim_pos

        # 重置预览位置
        self.preview_left_trim_pos = 0
        self.preview_right_trim_pos = 0

        # 更新媒体项的裁剪信息
        if isinstance(self.media_item, dict):
            total_width = self.width()
            current_duration = self.media_item['duration']

            # 计算裁剪时间
            left_trim_time = (self.left_trim_pos / total_width) * current_duration
            right_trim_time = (self.right_trim_pos / total_width) * current_duration

            # 更新媒体项数据
            self.media_item['trim_start'] = self.media_item.get('trim_start', 0) + left_trim_time
            self.media_item['trim_end'] = self.media_item.get('trim_end', 0) + right_trim_time
            self.media_item['duration'] = current_duration - left_trim_time - right_trim_time

            print(f"应用裁剪: trim_start={self.media_item['trim_start']:.2f}s, trim_end={self.media_item['trim_end']:.2f}s, new_duration={self.media_item['duration']:.2f}s")

    def show_all_drag_indicators(self):
        """显示所有轨道的拖拽指示器"""
        # 向上查找MultiTrackTimeline
        timeline = self.timeline
        if hasattr(timeline, 'show_all_drag_indicators'):
            timeline.show_all_drag_indicators()

    def hide_all_drag_indicators(self):
        """隐藏所有轨道的拖拽指示器"""
        # 向上查找MultiTrackTimeline
        timeline = self.timeline
        if hasattr(timeline, 'hide_all_drag_indicators'):
            timeline.hide_all_drag_indicators()

    def resizeEvent(self, event):
        """调整大小事件"""
        super().resizeEvent(event)
        # 重新计算缩略图
        if not self.is_placeholder and hasattr(self, '_current_timestamps'):
            self._start_async_loading(self._current_timestamps, self.thumbnail_width, self.thumbnail_height)
